<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <script type="module" crossorigin src="./assets/remote-agent-home-BH1eva8L.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CQKp6jSN.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-D-hN7xfd.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-DhtTPDph.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-CWrSWzOd.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-ESlFPUk1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-D-fvrWAT.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/Content-D7Q35t53.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment--NM_J2iY.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-DOHETl9Q.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/utils-C8gPzElB.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-CMtlLYew.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-CVSFyrPR.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-KjDsYzQv.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-ZPisEIAt.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CB1sTE6C.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-CEPjk4z2.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DhUYorpN.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-Be7obQsv.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/index-DL-lqibn.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BuBr3xQZ.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-Dvw-pMXw.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-CuWF5Lfd.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
