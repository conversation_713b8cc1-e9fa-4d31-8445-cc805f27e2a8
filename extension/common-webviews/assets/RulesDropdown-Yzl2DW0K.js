import{S as J,i as K,s as W,A as k,e as y,q as S,t as u,r as C,u as f,h as w,n as R,B as Y,C as M,D as B,E as m,F as d,G as N,I as g,J as I,K as A,L as j,M as E}from"./SpinnerAugment-CQKp6jSN.js";import{e as z}from"./BaseButton-ESlFPUk1.js";import{D as x}from"./index-DvKVcjj3.js";import{B as F}from"./ButtonAugment-CAn8LxGl.js";import{C as _}from"./lodash-8faY21Ia.js";import{T as H}from"./TextTooltipAugment--NM_J2iY.js";import{A as O}from"./types-DwxhLPcD.js";function D(s,n,t){const e=s.slice();return e[15]=n[t],e}function P(s){let n,t,e,r;function o(a){s[11](a)}function i(a){s[12](a)}let $={$$slots:{default:[te]},$$scope:{ctx:s}};return s[3]!==void 0&&($.requestClose=s[3]),s[2]!==void 0&&($.focusedIndex=s[2]),n=new x.Root({props:$}),M.push(()=>B(n,"requestClose",o)),M.push(()=>B(n,"focusedIndex",i)),{c(){m(n.$$.fragment)},m(a,l){d(n,a,l),r=!0},p(a,l){const c={};262193&l&&(c.$$scope={dirty:l,ctx:a}),!t&&8&l&&(t=!0,c.requestClose=a[3],N(()=>t=!1)),!e&&4&l&&(e=!0,c.focusedIndex=a[2],N(()=>e=!1)),n.$set(c)},i(a){r||(f(n.$$.fragment,a),r=!0)},o(a){u(n.$$.fragment,a),r=!1},d(a){g(n,a)}}}function Q(s){let n,t;return n=new H({props:{content:"Workspace guidelines are always applied",$$slots:{default:[re]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment)},m(e,r){d(n,e,r),t=!0},p(e,r){const o={};262144&r&&(o.$$scope={dirty:r,ctx:e}),n.$set(o)},i(e){t||(f(n.$$.fragment,e),t=!0)},o(e){u(n.$$.fragment,e),t=!1},d(e){g(n,e)}}}function U(s){let n,t=s[0]?"Always":"Manual";return{c(){n=A(t)},m(e,r){y(e,n,r)},p(e,r){1&r&&t!==(t=e[0]?"Always":"Manual")&&E(n,t)},d(e){e&&w(n)}}}function V(s){let n,t;return n=new _({props:{slot:"iconRight"}}),{c(){m(n.$$.fragment)},m(e,r){d(n,e,r),t=!0},p:R,i(e){t||(f(n.$$.fragment,e),t=!0)},o(e){u(n.$$.fragment,e),t=!1},d(e){g(n,e)}}}function X(s){let n,t;return n=new F({props:{color:s[0]?"accent":"neutral",size:1,$$slots:{iconRight:[V],default:[U]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment)},m(e,r){d(n,e,r),t=!0},p(e,r){const o={};1&r&&(o.color=e[0]?"accent":"neutral"),262145&r&&(o.$$scope={dirty:r,ctx:e}),n.$set(o)},i(e){t||(f(n.$$.fragment,e),t=!0)},o(e){u(n.$$.fragment,e),t=!1},d(e){g(n,e)}}}function Z(s){let n,t=s[15].label+"";return{c(){n=A(t)},m(e,r){y(e,n,r)},p:R,d(e){e&&w(n)}}}function L(s){let n,t;return n=new x.Item({props:{onSelect:function(){return s[10](s[15])},highlight:s[4].label===s[15].label,$$slots:{default:[Z]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment)},m(e,r){d(n,e,r),t=!0},p(e,r){s=e;const o={};16&r&&(o.highlight=s[4].label===s[15].label),262144&r&&(o.$$scope={dirty:r,ctx:s}),n.$set(o)},i(e){t||(f(n.$$.fragment,e),t=!0)},o(e){u(n.$$.fragment,e),t=!1},d(e){g(n,e)}}}function T(s){let n,t,e,r;return n=new x.Separator({}),e=new x.Label({props:{$$slots:{default:[ee]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment),t=I(),m(e.$$.fragment)},m(o,i){d(n,o,i),y(o,t,i),d(e,o,i),r=!0},p(o,i){const $={};262192&i&&($.$$scope={dirty:i,ctx:o}),e.$set($)},i(o){r||(f(n.$$.fragment,o),f(e.$$.fragment,o),r=!0)},o(o){u(n.$$.fragment,o),u(e.$$.fragment,o),r=!1},d(o){o&&w(t),g(n,o),g(e,o)}}}function ee(s){let n,t=(s[5]!==void 0?s[6][s[5]].description:s[4].description)+"";return{c(){n=A(t)},m(e,r){y(e,n,r)},p(e,r){48&r&&t!==(t=(e[5]!==void 0?e[6][e[5]].description:e[4].description)+"")&&E(n,t)},d(e){e&&w(n)}}}function ne(s){let n,t,e,r=z(s[6]),o=[];for(let a=0;a<r.length;a+=1)o[a]=L(D(s,r,a));const i=a=>u(o[a],1,1,()=>{o[a]=null});let $=(s[5]!==void 0||s[4])&&T(s);return{c(){for(let a=0;a<o.length;a+=1)o[a].c();n=I(),$&&$.c(),t=k()},m(a,l){for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(a,l);y(a,n,l),$&&$.m(a,l),y(a,t,l),e=!0},p(a,l){if(208&l){let c;for(r=z(a[6]),c=0;c<r.length;c+=1){const h=D(a,r,c);o[c]?(o[c].p(h,l),f(o[c],1)):(o[c]=L(h),o[c].c(),f(o[c],1),o[c].m(n.parentNode,n))}for(S(),c=r.length;c<o.length;c+=1)i(c);C()}a[5]!==void 0||a[4]?$?($.p(a,l),48&l&&f($,1)):($=T(a),$.c(),f($,1),$.m(t.parentNode,t)):$&&(S(),u($,1,1,()=>{$=null}),C())},i(a){if(!e){for(let l=0;l<r.length;l+=1)f(o[l]);f($),e=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)u(o[l]);u($),e=!1},d(a){a&&(w(n),w(t)),j(o,a),$&&$.d(a)}}}function te(s){let n,t,e,r;return n=new x.Trigger({props:{$$slots:{default:[X]},$$scope:{ctx:s}}}),e=new x.Content({props:{side:"bottom",align:"start",$$slots:{default:[ne]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment),t=I(),m(e.$$.fragment)},m(o,i){d(n,o,i),y(o,t,i),d(e,o,i),r=!0},p(o,i){const $={};262145&i&&($.$$scope={dirty:i,ctx:o}),n.$set($);const a={};262192&i&&(a.$$scope={dirty:i,ctx:o}),e.$set(a)},i(o){r||(f(n.$$.fragment,o),f(e.$$.fragment,o),r=!0)},o(o){u(n.$$.fragment,o),u(e.$$.fragment,o),r=!1},d(o){o&&w(t),g(n,o),g(e,o)}}}function oe(s){let n;return{c(){n=A("Always")},m(t,e){y(t,n,e)},d(t){t&&w(n)}}}function re(s){let n,t;return n=new F({props:{color:"accent",size:1,disabled:!0,$$slots:{default:[oe]},$$scope:{ctx:s}}}),{c(){m(n.$$.fragment)},m(e,r){d(n,e,r),t=!0},p(e,r){const o={};262144&r&&(o.$$scope={dirty:r,ctx:e}),n.$set(o)},i(e){t||(f(n.$$.fragment,e),t=!0)},o(e){u(n.$$.fragment,e),t=!1},d(e){g(n,e)}}}function se(s){let n,t,e,r,o;const i=[Q,P],$=[];function a(l,c){return 2&c&&(n=null),n==null&&(n=!!l[8](l[1])),n?0:1}return t=a(s,-1),e=$[t]=i[t](s),{c(){e.c(),r=k()},m(l,c){$[t].m(l,c),y(l,r,c),o=!0},p(l,[c]){let h=t;t=a(l,c),t===h?$[t].p(l,c):(S(),u($[h],1,1,()=>{$[h]=null}),C(),e=$[t],e?e.p(l,c):(e=$[t]=i[t](l),e.c()),f(e,1),e.m(r.parentNode,r))},i(l){o||(f(e),o=!0)},o(l){u(e),o=!1},d(l){l&&w(r),$[t].d(l)}}}function ae(s,n,t){let e,r,o=R,i=()=>(o(),o=Y(b,p=>t(5,r=p)),b);s.$$.on_destroy.push(()=>o());let{path:$}=n,{onSave:a}=n,{alwaysApply:l}=n;const c={label:"Always",description:"Rules will always be followed, though individual context will still be respected."},h={label:"Manual",description:"Rules will only be followed when manually triggered. You can trigger them from the prompt by @ tagging the active file."},G=[c,h];let b;i();let v=()=>{};function q(p){t(0,l=p.label==="Always"),a(l),v()}return s.$$set=p=>{"path"in p&&t(1,$=p.path),"onSave"in p&&t(9,a=p.onSave),"alwaysApply"in p&&t(0,l=p.alwaysApply)},s.$$.update=()=>{1&s.$$.dirty&&t(4,e=l?c:h)},[l,$,b,v,e,r,G,q,function(p){return p===O},a,p=>q(p),function(p){v=p,t(3,v)},function(p){b=p,i(t(2,b))}]}class me extends J{constructor(n){super(),K(this,n,ae,se,W,{path:1,onSave:9,alwaysApply:0})}}export{me as R};
