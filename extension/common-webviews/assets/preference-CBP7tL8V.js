import{S as K,i as Q,s as T,W as f,J as b,c,Y as F,e as R,f as r,a8 as E,n as j,h as S,aa as $e,K as se,M as ie,a7 as ue,ad as ge,E as L,F as O,q as re,t as D,r as ce,u as B,I as P,ao as ve,a6 as be,C as ee,D as te,G as ne,A as ye}from"./SpinnerAugment-CQKp6jSN.js";import{h as H,W as J}from"./BaseButton-ESlFPUk1.js";import{aq as le}from"./AugmentMessage-DZ4G-ao3.js";import{C as we,S as ke}from"./folder-D9ce_3EI.js";import{M as Ce}from"./TextTooltipAugment--NM_J2iY.js";import{s as xe}from"./check-DlU29TPV.js";import{M as qe}from"./index-csTQmXPq.js";import"./CalloutAugment-ZPisEIAt.js";import"./Content-D7Q35t53.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-ChzPb9WB.js";import"./types-DwxhLPcD.js";import"./chat-types-DOHETl9Q.js";import"./file-paths-BcSg4gks.js";import"./diff-utils-Dvc7ppQm.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C4SxYn1J.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-Bb7oiaOR.js";import"./await_block-p2Uc7RoJ.js";import"./CollapseButtonAugment--cD032dy.js";import"./IconButtonAugment-D-fvrWAT.js";import"./ButtonAugment-CAn8LxGl.js";import"./github-CwfQWdpa.js";import"./index-DvKVcjj3.js";import"./CardAugment-KjDsYzQv.js";import"./MaterialIcon-CprIOK2c.js";import"./CopyButton-BT3AalMT.js";import"./magnifying-glass-CXSTSDWT.js";import"./ellipsis-DA5Ek1es.js";import"./IconFilePath-QXTl3raU.js";import"./LanguageIcon-CBQUAmpN.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-BI7GEaM4.js";import"./lodash-8faY21Ia.js";import"./mcp-logo-DrfFqzDb.js";import"./terminal-CB1sTE6C.js";import"./pen-to-square-CIi2Dx1U.js";import"./utils-C8gPzElB.js";import"./types-DDm27S8B.js";import"./augment-logo-DhUYorpN.js";import"./types-CGlLNakm.js";import"./isObjectLike-BYlJR0wA.js";import"./TextAreaAugment-b9B2aQlO.js";import"./ra-diff-ops-model-CRIDwIDf.js";function pe(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function Ae(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m,M,q=s[1]&&pe(s);return{c(){t=f("div"),q&&q.c(),n=b(),e=f("div"),i=f("button"),i.textContent="A",o=b(),l=f("button"),l.textContent="A",p=b(),h=f("button"),h.textContent="A",d=b(),u=f("button"),u.textContent="=",A=b(),y=f("button"),y.textContent="B",$=b(),x=f("button"),x.textContent="B",k=b(),w=f("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),F(i,"highlighted",s[0]==="A3"),c(l,"type","button"),c(l,"class","button medium svelte-1894wv4"),F(l,"highlighted",s[0]==="A2"),c(h,"type","button"),c(h,"class","button small svelte-1894wv4"),F(h,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),F(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),F(y,"highlighted",s[0]==="B1"),c(x,"type","button"),c(x,"class","button medium svelte-1894wv4"),F(x,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),F(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,I){R(g,t,I),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,o),r(e,l),r(e,p),r(e,h),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,x),r(e,k),r(e,w),m||(M=[E(i,"click",s[3]),E(l,"click",s[4]),E(h,"click",s[5]),E(u,"click",s[6]),E(y,"click",s[7]),E(x,"click",s[8]),E(w,"click",s[9])],m=!0)},p(g,[I]){g[1]?q?q.p(g,I):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&I&&F(i,"highlighted",g[0]==="A3"),1&I&&F(l,"highlighted",g[0]==="A2"),1&I&&F(h,"highlighted",g[0]==="A1"),1&I&&F(u,"highlighted",g[0]==="="),1&I&&F(y,"highlighted",g[0]==="B1"),1&I&&F(x,"highlighted",g[0]==="B2"),1&I&&F(w,"highlighted",g[0]==="B3")},i:j,o:j,d(g){g&&S(t),q&&q.d(),m=!1,$e(M)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function o(l){n(0,e=l)}return s.$$set=l=>{"selected"in l&&n(0,e=l.selected),"question"in l&&n(1,i=l.question)},[e,i,o,()=>o("A3"),()=>o("A2"),()=>o("A1"),()=>o("="),()=>o("B1"),()=>o("B2"),()=>o("B3")]}class ae extends K{constructor(t){super(),Q(this,t,Be,Ae,T,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function De(s){let t,n,e,i,o,l=s[1]&&de(s);return{c(){t=f("div"),l&&l.c(),n=b(),e=f("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,h){R(p,t,h),l&&l.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(o=E(e,"input",s[3]),i=!0)},p(p,[h]){p[1]?l?l.p(p,h):(l=de(p),l.c(),l.m(t,n)):l&&(l.d(1),l=null),4&h&&c(e,"placeholder",p[2]),1&h&&ue(e,p[0])},i:j,o:j,d(p){p&&S(t),l&&l.d(),i=!1,o()}}}function Me(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:o=""}=t;return s.$$set=l=>{"value"in l&&n(0,e=l.value),"question"in l&&n(1,i=l.question),"placeholder"in l&&n(2,o=l.placeholder)},[e,i,o,function(){e=this.value,n(0,e)}]}class Ie extends K{constructor(t){super(),Q(this,t,Me,De,T,{value:0,question:1,placeholder:2})}}function Re(s){let t,n,e,i;return{c(){t=f("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(o,l){R(o,t,l),r(t,n),e||(i=E(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(o,[l]){s=o,1&l&&ie(n,s[0])},i:j,o:j,d(o){o&&S(t),e=!1,i()}}}function Se(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=o=>{"label"in o&&n(0,e=o.label),"onClick"in o&&n(1,i=o.onClick)},[e,i]}class We extends K{constructor(t){super(),Q(this,t,Se,Re,T,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=f("div"),n=se(s[1])},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function _e(s){let t,n,e,i,o,l,p,h,d=s[1]&&me(s);return{c(){t=f("div"),d&&d.c(),n=b(),e=f("label"),i=f("input"),o=b(),l=f("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(l,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){R(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,o),r(e,l),p||(h=E(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:j,o:j,d(u){u&&S(t),d&&d.d(),p=!1,h()}}}function Fe(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=o=>{"isChecked"in o&&n(0,e=o.isChecked),"question"in o&&n(1,i=o.question)},[e,i,function(){e=this.checked,n(0,e)}]}class ze extends K{constructor(t){super(),Q(this,t,Fe,_e,T,{isChecked:0,question:1})}}function Ee(s){let t;return{c(){t=f("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){R(n,t,e)},p:j,i:j,o:j,d(n){n&&S(t)}}}function Le(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m;function M(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",M));let I={question:"Which response follows your instruction better?"};function Y(a){s[14](a)}s[3]!==void 0&&(I.selected=s[3]),i=new ae({props:I}),ee.push(()=>te(i,"selected",g));let U={question:"Which response is better overall?"};function W(a){s[15](a)}s[1]!==void 0&&(U.selected=s[1]),p=new ae({props:U}),ee.push(()=>te(p,"selected",Y));let _={question:s[9]};function N(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new ze({props:_}),ee.push(()=>te(u,"isChecked",W));let V={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(V.value=s[4]),$=new Ie({props:V}),ee.push(()=>te($,"value",N)),w=new We({props:{label:"Submit",onClick:s[10]}}),{c(){L(t.$$.fragment),e=b(),L(i.$$.fragment),l=b(),L(p.$$.fragment),d=b(),L(u.$$.fragment),y=b(),L($.$$.fragment),k=b(),L(w.$$.fragment)},m(a,C){O(t,a,C),R(a,e,C),O(i,a,C),R(a,l,C),O(p,a,C),R(a,d,C),O(u,a,C),R(a,y,C),O($,a,C),R(a,k,C),O(w,a,C),m=!0},p(a,C){const v={};!n&&4&C&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const z={};!o&&8&C&&(o=!0,z.selected=a[3],ne(()=>o=!1)),i.$set(z);const X={};!h&&2&C&&(h=!0,X.selected=a[1],ne(()=>h=!1)),p.$set(X);const G={};512&C&&(G.question=a[9]),!A&&32&C&&(A=!0,G.isChecked=a[5],ne(()=>A=!1)),u.$set(G);const Z={};!x&&16&C&&(x=!0,Z.value=a[4],ne(()=>x=!1)),$.$set(Z)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){D(t.$$.fragment,a),D(i.$$.fragment,a),D(p.$$.fragment,a),D(u.$$.fragment,a),D($.$$.fragment,a),D(w.$$.fragment,a),m=!1},d(a){a&&(S(e),S(l),S(d),S(y),S(k)),P(t,a),P(i,a),P(p,a),P(u,a),P($,a),P(w,a)}}}function Oe(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m,M,q,g,I,Y,U,W,_,N;o=new le({props:{markdown:s[0].data.a.message}}),$=new le({props:{markdown:s[8]}}),g=new le({props:{markdown:s[7]}});const V=[Le,Ee],a=[];function C(v,z){return v[6]?0:1}return W=C(s),_=a[W]=V[W](s),{c(){t=f("main"),n=f("div"),e=f("h1"),e.textContent="Input message",i=b(),L(o.$$.fragment),l=b(),p=f("hr"),h=b(),d=f("div"),u=f("div"),A=f("h1"),A.textContent="Option A",y=b(),L($.$$.fragment),x=b(),k=f("div"),w=b(),m=f("div"),M=f("h1"),M.textContent="Option B",q=b(),L(g.$$.fragment),I=b(),Y=f("hr"),U=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(M,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(Y,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,z){R(v,t,z),r(t,n),r(n,e),r(n,i),O(o,n,null),r(n,l),r(n,p),r(n,h),r(n,d),r(d,u),r(u,A),r(u,y),O($,u,null),r(d,x),r(d,k),r(d,w),r(d,m),r(m,M),r(m,q),O(g,m,null),r(n,I),r(n,Y),r(n,U),a[W].m(n,null),N=!0},p(v,[z]){const X={};1&z&&(X.markdown=v[0].data.a.message),o.$set(X);const G={};256&z&&(G.markdown=v[8]),$.$set(G);const Z={};128&z&&(Z.markdown=v[7]),g.$set(Z);let oe=W;W=C(v),W===oe?a[W].p(v,z):(re(),D(a[oe],1,1,()=>{a[oe]=null}),ce(),_=a[W],_?_.p(v,z):(_=a[W]=V[W](v),_.c()),B(_,1),_.m(n,null))},i(v){N||(B(o.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),N=!0)},o(v){D(o.$$.fragment,v),D($.$$.fragment,v),D(g.$$.fragment,v),D(_),N=!1},d(v){v&&S(t),P(o),P($),P(g),a[W].d()}}}function Pe(s,t,n){let e,i,o,{inputData:l}=t;const p=ve();let h=new we(new Ce(H),H,new ke);xe(h);let d=null,u=null,A=null,y=null,$="",x=!1,k={a:null,b:null},w=l.data.a.response.length>0&&l.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const M=m.data;M.type===J.chatModelReply?(M.stream==="A"?n(11,k.a=M.data.text,k):M.stream==="B"&&n(11,k.b=M.data.text,k),n(11,k)):M.type===J.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,l=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:l.data.a.response),2049&s.$$.dirty&&n(7,o=k.b!==null?k.b:l.data.b.response),1&s.$$.dirty&&n(6,w=l.data.a.response.length>0&&l.data.b.response.length>0)},[l,y,d,u,$,x,w,o,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:x,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){x=m,n(5,x)},function(m){$=m,n(4,$)}]}class je extends K{constructor(t){super(),Q(this,t,Pe,Oe,T,{inputData:0})}}function fe(s){let t,n,e=s[0].type==="Chat"&&he(s);return{c(){e&&e.c(),t=ye()},m(i,o){e&&e.m(i,o),R(i,t,o),n=!0},p(i,o){i[0].type==="Chat"?e?(e.p(i,o),1&o&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d(i)}}}function he(s){let t,n;return t=new je({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){L(t.$$.fragment)},m(e,i){O(t,e,i),n=!0},p(e,i){const o={};1&i&&(o.inputData=e[0]),t.$set(o)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){D(t.$$.fragment,e),n=!1},d(e){P(t,e)}}}function He(s){let t,n,e=s[0]&&fe(s);return{c(){t=f("main"),e&&e.c()},m(i,o){R(i,t,o),e&&e.m(t,null),n=!0},p(i,o){i[0]?e?(e.p(i,o),1&o&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d()}}}function Ne(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[He]},$$scope:{ctx:s}}}),{c(){L(t.$$.fragment)},m(o,l){O(t,o,l),n=!0,e||(i=E(window,"message",s[1]),e=!0)},p(o,[l]){const p={};17&l&&(p.$$scope={dirty:l,ctx:o}),t.$set(p)},i(o){n||(B(t.$$.fragment,o),n=!0)},o(o){D(t.$$.fragment,o),n=!1},d(o){P(t,o),e=!1,i()}}}function Ge(s,t,n){let e;return H.postMessage({type:J.preferencePanelLoaded}),[e,function(i){const o=i.data;o.type===J.preferenceInit&&n(0,e=o.data)},function(i){const o=i.detail;H.postMessage({type:J.preferenceResultMessage,data:o})},function(i){H.postMessage({type:J.preferenceNotify,data:i.detail})}]}class Je extends K{constructor(t){super(),Q(this,t,Ge,Ne,T,{})}}(async function(){H&&H.initialize&&await H.initialize(),new Je({target:document.getElementById("app")})})();
