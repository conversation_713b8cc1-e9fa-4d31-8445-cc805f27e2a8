import{O as r}from"./SpinnerAugment-CQKp6jSN.js";var b=typeof r=="object"&&r&&r.Object===Object&&r,v=b,j=typeof self=="object"&&self&&self.Object===Object&&self,s=v||j||Function("return this")(),n=s.Symbol,c=n,i=Object.prototype,p=i.hasOwnProperty,y=i.toString,e=c?c.toStringTag:void 0,O=function(t){var o=p.call(t,e),l=t[e];try{t[e]=void 0;var u=!0}catch{}var f=y.call(t);return u&&(o?t[e]=l:delete t[e]),f},d=Object.prototype.toString,g=O,S=function(t){return d.call(t)},a=n?n.toStringTag:void 0,T=function(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":a&&a in Object(t)?g(t):S(t)},w=function(t){var o=typeof t;return t!=null&&(o=="object"||o=="function")},x=function(t){return t!=null&&typeof t=="object"};export{s as _,T as a,w as b,n as c,b as d,x as i};
