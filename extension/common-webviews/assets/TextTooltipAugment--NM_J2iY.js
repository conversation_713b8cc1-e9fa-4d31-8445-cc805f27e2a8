var S=Object.defineProperty;var j=(i,t,s)=>t in i?S(i,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[t]=s;var F=(i,t,s)=>j(i,typeof t!="symbol"?t+"":t,s);import{W as B}from"./BaseButton-ESlFPUk1.js";import{A as k}from"./IconButtonAugment-D-fvrWAT.js";import{S as G,i as H,s as L,E as h,F as x,u as $,t as d,I as C,_ as Q,J as U,A as V,e as f,q as b,r as z,h as g,C as X,$ as I,a0 as J,a1 as K,a2 as N,W as Y,ac as m,T as Z,K as tt,M as st}from"./SpinnerAugment-CQKp6jSN.js";import{R as nt,a as et,C as ot}from"./Content-D7Q35t53.js";class Ct extends k{constructor(s){super(n=>{this._host.postMessage(n)});F(this,"_consumers",[]);this._host=s,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(s){this._host.postMessage(s)}registerConsumer(s){this._consumers.push(s)}onMessageFromExtension(s){s.data.type!==B.asyncWrapper&&this._consumers.forEach(n=>{n.handleMessageFromExtension(s)})}}const M={Root:nt,Trigger:et,Content:ot},it=i=>({}),T=i=>({});function rt(i){let t;const s=i[19].default,n=I(s,i,i[21],null);return{c(){n&&n.c()},m(e,o){n&&n.m(e,o),t=!0},p(e,o){n&&n.p&&(!t||2097152&o)&&J(n,s,e,e[21],t?N(s,e[21],o,null):K(e[21]),null)},i(e){t||($(n,e),t=!0)},o(e){d(n,e),t=!1},d(e){n&&n.d(e)}}}function A(i){let t,s;return t=new M.Content({props:{side:i[6],align:i[11],$$slots:{default:[pt]},$$scope:{ctx:i}}}),{c(){h(t.$$.fragment)},m(n,e){x(t,n,e),s=!0},p(n,e){const o={};64&e&&(o.side=n[6]),2048&e&&(o.align=n[11]),2162703&e&&(o.$$scope={dirty:e,ctx:n}),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){d(t.$$.fragment,n),s=!1},d(n){C(t,n)}}}function at(i){let t,s;return t=new Z({props:{size:1,class:"tooltip-text",$$slots:{default:[lt]},$$scope:{ctx:i}}}),{c(){h(t.$$.fragment)},m(n,e){x(t,n,e),s=!0},p(n,e){const o={};2097153&e&&(o.$$scope={dirty:e,ctx:n}),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){d(t.$$.fragment,n),s=!1},d(n){C(t,n)}}}function ct(i){let t;const s=i[19].content,n=I(s,i,i[21],T);return{c(){n&&n.c()},m(e,o){n&&n.m(e,o),t=!0},p(e,o){n&&n.p&&(!t||2097152&o)&&J(n,s,e,e[21],t?N(s,e[21],o,it):K(e[21]),T)},i(e){t||($(n,e),t=!0)},o(e){d(n,e),t=!1},d(e){n&&n.d(e)}}}function lt(i){let t;return{c(){t=tt(i[0])},m(s,n){f(s,t,n)},p(s,n){1&n&&st(t,s[0])},d(s){s&&g(t)}}}function pt(i){let t,s,n,e;const o=[ct,at],r=[];function l(c,p){return c[16].content?0:1}return s=l(i),n=r[s]=o[s](i),{c(){t=Y("div"),n.c(),m(t,"width",i[1]),m(t,"min-width",i[2]),m(t,"max-width",i[3])},m(c,p){f(c,t,p),r[s].m(t,null),e=!0},p(c,p){let u=s;s=l(c),s===u?r[s].p(c,p):(b(),d(r[u],1,1,()=>{r[u]=null}),z(),n=r[s],n?n.p(c,p):(n=r[s]=o[s](c),n.c()),$(n,1),n.m(t,null)),2&p&&m(t,"width",c[1]),4&p&&m(t,"min-width",c[2]),8&p&&m(t,"max-width",c[3])},i(c){e||($(n),e=!0)},o(c){d(n),e=!1},d(c){c&&g(t),r[s].d()}}}function $t(i){let t,s,n,e;t=new M.Trigger({props:{referenceClientRect:i[14],class:i[12],$$slots:{default:[rt]},$$scope:{ctx:i}}});let o=(i[0]||i[16].content)&&A(i);return{c(){h(t.$$.fragment),s=U(),o&&o.c(),n=V()},m(r,l){x(t,r,l),f(r,s,l),o&&o.m(r,l),f(r,n,l),e=!0},p(r,l){const c={};16384&l&&(c.referenceClientRect=r[14]),4096&l&&(c.class=r[12]),2097152&l&&(c.$$scope={dirty:l,ctx:r}),t.$set(c),r[0]||r[16].content?o?(o.p(r,l),65537&l&&$(o,1)):(o=A(r),o.c(),$(o,1),o.m(n.parentNode,n)):o&&(b(),d(o,1,1,()=>{o=null}),z())},i(r){e||($(t.$$.fragment,r),$(o),e=!0)},o(r){d(t.$$.fragment,r),d(o),e=!1},d(r){r&&(g(s),g(n)),C(t,r),o&&o.d(r)}}}function dt(i){let t,s,n={delayDurationMs:i[4],onOpenChange:i[13],triggerOn:i[5],nested:i[7],hasPointerEvents:i[8],offset:i[9],open:i[10],tippyTheme:"default text-tooltip-augment",$$slots:{default:[$t]},$$scope:{ctx:i}};return t=new M.Root({props:n}),i[20](t),{c(){h(t.$$.fragment)},m(e,o){x(t,e,o),s=!0},p(e,[o]){const r={};16&o&&(r.delayDurationMs=e[4]),8192&o&&(r.onOpenChange=e[13]),32&o&&(r.triggerOn=e[5]),128&o&&(r.nested=e[7]),256&o&&(r.hasPointerEvents=e[8]),512&o&&(r.offset=e[9]),1024&o&&(r.open=e[10]),2185295&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){s||($(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){i[20](null),C(t,e)}}}function ut(i,t,s){let{$$slots:n={},$$scope:e}=t;const o=Q(n);let r,{content:l}=t,{width:c}=t,{minWidth:p}=t,{maxWidth:u="250px"}=t,{delayDurationMs:O}=t,{triggerOn:w}=t,{side:y="top"}=t,{nested:E}=t,{hasPointerEvents:W}=t,{offset:R}=t,{open:_}=t,{align:q="center"}=t,{class:v=""}=t,{onOpenChange:D}=t,{referenceClientRect:P}=t;return i.$$set=a=>{"content"in a&&s(0,l=a.content),"width"in a&&s(1,c=a.width),"minWidth"in a&&s(2,p=a.minWidth),"maxWidth"in a&&s(3,u=a.maxWidth),"delayDurationMs"in a&&s(4,O=a.delayDurationMs),"triggerOn"in a&&s(5,w=a.triggerOn),"side"in a&&s(6,y=a.side),"nested"in a&&s(7,E=a.nested),"hasPointerEvents"in a&&s(8,W=a.hasPointerEvents),"offset"in a&&s(9,R=a.offset),"open"in a&&s(10,_=a.open),"align"in a&&s(11,q=a.align),"class"in a&&s(12,v=a.class),"onOpenChange"in a&&s(13,D=a.onOpenChange),"referenceClientRect"in a&&s(14,P=a.referenceClientRect),"$$scope"in a&&s(21,e=a.$$scope)},[l,c,p,u,O,w,y,E,W,R,_,q,v,D,P,r,o,()=>r==null?void 0:r.requestOpen(),()=>r==null?void 0:r.requestClose(),n,function(a){X[a?"unshift":"push"](()=>{r=a,s(15,r)})},e]}class Mt extends G{constructor(t){super(),H(this,t,ut,dt,L,{content:0,width:1,minWidth:2,maxWidth:3,delayDurationMs:4,triggerOn:5,side:6,nested:7,hasPointerEvents:8,offset:9,open:10,align:11,class:12,onOpenChange:13,referenceClientRect:14,requestOpen:17,requestClose:18})}get requestOpen(){return this.$$.ctx[17]}get requestClose(){return this.$$.ctx[18]}}export{Ct as M,Mt as T};
