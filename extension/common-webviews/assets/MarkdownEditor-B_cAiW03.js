import{S as P,i as Q,s as R,$ as U,a as q,C,D as G,W as b,J as V,E as D,c as F,e as T,f as _,F as K,a0 as X,a1 as Y,a2 as tt,g as et,a4 as st,G as J,u as v,q as M,t as x,r as W,h as k,I as j,Z,a5 as nt,j as ot,T as L,K as N,M as at}from"./SpinnerAugment-CQKp6jSN.js";import"./BaseButton-ESlFPUk1.js";import"./index-DvKVcjj3.js";import{T as ct}from"./TextAreaAugment-b9B2aQlO.js";import{l as rt}from"./lodash-8faY21Ia.js";const it=o=>({}),A=o=>({});function B(o){let t,e;return t=new L({props:{size:1,weight:"light",color:"error",$$slots:{default:[lt]},$$scope:{ctx:o}}}),{c(){D(t.$$.fragment)},m(a,d){K(t,a,d),e=!0},p(a,d){const l={};4194432&d&&(l.$$scope={dirty:d,ctx:a}),t.$set(l)},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){x(t.$$.fragment,a),e=!1},d(a){j(t,a)}}}function lt(o){let t;return{c(){t=N(o[7])},m(e,a){T(e,t,a)},p(e,a){128&a&&at(t,e[7])},d(e){e&&k(t)}}}function H(o){let t,e;return t=new L({props:{size:1,weight:"light",color:"success",$$slots:{default:[ut]},$$scope:{ctx:o}}}),{c(){D(t.$$.fragment)},m(a,d){K(t,a,d),e=!0},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){x(t.$$.fragment,a),e=!1},d(a){j(t,a)}}}function ut(o){let t;return{c(){t=N("Saved")},m(e,a){T(e,t,a)},d(e){e&&k(t)}}}function dt(o){let t,e,a,d,l,z,I,h,$,w,f;const E=o[17].header,u=U(E,o,o[22],A),p=[{variant:o[2]},{size:o[3]},{color:o[4]},{resize:o[5]},{placeholder:"Enter markdown content..."},o[11]];function S(s){o[18](s)}function g(s){o[19](s)}let m={};for(let s=0;s<p.length;s+=1)m=q(m,p[s]);o[0]!==void 0&&(m.textInput=o[0]),o[1]!==void 0&&(m.value=o[1]),l=new ct({props:m}),C.push(()=>G(l,"textInput",S)),C.push(()=>G(l,"value",g)),l.$on("select",o[9]),l.$on("mouseup",o[9]),l.$on("keyup",o[20]),l.$on("input",o[10]),l.$on("keydown",o[21]);let c=!!o[7]&&B(o),i=o[6]&&H(o);return{c(){t=b("div"),e=b("div"),u&&u.c(),a=V(),d=b("div"),D(l.$$.fragment),h=V(),$=b("div"),c&&c.c(),w=V(),i&&i.c(),F(e,"class","c-markdown-editor__header svelte-1xrqe9m"),F(d,"class","c-markdown-editor__content svelte-1xrqe9m"),F(t,"class","l-markdown-editor svelte-1xrqe9m"),F($,"class","c-markdown-editor__status svelte-1xrqe9m")},m(s,r){T(s,t,r),_(t,e),u&&u.m(e,null),_(t,a),_(t,d),K(l,d,null),T(s,h,r),T(s,$,r),c&&c.m($,null),_($,w),i&&i.m($,null),f=!0},p(s,[r]){u&&u.p&&(!f||4194304&r)&&X(u,E,s,s[22],f?tt(E,s[22],r,it):Y(s[22]),A);const y=2108&r?et(p,[4&r&&{variant:s[2]},8&r&&{size:s[3]},16&r&&{color:s[4]},32&r&&{resize:s[5]},p[4],2048&r&&st(s[11])]):{};!z&&1&r&&(z=!0,y.textInput=s[0],J(()=>z=!1)),!I&&2&r&&(I=!0,y.value=s[1],J(()=>I=!1)),l.$set(y),s[7]?c?(c.p(s,r),128&r&&v(c,1)):(c=B(s),c.c(),v(c,1),c.m($,w)):c&&(M(),x(c,1,1,()=>{c=null}),W()),s[6]?i?64&r&&v(i,1):(i=H(s),i.c(),v(i,1),i.m($,null)):i&&(M(),x(i,1,1,()=>{i=null}),W())},i(s){f||(v(u,s),v(l.$$.fragment,s),v(c),v(i),f=!0)},o(s){x(u,s),x(l.$$.fragment,s),x(c),x(i),f=!1},d(s){s&&(k(t),k(h),k($)),u&&u.d(s),j(l),c&&c.d(),i&&i.d()}}}function $t(o,t,e){const a=["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"];let d,l,z=Z(t,a),{$$slots:I={},$$scope:h}=t,{variant:$="surface"}=t,{size:w=2}=t,{color:f}=t,{resize:E="none"}=t,{textInput:u}=t,{value:p=""}=t,{selectedText:S=""}=t,{selectionStart:g=0}=t,{selectionEnd:m=0}=t,{saveFunction:c}=t,{debounceValue:i=2500}=t,s=!1;const r=async()=>{try{c(),e(6,s=!0),clearTimeout(d),d=setTimeout(()=>{e(6,s=!1)},1500)}catch(n){e(7,l=n instanceof Error?n.message:String(n))}};function y(){u&&(e(13,g=u.selectionStart),e(14,m=u.selectionEnd),e(12,S=g!==m?p.substring(g,m):""))}const O=rt.debounce(r,i);return nt(()=>{r()}),o.$$set=n=>{t=q(q({},t),ot(n)),e(11,z=Z(t,a)),"variant"in n&&e(2,$=n.variant),"size"in n&&e(3,w=n.size),"color"in n&&e(4,f=n.color),"resize"in n&&e(5,E=n.resize),"textInput"in n&&e(0,u=n.textInput),"value"in n&&e(1,p=n.value),"selectedText"in n&&e(12,S=n.selectedText),"selectionStart"in n&&e(13,g=n.selectionStart),"selectionEnd"in n&&e(14,m=n.selectionEnd),"saveFunction"in n&&e(15,c=n.saveFunction),"debounceValue"in n&&e(16,i=n.debounceValue),"$$scope"in n&&e(22,h=n.$$scope)},[u,p,$,w,f,E,s,l,r,y,O,z,S,g,m,c,i,I,function(n){u=n,e(0,u)},function(n){p=n,e(1,p)},()=>{y()},n=>{(n.key==="Escape"||(n.metaKey||n.ctrlKey)&&n.key==="s")&&(n.preventDefault(),r())},h]}class gt extends P{constructor(t){super(),Q(this,t,$t,dt,R,{variant:2,size:3,color:4,resize:5,textInput:0,value:1,selectedText:12,selectionStart:13,selectionEnd:14,saveFunction:15,debounceValue:16})}}export{gt as M};
