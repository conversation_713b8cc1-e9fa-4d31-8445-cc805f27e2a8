import{S as I,i as K,s as S,a as N,b as D,H,w as J,x as Q,y as U,h as y,d as G,z as V,g as X,n as x,j as P,C as Y,D as Z,E as g,F as w,G as _,u as f,t as d,I as h,T as W,W as b,J as B,c as z,e as v,f as L,K as T,a8 as tt,r as et,ah as E,a6 as st,q as nt,N as R}from"./SpinnerAugment-CQKp6jSN.js";import"./design-system-init-D-hN7xfd.js";import{h as M,W as q}from"./BaseButton-ESlFPUk1.js";import{B as ot}from"./ButtonAugment-CAn8LxGl.js";import{O as at}from"./OpenFileButton-DG4CgSDg.js";import{C as rt,E as ct}from"./chat-flags-model-BhsWla-l.js";import{M as j,T as lt}from"./TextTooltipAugment--NM_J2iY.js";import{M as pt}from"./MarkdownEditor-B_cAiW03.js";import{R as O}from"./types-DwxhLPcD.js";import{R as it}from"./RulesDropdown-Yzl2DW0K.js";import"./check-DlU29TPV.js";import"./types-DDm27S8B.js";import"./chat-types-DOHETl9Q.js";import"./index-C4SxYn1J.js";import"./arrow-up-right-from-square-ChzPb9WB.js";import"./file-paths-BcSg4gks.js";import"./utils-C8gPzElB.js";import"./ra-diff-ops-model-CRIDwIDf.js";import"./types-CGlLNakm.js";import"./Content-D7Q35t53.js";import"./globals-D0QH3NT1.js";import"./IconButtonAugment-D-fvrWAT.js";import"./index-DvKVcjj3.js";import"./CardAugment-KjDsYzQv.js";import"./TextAreaAugment-b9B2aQlO.js";import"./lodash-8faY21Ia.js";function $t(n){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},n[0]],a={};for(let o=0;o<e.length;o+=1)a=N(a,e[o]);return{c(){t=D("svg"),s=new H(!0),this.h()},l(o){t=J(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Q(t);s=U(r,!0),r.forEach(y),this.h()},h(){s.a=null,G(t,a)},m(o,r){V(o,t,r),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M15 239c-9.4 9.4-9.4 24.6 0 33.9L207 465c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9L65.9 256 241 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0z"/>',t)},p(o,[r]){G(t,a=X(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&r&&o[0]]))},i:x,o:x,d(o){o&&y(t)}}}function mt(n,t,s){return n.$$set=e=>{s(0,t=N(N({},t),P(e)))},[t=P(t)]}class ut extends I{constructor(t){super(),K(this,t,mt,$t,S,{})}}function ft(n){let t;return{c(){t=T("Back")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function dt(n){let t,s;return t=new ut({props:{slot:"iconLeft"}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p:x,i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function gt(n){let t,s;return t=new ot({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$slots:{iconLeft:[dt],default:[ft]},$$scope:{ctx:n}}}),t.$on("click",n[5]),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};2048&a&&(o.$$scope={dirty:a,ctx:e}),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function wt(n){let t;return{c(){t=T("Rules Trigger Mode:")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function ht(n){let t;return{c(){t=T("Open file")},m(s,e){v(s,t,e)},d(s){s&&y(t)}}}function yt(n){let t,s;return t=new W({props:{slot:"text",size:1,$$slots:{default:[ht]},$$scope:{ctx:n}}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};2048&a&&(o.$$scope={dirty:a,ctx:e}),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function xt(n){let t,s,e,a,o,r,i,p,u,c,$;return e=new lt({props:{content:"Navigate back to all Rules & Guidelines",$$slots:{default:[gt]},$$scope:{ctx:n}}}),r=new W({props:{size:1,$$slots:{default:[wt]},$$scope:{ctx:n}}}),p=new it({props:{path:n[1],onSave:n[4],alwaysApply:n[2]}}),c=new at({props:{size:1,path:n[1],onOpenLocalFile:n[6],$$slots:{text:[yt]},$$scope:{ctx:n}}}),{c(){t=b("div"),s=b("div"),g(e.$$.fragment),a=B(),o=b("div"),g(r.$$.fragment),i=B(),g(p.$$.fragment),u=B(),g(c.$$.fragment),z(o,"class","c-dropdown-with-label svelte-13rq98s"),z(s,"class","l-file-controls-left svelte-13rq98s"),z(t,"class","l-file-controls svelte-13rq98s"),z(t,"slot","header")},m(l,m){v(l,t,m),L(t,s),w(e,s,null),L(s,a),L(s,o),w(r,o,null),L(o,i),w(p,o,null),L(t,u),w(c,t,null),$=!0},p(l,m){const F={};2048&m&&(F.$$scope={dirty:m,ctx:l}),e.$set(F);const C={};2048&m&&(C.$$scope={dirty:m,ctx:l}),r.$set(C);const A={};2&m&&(A.path=l[1]),4&m&&(A.alwaysApply=l[2]),p.$set(A);const k={};2&m&&(k.path=l[1]),2&m&&(k.onOpenLocalFile=l[6]),2048&m&&(k.$$scope={dirty:m,ctx:l}),c.$set(k)},i(l){$||(f(e.$$.fragment,l),f(r.$$.fragment,l),f(p.$$.fragment,l),f(c.$$.fragment,l),$=!0)},o(l){d(e.$$.fragment,l),d(r.$$.fragment,l),d(p.$$.fragment,l),d(c.$$.fragment,l),$=!1},d(l){l&&y(t),h(e),h(r),h(p),h(c)}}}function vt(n){let t,s,e;function a(r){n[8](r)}let o={saveFunction:n[7],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[xt]},$$scope:{ctx:n}};return n[0]!==void 0&&(o.value=n[0]),t=new pt({props:o}),Y.push(()=>Z(t,"value",a)),{c(){g(t.$$.fragment)},m(r,i){w(t,r,i),e=!0},p(r,[i]){const p={};4&i&&(p.saveFunction=r[7]),2054&i&&(p.$$scope={dirty:i,ctx:r}),!s&&1&i&&(s=!0,p.value=r[0],_(()=>s=!1)),t.$set(p)},i(r){e||(f(t.$$.fragment,r),e=!0)},o(r){d(t.$$.fragment,r),e=!1},d(r){h(t,r)}}}function Ft(n,t,s){let{text:e}=t,{path:a}=t;const o=new j(M),r=new rt,i=new ct(M,o,r);let{alwaysApply:p}=t;const u=async c=>{const $=O.updateAlwaysApplyFrontmatterKey(e,c);i.saveFile({repoRoot:"",pathName:a,content:$})};return n.$$set=c=>{"text"in c&&s(0,e=c.text),"path"in c&&s(1,a=c.path),"alwaysApply"in c&&s(2,p=c.alwaysApply)},[e,a,p,i,u,()=>{M.postMessage({type:q.openSettingsPage,data:"guidelines"})},async()=>(i.openFile({repoRoot:"",pathName:a}),"success"),()=>u(p),function(c){e=c,s(0,e)}]}class At extends I{constructor(t){super(),K(this,t,Ft,vt,S,{text:0,path:1,alwaysApply:2})}}function Lt(n){let t;return{c(){t=b("div"),t.textContent="Loading..."},m(s,e){v(s,t,e)},p:x,i:x,o:x,d(s){s&&y(t)}}}function zt(n){let t,s;return t=new At({props:{text:n[0],path:n[1],alwaysApply:n[2]}}),{c(){g(t.$$.fragment)},m(e,a){w(t,e,a),s=!0},p(e,a){const o={};1&a&&(o.text=e[0]),2&a&&(o.path=e[1]),4&a&&(o.alwaysApply=e[2]),t.$set(o)},i(e){s||(f(t.$$.fragment,e),s=!0)},o(e){d(t.$$.fragment,e),s=!1},d(e){h(t,e)}}}function bt(n){let t,s,e,a,o,r;const i=[zt,Lt],p=[];function u(c,$){return c[0]!==null&&c[1]!==null?0:1}return s=u(n),e=p[s]=i[s](n),{c(){t=b("div"),e.c(),z(t,"class","c-rules-container svelte-1vbu0zh")},m(c,$){v(c,t,$),p[s].m(t,null),a=!0,o||(r=tt(window,"message",n[3].onMessageFromExtension),o=!0)},p(c,[$]){let l=s;s=u(c),s===l?p[s].p(c,$):(nt(),d(p[l],1,1,()=>{p[l]=null}),et(),e=p[s],e?e.p(c,$):(e=p[s]=i[s](c),e.c()),f(e,1),e.m(t,null))},i(c){a||(f(e),a=!0)},o(c){d(e),a=!1},d(c){c&&y(t),p[s].d(),o=!1,r()}}}function Mt(n,t,s){let e,a,o;const r=new j(M),i=R(null);E(n,i,$=>s(0,e=$));const p=R("");E(n,p,$=>s(1,a=$));const u=R(!0);E(n,u,$=>s(2,o=$));const c={handleMessageFromExtension($){const l=$.data;if(l&&l.type===q.loadFile&&l){const m=l.data.content;if(m!==void 0){const F=m.replace(/^\n+/,""),C=O.getAlwaysApplyFrontmatterKey(F),A=O.extractContent(F);u.set(C),i.set(A)}p.set(l.data.pathName)}return!0}};return st(()=>{r.registerConsumer(c),M.postMessage({type:q.rulesLoaded})}),[e,a,o,r,i,p,u]}new class extends I{constructor(n){super(),K(this,n,Mt,bt,S,{})}}({target:document.getElementById("app")});
