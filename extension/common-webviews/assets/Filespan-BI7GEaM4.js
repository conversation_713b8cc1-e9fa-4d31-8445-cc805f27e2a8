import{S as W,i as b,s as k,T as q,E as B,F as C,u as z,t as _,I as D,$ as j,W as v,J as x,K as w,c as g,a3 as y,e as E,f as m,a0 as I,a1 as F,a2 as J,M as S,h as A}from"./SpinnerAugment-CQKp6jSN.js";import{n as G,g as H,a as L}from"./file-paths-BcSg4gks.js";const N=e=>({}),K=e=>({}),O=e=>({}),M=e=>({});function T(e){let t,s,n;return{c(){t=v("div"),s=v("div"),n=w(e[3]),g(s,"class","c-filespan__dir-text svelte-juudge"),g(t,"class","c-filespan__dir svelte-juudge")},m(c,$){E(c,t,$),m(t,s),m(s,n)},p(c,$){8&$&&S(n,c[3])},d(c){c&&A(t)}}}function P(e){let t,s,n,c,$,h,d,o;const f=e[7].leftIcon,l=j(f,e,e[8],M);let i=!e[2]&&T(e);const p=e[7].rightIcon,r=j(p,e,e[8],K);return{c(){t=v("div"),l&&l.c(),s=x(),n=v("span"),c=w(e[4]),$=x(),i&&i.c(),h=x(),r&&r.c(),g(n,"class","c-filespan__filename svelte-juudge"),g(t,"class",d=y(`c-filespan ${e[0]}`)+" svelte-juudge")},m(a,u){E(a,t,u),l&&l.m(t,null),m(t,s),m(t,n),m(n,c),m(t,$),i&&i.m(t,null),m(t,h),r&&r.m(t,null),o=!0},p(a,u){l&&l.p&&(!o||256&u)&&I(l,f,a,a[8],o?J(f,a[8],u,O):F(a[8]),M),(!o||16&u)&&S(c,a[4]),a[2]?i&&(i.d(1),i=null):i?i.p(a,u):(i=T(a),i.c(),i.m(t,h)),r&&r.p&&(!o||256&u)&&I(r,p,a,a[8],o?J(p,a[8],u,N):F(a[8]),K),(!o||1&u&&d!==(d=y(`c-filespan ${a[0]}`)+" svelte-juudge"))&&g(t,"class",d)},i(a){o||(z(l,a),z(r,a),o=!0)},o(a){_(l,a),_(r,a),o=!1},d(a){a&&A(t),l&&l.d(a),i&&i.d(),r&&r.d(a)}}}function Q(e){let t,s;return t=new q({props:{size:e[1],$$slots:{default:[P]},$$scope:{ctx:e}}}),{c(){B(t.$$.fragment)},m(n,c){C(t,n,c),s=!0},p(n,[c]){const $={};2&c&&($.size=n[1]),285&c&&($.$$scope={dirty:c,ctx:n}),t.$set($)},i(n){s||(z(t.$$.fragment,n),s=!0)},o(n){_(t.$$.fragment,n),s=!1},d(n){D(t,n)}}}function R(e,t,s){let n,c,$,{$$slots:h={},$$scope:d}=t,{class:o=""}=t,{filepath:f}=t,{size:l=1}=t,{nopath:i=!1}=t;return e.$$set=p=>{"class"in p&&s(0,o=p.class),"filepath"in p&&s(5,f=p.filepath),"size"in p&&s(1,l=p.size),"nopath"in p&&s(2,i=p.nopath),"$$scope"in p&&s(8,d=p.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&s(6,n=G(f)),64&e.$$.dirty&&s(4,c=H(n)),64&e.$$.dirty&&s(3,$=L(n))},[o,l,i,$,c,f,n,h,d]}class X extends W{constructor(t){super(),b(this,t,R,Q,k,{class:0,filepath:5,size:1,nopath:2})}}export{X as F};
