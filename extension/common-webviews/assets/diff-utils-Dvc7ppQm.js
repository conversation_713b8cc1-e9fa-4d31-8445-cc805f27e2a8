var ln=Object.defineProperty;var tt=s=>{throw TypeError(s)};var an=(s,e,t)=>e in s?ln(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var T=(s,e,t)=>an(s,typeof e!="symbol"?e+"":e,t),cn=(s,e,t)=>e.has(s)||tt("Cannot "+t);var nt=(s,e,t)=>e.has(s)?tt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(s):e.set(s,t);var Fe=(s,e,t)=>(cn(s,e,"access private method"),t);import{S as B,i as y,s as b,A as Y,e as C,u as k,t as m,h as A,$ as O,a as Jt,W as w,az as Ce,a0 as j,a1 as N,a2 as q,g as Ut,q as G,r as K,L as pe,E as J,F as U,I as X,aA as rt,K as te,M as he,n as R,c as $,f as L,J as ue,aB as pn,aC as ut,a6 as hn,C as We,D as Dn,a8 as st,G as dn,aa as fn,ah as gn,ab as Fn,b as Ae}from"./SpinnerAugment-CQKp6jSN.js";import{e as W}from"./BaseButton-ESlFPUk1.js";import"./toggleHighContrast-CwIv4U26.js";import{S as kn,a as mn,g as $n}from"./index-csTQmXPq.js";function ot(...s){return"/"+s.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function it(s){return s.startsWith("/")||s.startsWith("#")}function Re(s){let e,t;const n=s[5].default,r=O(n,s,s[4],null);let u=[{id:s[1]}],o={};for(let i=0;i<u.length;i+=1)o=Jt(o,u[i]);return{c(){e=w(`h${s[0].depth}`),r&&r.c(),Ce(`h${s[0].depth}`)(e,o)},m(i,l){C(i,e,l),r&&r.m(e,null),t=!0},p(i,l){r&&r.p&&(!t||16&l)&&j(r,n,i,i[4],t?q(n,i[4],l,null):N(i[4]),null),Ce(`h${i[0].depth}`)(e,o=Ut(u,[(!t||2&l)&&{id:i[1]}]))},i(i){t||(k(r,i),t=!0)},o(i){m(r,i),t=!1},d(i){i&&A(e),r&&r.d(i)}}}function xn(s){let e,t,n=`h${s[0].depth}`,r=`h${s[0].depth}`&&Re(s);return{c(){r&&r.c(),e=Y()},m(u,o){r&&r.m(u,o),C(u,e,o),t=!0},p(u,[o]){`h${u[0].depth}`?n?b(n,`h${u[0].depth}`)?(r.d(1),r=Re(u),n=`h${u[0].depth}`,r.c(),r.m(e.parentNode,e)):r.p(u,o):(r=Re(u),n=`h${u[0].depth}`,r.c(),r.m(e.parentNode,e)):n&&(r.d(1),r=null,n=`h${u[0].depth}`)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function Cn(s,e,t){let{$$slots:n={},$$scope:r}=e,{token:u}=e,{options:o}=e,i;return s.$$set=l=>{"token"in l&&t(0,u=l.token),"options"in l&&t(2,o=l.options),"$$scope"in l&&t(4,r=l.$$scope)},s.$$.update=()=>{var l,a;5&s.$$.dirty&&t(1,(l=u.text,a=o.slugger,i=a.slug(l).replace(/--+/g,"-")))},[u,i,o,void 0,r,n]}class An extends B{constructor(e){super(),y(this,e,Cn,xn,b,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function En(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("blockquote"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function wn(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class bn extends B{constructor(e){super(),y(this,e,wn,En,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function lt(s,e,t){const n=s.slice();return n[3]=e[t],n}function at(s){let e,t,n=W(s[0]),r=[];for(let o=0;o<n.length;o+=1)r[o]=ct(lt(s,n,o));const u=o=>m(r[o],1,1,()=>{r[o]=null});return{c(){for(let o=0;o<r.length;o+=1)r[o].c();e=Y()},m(o,i){for(let l=0;l<r.length;l+=1)r[l]&&r[l].m(o,i);C(o,e,i),t=!0},p(o,i){if(7&i){let l;for(n=W(o[0]),l=0;l<n.length;l+=1){const a=lt(o,n,l);r[l]?(r[l].p(a,i),k(r[l],1)):(r[l]=ct(a),r[l].c(),k(r[l],1),r[l].m(e.parentNode,e))}for(G(),l=n.length;l<r.length;l+=1)u(l);K()}},i(o){if(!t){for(let i=0;i<n.length;i+=1)k(r[i]);t=!0}},o(o){r=r.filter(Boolean);for(let i=0;i<r.length;i+=1)m(r[i]);t=!1},d(o){o&&A(e),pe(r,o)}}}function ct(s){let e,t;return e=new Xt({props:{token:s[3],renderers:s[1],options:s[2]}}),{c(){J(e.$$.fragment)},m(n,r){U(e,n,r),t=!0},p(n,r){const u={};1&r&&(u.token=n[3]),2&r&&(u.renderers=n[1]),4&r&&(u.options=n[2]),e.$set(u)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){X(e,n)}}}function vn(s){let e,t,n=s[0]&&at(s);return{c(){n&&n.c(),e=Y()},m(r,u){n&&n.m(r,u),C(r,e,u),t=!0},p(r,[u]){r[0]?n?(n.p(r,u),1&u&&k(n,1)):(n=at(r),n.c(),k(n,1),n.m(e.parentNode,e)):n&&(G(),m(n,1,1,()=>{n=null}),K())},i(r){t||(k(n),t=!0)},o(r){m(n),t=!1},d(r){r&&A(e),n&&n.d(r)}}}function Bn(s,e,t){let{tokens:n}=e,{renderers:r}=e,{options:u}=e;return s.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,u=o.options)},[n,r,u]}class ze extends B{constructor(e){super(),y(this,e,Bn,vn,b,{tokens:0,renderers:1,options:2})}}function pt(s){let e,t,n;var r=s[1][s[0].type];function u(o,i){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Sn]},$$scope:{ctx:o}}}}return r&&(e=rt(r,u(s))),{c(){e&&J(e.$$.fragment),t=Y()},m(o,i){e&&U(e,o,i),C(o,t,i),n=!0},p(o,i){if(3&i&&r!==(r=o[1][o[0].type])){if(e){G();const l=e;m(l.$$.fragment,1,0,()=>{X(l,1)}),K()}r?(e=rt(r,u(o)),J(e.$$.fragment),k(e.$$.fragment,1),U(e,t.parentNode,t)):e=null}else if(r){const l={};1&i&&(l.token=o[0]),4&i&&(l.options=o[2]),2&i&&(l.renderers=o[1]),15&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)}},i(o){n||(e&&k(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&A(t),e&&X(e,o)}}}function yn(s){let e,t=s[0].raw+"";return{c(){e=te(t)},m(n,r){C(n,e,r)},p(n,r){1&r&&t!==(t=n[0].raw+"")&&he(e,t)},i:R,o:R,d(n){n&&A(e)}}}function zn(s){let e,t;return e=new ze({props:{tokens:s[0].tokens,renderers:s[1],options:s[2]}}),{c(){J(e.$$.fragment)},m(n,r){U(e,n,r),t=!0},p(n,r){const u={};1&r&&(u.tokens=n[0].tokens),2&r&&(u.renderers=n[1]),4&r&&(u.options=n[2]),e.$set(u)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){X(e,n)}}}function Sn(s){let e,t,n,r;const u=[zn,yn],o=[];function i(l,a){return"tokens"in l[0]&&l[0].tokens?0:1}return e=i(s),t=o[e]=u[e](s),{c(){t.c(),n=Y()},m(l,a){o[e].m(l,a),C(l,n,a),r=!0},p(l,a){let c=e;e=i(l),e===c?o[e].p(l,a):(G(),m(o[c],1,1,()=>{o[c]=null}),K(),t=o[e],t?t.p(l,a):(t=o[e]=u[e](l),t.c()),k(t,1),t.m(n.parentNode,n))},i(l){r||(k(t),r=!0)},o(l){m(t),r=!1},d(l){l&&A(n),o[e].d(l)}}}function Tn(s){let e,t,n=s[1][s[0].type]&&pt(s);return{c(){n&&n.c(),e=Y()},m(r,u){n&&n.m(r,u),C(r,e,u),t=!0},p(r,[u]){r[1][r[0].type]?n?(n.p(r,u),3&u&&k(n,1)):(n=pt(r),n.c(),k(n,1),n.m(e.parentNode,e)):n&&(G(),m(n,1,1,()=>{n=null}),K())},i(r){t||(k(n),t=!0)},o(r){m(n),t=!1},d(r){r&&A(e),n&&n.d(r)}}}function Rn(s,e,t){let{token:n}=e,{renderers:r}=e,{options:u}=e;return s.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,u=o.options)},[n,r,u]}class Xt extends B{constructor(e){super(),y(this,e,Rn,Tn,b,{token:0,renderers:1,options:2})}}function ht(s,e,t){const n=s.slice();return n[4]=e[t],n}function Dt(s){let e,t;return e=new Xt({props:{token:{...s[4]},options:s[1],renderers:s[2]}}),{c(){J(e.$$.fragment)},m(n,r){U(e,n,r),t=!0},p(n,r){const u={};1&r&&(u.token={...n[4]}),2&r&&(u.options=n[1]),4&r&&(u.renderers=n[2]),e.$set(u)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){X(e,n)}}}function Ie(s){let e,t,n,r=W(s[0].items),u=[];for(let a=0;a<r.length;a+=1)u[a]=Dt(ht(s,r,a));const o=a=>m(u[a],1,1,()=>{u[a]=null});let i=[{start:t=s[0].start||1}],l={};for(let a=0;a<i.length;a+=1)l=Jt(l,i[a]);return{c(){e=w(s[3]);for(let a=0;a<u.length;a+=1)u[a].c();Ce(s[3])(e,l)},m(a,c){C(a,e,c);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(e,null);n=!0},p(a,c){if(7&c){let p;for(r=W(a[0].items),p=0;p<r.length;p+=1){const D=ht(a,r,p);u[p]?(u[p].p(D,c),k(u[p],1)):(u[p]=Dt(D),u[p].c(),k(u[p],1),u[p].m(e,null))}for(G(),p=r.length;p<u.length;p+=1)o(p);K()}Ce(a[3])(e,l=Ut(i,[(!n||1&c&&t!==(t=a[0].start||1))&&{start:t}]))},i(a){if(!n){for(let c=0;c<r.length;c+=1)k(u[c]);n=!0}},o(a){u=u.filter(Boolean);for(let c=0;c<u.length;c+=1)m(u[c]);n=!1},d(a){a&&A(e),pe(u,a)}}}function In(s){let e,t=s[3],n=s[3]&&Ie(s);return{c(){n&&n.c(),e=Y()},m(r,u){n&&n.m(r,u),C(r,e,u)},p(r,[u]){r[3]?t?b(t,r[3])?(n.d(1),n=Ie(r),t=r[3],n.c(),n.m(e.parentNode,e)):n.p(r,u):(n=Ie(r),t=r[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=r[3])},i:R,o(r){m(n,r)},d(r){r&&A(e),n&&n.d(r)}}}function Ln(s,e,t){let n,{token:r}=e,{options:u}=e,{renderers:o}=e;return s.$$set=i=>{"token"in i&&t(0,r=i.token),"options"in i&&t(1,u=i.options),"renderers"in i&&t(2,o=i.renderers)},s.$$.update=()=>{1&s.$$.dirty&&t(3,n=r.ordered?"ol":"ul")},[r,u,o,n]}class Pn extends B{constructor(e){super(),y(this,e,Ln,In,b,{token:0,options:1,renderers:2})}}function _n(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("li"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function On(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class jn extends B{constructor(e){super(),y(this,e,On,_n,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Nn(s){let e;return{c(){e=w("br")},m(t,n){C(t,e,n)},p:R,i:R,o:R,d(t){t&&A(e)}}}function qn(s,e,t){return[void 0,void 0,void 0]}class Zn extends B{constructor(e){super(),y(this,e,qn,Nn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Hn(s){let e,t,n,r,u=s[0].text+"";return{c(){e=w("pre"),t=w("code"),n=te(u),$(t,"class",r=`lang-${s[0].lang}`)},m(o,i){C(o,e,i),L(e,t),L(t,n)},p(o,[i]){1&i&&u!==(u=o[0].text+"")&&he(n,u),1&i&&r!==(r=`lang-${o[0].lang}`)&&$(t,"class",r)},i:R,o:R,d(o){o&&A(e)}}}function Mn(s,e,t){let{token:n}=e;return s.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class Wn extends B{constructor(e){super(),y(this,e,Mn,Hn,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Qn(s){let e,t,n=s[0].raw.slice(1,s[0].raw.length-1)+"";return{c(){e=w("code"),t=te(n)},m(r,u){C(r,e,u),L(e,t)},p(r,[u]){1&u&&n!==(n=r[0].raw.slice(1,r[0].raw.length-1)+"")&&he(t,n)},i:R,o:R,d(r){r&&A(e)}}}function Vn(s,e,t){let{token:n}=e;return s.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class Jn extends B{constructor(e){super(),y(this,e,Vn,Qn,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function dt(s,e,t){const n=s.slice();return n[3]=e[t],n}function ft(s,e,t){const n=s.slice();return n[6]=e[t],n}function gt(s,e,t){const n=s.slice();return n[9]=e[t],n}function Ft(s){let e,t,n,r;return t=new ze({props:{tokens:s[9].tokens,options:s[1],renderers:s[2]}}),{c(){e=w("th"),J(t.$$.fragment),n=ue(),$(e,"scope","col")},m(u,o){C(u,e,o),U(t,e,null),L(e,n),r=!0},p(u,o){const i={};1&o&&(i.tokens=u[9].tokens),2&o&&(i.options=u[1]),4&o&&(i.renderers=u[2]),t.$set(i)},i(u){r||(k(t.$$.fragment,u),r=!0)},o(u){m(t.$$.fragment,u),r=!1},d(u){u&&A(e),X(t)}}}function kt(s){let e,t,n;return t=new ze({props:{tokens:s[6].tokens,options:s[1],renderers:s[2]}}),{c(){e=w("td"),J(t.$$.fragment)},m(r,u){C(r,e,u),U(t,e,null),n=!0},p(r,u){const o={};1&u&&(o.tokens=r[6].tokens),2&u&&(o.options=r[1]),4&u&&(o.renderers=r[2]),t.$set(o)},i(r){n||(k(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&A(e),X(t)}}}function mt(s){let e,t,n,r=W(s[3]),u=[];for(let i=0;i<r.length;i+=1)u[i]=kt(ft(s,r,i));const o=i=>m(u[i],1,1,()=>{u[i]=null});return{c(){e=w("tr");for(let i=0;i<u.length;i+=1)u[i].c();t=ue()},m(i,l){C(i,e,l);for(let a=0;a<u.length;a+=1)u[a]&&u[a].m(e,null);L(e,t),n=!0},p(i,l){if(7&l){let a;for(r=W(i[3]),a=0;a<r.length;a+=1){const c=ft(i,r,a);u[a]?(u[a].p(c,l),k(u[a],1)):(u[a]=kt(c),u[a].c(),k(u[a],1),u[a].m(e,t))}for(G(),a=r.length;a<u.length;a+=1)o(a);K()}},i(i){if(!n){for(let l=0;l<r.length;l+=1)k(u[l]);n=!0}},o(i){u=u.filter(Boolean);for(let l=0;l<u.length;l+=1)m(u[l]);n=!1},d(i){i&&A(e),pe(u,i)}}}function Un(s){let e,t,n,r,u,o,i=W(s[0].header),l=[];for(let h=0;h<i.length;h+=1)l[h]=Ft(gt(s,i,h));const a=h=>m(l[h],1,1,()=>{l[h]=null});let c=W(s[0].rows),p=[];for(let h=0;h<c.length;h+=1)p[h]=mt(dt(s,c,h));const D=h=>m(p[h],1,1,()=>{p[h]=null});return{c(){e=w("table"),t=w("thead"),n=w("tr");for(let h=0;h<l.length;h+=1)l[h].c();r=ue(),u=w("tbody");for(let h=0;h<p.length;h+=1)p[h].c()},m(h,f){C(h,e,f),L(e,t),L(t,n);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(n,null);L(e,r),L(e,u);for(let d=0;d<p.length;d+=1)p[d]&&p[d].m(u,null);o=!0},p(h,[f]){if(7&f){let d;for(i=W(h[0].header),d=0;d<i.length;d+=1){const x=gt(h,i,d);l[d]?(l[d].p(x,f),k(l[d],1)):(l[d]=Ft(x),l[d].c(),k(l[d],1),l[d].m(n,null))}for(G(),d=i.length;d<l.length;d+=1)a(d);K()}if(7&f){let d;for(c=W(h[0].rows),d=0;d<c.length;d+=1){const x=dt(h,c,d);p[d]?(p[d].p(x,f),k(p[d],1)):(p[d]=mt(x),p[d].c(),k(p[d],1),p[d].m(u,null))}for(G(),d=c.length;d<p.length;d+=1)D(d);K()}},i(h){if(!o){for(let f=0;f<i.length;f+=1)k(l[f]);for(let f=0;f<c.length;f+=1)k(p[f]);o=!0}},o(h){l=l.filter(Boolean);for(let f=0;f<l.length;f+=1)m(l[f]);p=p.filter(Boolean);for(let f=0;f<p.length;f+=1)m(p[f]);o=!1},d(h){h&&A(e),pe(l,h),pe(p,h)}}}function Xn(s,e,t){let{token:n}=e,{options:r}=e,{renderers:u}=e;return s.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,r=o.options),"renderers"in o&&t(2,u=o.renderers)},[n,r,u]}class Gn extends B{constructor(e){super(),y(this,e,Xn,Un,b,{token:0,options:1,renderers:2})}}function Kn(s){let e,t,n=s[0].text+"";return{c(){e=new pn(!1),t=Y(),e.a=t},m(r,u){e.m(n,r,u),C(r,t,u)},p(r,[u]){1&u&&n!==(n=r[0].text+"")&&e.p(n)},i:R,o:R,d(r){r&&(A(t),e.d())}}}function Yn(s,e,t){let{token:n}=e;return s.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class er extends B{constructor(e){super(),y(this,e,Yn,Kn,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function tr(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("p"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function nr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}let rr=class extends B{constructor(s){super(),y(this,s,nr,tr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function ur(s){let e,t,n,r;const u=s[4].default,o=O(u,s,s[3],null);return{c(){e=w("a"),o&&o.c(),$(e,"href",t=it(s[0].href)?ot(s[1].baseUrl,s[0].href):s[0].href),$(e,"title",n=s[0].title)},m(i,l){C(i,e,l),o&&o.m(e,null),r=!0},p(i,[l]){o&&o.p&&(!r||8&l)&&j(o,u,i,i[3],r?q(u,i[3],l,null):N(i[3]),null),(!r||3&l&&t!==(t=it(i[0].href)?ot(i[1].baseUrl,i[0].href):i[0].href))&&$(e,"href",t),(!r||1&l&&n!==(n=i[0].title))&&$(e,"title",n)},i(i){r||(k(o,i),r=!0)},o(i){m(o,i),r=!1},d(i){i&&A(e),o&&o.d(i)}}}function sr(s,e,t){let{$$slots:n={},$$scope:r}=e,{token:u}=e,{options:o}=e;return s.$$set=i=>{"token"in i&&t(0,u=i.token),"options"in i&&t(1,o=i.options),"$$scope"in i&&t(3,r=i.$$scope)},[u,o,void 0,r,n]}class or extends B{constructor(e){super(),y(this,e,sr,ur,b,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function ir(s){let e;const t=s[4].default,n=O(t,s,s[3],null);return{c(){n&&n.c()},m(r,u){n&&n.m(r,u),e=!0},p(r,[u]){n&&n.p&&(!e||8&u)&&j(n,t,r,r[3],e?q(t,r[3],u,null):N(r[3]),null)},i(r){e||(k(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function lr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class ar extends B{constructor(e){super(),y(this,e,lr,ir,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function cr(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("dfn"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function pr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class hr extends B{constructor(e){super(),y(this,e,pr,cr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Dr(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("del"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function dr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class fr extends B{constructor(e){super(),y(this,e,dr,Dr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function gr(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("em"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function Fr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class kr extends B{constructor(e){super(),y(this,e,Fr,gr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function mr(s){let e;return{c(){e=w("hr")},m(t,n){C(t,e,n)},p:R,i:R,o:R,d(t){t&&A(e)}}}function $r(s,e,t){return[void 0,void 0,void 0]}class xr extends B{constructor(e){super(),y(this,e,$r,mr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Cr(s){let e,t;const n=s[4].default,r=O(n,s,s[3],null);return{c(){e=w("strong"),r&&r.c()},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||8&o)&&j(r,n,u,u[3],t?q(n,u[3],o,null):N(u[3]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function Ar(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class Er extends B{constructor(e){super(),y(this,e,Ar,Cr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function wr(s){let e,t,n,r;return{c(){e=w("img"),ut(e.src,t=s[0].href)||$(e,"src",t),$(e,"title",n=s[0].title),$(e,"alt",r=s[0].text),$(e,"class","markdown-image svelte-z38cge")},m(u,o){C(u,e,o)},p(u,[o]){1&o&&!ut(e.src,t=u[0].href)&&$(e,"src",t),1&o&&n!==(n=u[0].title)&&$(e,"title",n),1&o&&r!==(r=u[0].text)&&$(e,"alt",r)},i:R,o:R,d(u){u&&A(e)}}}function br(s,e,t){let{token:n}=e;return s.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class vr extends B{constructor(e){super(),y(this,e,br,wr,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Br(s){let e;const t=s[4].default,n=O(t,s,s[3],null);return{c(){n&&n.c()},m(r,u){n&&n.m(r,u),e=!0},p(r,[u]){n&&n.p&&(!e||8&u)&&j(n,t,r,r[3],e?q(t,r[3],u,null):N(r[3]),null)},i(r){e||(k(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function yr(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(3,r=u.$$scope)},[void 0,void 0,void 0,r,n]}class $t extends B{constructor(e){super(),y(this,e,yr,Br,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function zr(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let re={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function xt(s){re=s}const Gt=/[&<>"']/,Sr=new RegExp(Gt.source,"g"),Kt=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Tr=new RegExp(Kt.source,"g"),Rr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ct=s=>Rr[s];function H(s,e){if(e){if(Gt.test(s))return s.replace(Sr,Ct)}else if(Kt.test(s))return s.replace(Tr,Ct);return s}const Ir=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Lr(s){return s.replace(Ir,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Pr=/(^|[^\[])\^/g;function S(s,e){let t=typeof s=="string"?s:s.source;e=e||"";const n={replace:(r,u)=>{let o=typeof u=="string"?u:u.source;return o=o.replace(Pr,"$1"),t=t.replace(r,o),n},getRegex:()=>new RegExp(t,e)};return n}function At(s){try{s=encodeURI(s).replace(/%25/g,"%")}catch{return null}return s}const ae={exec:()=>null};function Et(s,e){const t=s.replace(/\|/g,(r,u,o)=>{let i=!1,l=u;for(;--l>=0&&o[l]==="\\";)i=!i;return i?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function ke(s,e,t){const n=s.length;if(n===0)return"";let r=0;for(;r<n;){const u=s.charAt(n-r-1);if(u!==e||t){if(u===e||!t)break;r++}else r++}return s.slice(0,n-r)}function wt(s,e,t,n){const r=e.href,u=e.title?H(e.title):null,o=s[1].replace(/\\([\[\]])/g,"$1");if(s[0].charAt(0)!=="!"){n.state.inLink=!0;const i={type:"link",raw:t,href:r,title:u,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,i}return{type:"image",raw:t,href:r,title:u,text:H(o)}}class Ee{constructor(e){T(this,"options");T(this,"rules");T(this,"lexer");this.options=e||re}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:ke(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=function(u,o){const i=u.match(/^(\s+)(?:```)/);if(i===null)return o;const l=i[1];return o.split(`
`).map(a=>{const c=a.match(/^\s+/);if(c===null)return a;const[p]=c;return p.length>=l.length?a.slice(l.length):a}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const r=ke(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=ke(t[0].replace(/^ *>[ \t]?/gm,""),`
`),r=this.lexer.state.top;this.lexer.state.top=!0;const u=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:u,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,u={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let i="",l="",a=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;i=t[0],e=e.substring(i.length);let p=t[2].split(`
`,1)[0].replace(/^\t+/,F=>" ".repeat(3*F.length)),D=e.split(`
`,1)[0],h=0;this.options.pedantic?(h=2,l=p.trimStart()):(h=t[2].search(/[^ ]/),h=h>4?1:h,l=p.slice(h),h+=t[1].length);let f=!1;if(!p&&/^ *$/.test(D)&&(i+=D+`
`,e=e.substring(D.length+1),c=!0),!c){const F=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),_=new RegExp(`^ {0,${Math.min(3,h-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),E=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:\`\`\`|~~~)`),v=new RegExp(`^ {0,${Math.min(3,h-1)}}#`);for(;e;){const g=e.split(`
`,1)[0];if(D=g,this.options.pedantic&&(D=D.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),E.test(D)||v.test(D)||F.test(D)||_.test(e))break;if(D.search(/[^ ]/)>=h||!D.trim())l+=`
`+D.slice(h);else{if(f||p.search(/[^ ]/)>=4||E.test(p)||v.test(p)||_.test(p))break;l+=`
`+D}f||D.trim()||(f=!0),i+=g+`
`,e=e.substring(g.length+1),p=D.slice(h)}}u.loose||(a?u.loose=!0:/\n *\n *$/.test(i)&&(a=!0));let d,x=null;this.options.gfm&&(x=/^\[[ xX]\] /.exec(l),x&&(d=x[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),u.items.push({type:"list_item",raw:i,task:!!x,checked:d,loose:!1,text:l,tokens:[]}),u.raw+=i}u.items[u.items.length-1].raw=i.trimEnd(),u.items[u.items.length-1].text=l.trimEnd(),u.raw=u.raw.trimEnd();for(let c=0;c<u.items.length;c++)if(this.lexer.state.top=!1,u.items[c].tokens=this.lexer.blockTokens(u.items[c].text,[]),!u.loose){const p=u.items[c].tokens.filter(h=>h.type==="space"),D=p.length>0&&p.some(h=>/\n.*\n/.test(h.raw));u.loose=D}if(u.loose)for(let c=0;c<u.items.length;c++)u.items[c].loose=!0;return u}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",u=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:u}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=Et(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),u=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const i of r)/^ *-+: *$/.test(i)?o.align.push("right"):/^ *:-+: *$/.test(i)?o.align.push("center"):/^ *:-+ *$/.test(i)?o.align.push("left"):o.align.push(null);for(const i of n)o.header.push({text:i,tokens:this.lexer.inline(i)});for(const i of u)o.rows.push(Et(i,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:H(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=ke(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(i,l){if(i.indexOf(l[1])===-1)return-1;let a=0;for(let c=0;c<i.length;c++)if(i[c]==="\\")c++;else if(i[c]===l[0])a++;else if(i[c]===l[1]&&(a--,a<0))return c;return-1}(t[2],"()");if(o>-1){const i=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,i).trim(),t[3]=""}}let r=t[2],u="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);o&&(r=o[1],u=o[3])}else u=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(r=this.options.pedantic&&!/>$/.test(n)?r.slice(1):r.slice(1,-1)),wt(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:u&&u.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const r=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!r){const u=n[0].charAt(0);return{type:"text",raw:u,text:u}}return wt(n,r,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&!(r[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){const u=[...r[0]].length-1;let o,i,l=u,a=0;const c=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+u);(r=c.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(i=[...o].length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&u%3&&!((u+i)%3)){a+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+a);const p=[...r[0]][0].length,D=e.slice(0,u+r.index+p+i);if(Math.min(u,i)%2){const f=D.slice(1,-1);return{type:"em",raw:D,text:f,tokens:this.lexer.inlineTokens(f)}}const h=D.slice(2,-2);return{type:"strong",raw:D,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const r=/[^ ]/.test(n),u=/^ /.test(n)&&/ $/.test(n);return r&&u&&(n=n.substring(1,n.length-1)),n=H(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=H(t[1]),r="mailto:"+n):(n=H(t[1]),r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let r,u;if(t[2]==="@")r=H(t[0]),u="mailto:"+r;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);r=H(t[0]),u=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:r,href:u,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:H(t[0]),{type:"text",raw:t[0],text:n}}}}const De=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Yt=/(?:[*+-]|\d{1,9}[.)])/,en=S(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Yt).getRegex(),Qe=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ve=/(?!\s*\])(?:\\.|[^\[\]\\])+/,_r=S(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Ve).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Or=S(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Yt).getRegex(),Se="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Je=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,jr=S("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Je).replace("tag",Se).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),bt=S(Qe).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Se).getRegex(),Ue={blockquote:S(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",bt).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:_r,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:De,html:jr,lheading:en,list:Or,newline:/^(?: *(?:\n|$))+/,paragraph:bt,table:ae,text:/^[^\n]+/},vt=S("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Se).getRegex(),Nr={...Ue,table:vt,paragraph:S(Qe).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",vt).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Se).getRegex()},qr={...Ue,html:S(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Je).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ae,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:S(Qe).replace("hr",De).replace("heading",` *#{1,6} *[^
]`).replace("lheading",en).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},tn=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,nn=/^( {2,}|\\)\n(?!\s*$)/,de="\\p{P}$+<=>`^|~",Zr=S(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,de).getRegex(),Hr=S(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,de).getRegex(),Mr=S("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,de).getRegex(),Wr=S("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,de).getRegex(),Qr=S(/\\([punct])/,"gu").replace(/punct/g,de).getRegex(),Vr=S(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Jr=S(Je).replace("(?:-->|$)","-->").getRegex(),Ur=S("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Jr).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),we=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Xr=S(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",we).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Bt=S(/^!?\[(label)\]\[(ref)\]/).replace("label",we).replace("ref",Ve).getRegex(),yt=S(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ve).getRegex(),Xe={_backpedal:ae,anyPunctuation:Qr,autolink:Vr,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:nn,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ae,emStrongLDelim:Hr,emStrongRDelimAst:Mr,emStrongRDelimUnd:Wr,escape:tn,link:Xr,nolink:yt,punctuation:Zr,reflink:Bt,reflinkSearch:S("reflink|nolink(?!\\()","g").replace("reflink",Bt).replace("nolink",yt).getRegex(),tag:Ur,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ae},Gr={...Xe,link:S(/^!?\[(label)\]\((.*?)\)/).replace("label",we).getRegex(),reflink:S(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",we).getRegex()},Oe={...Xe,escape:S(tn).replace("])","~|])").getRegex(),url:S(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Kr={...Oe,br:S(nn).replace("{2,}","*").getRegex(),text:S(Oe.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},me={normal:Ue,gfm:Nr,pedantic:qr},oe={normal:Xe,gfm:Oe,breaks:Kr,pedantic:Gr};class M{constructor(e){T(this,"tokens");T(this,"options");T(this,"state");T(this,"tokenizer");T(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||re,this.options.tokenizer=this.options.tokenizer||new Ee,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:me.normal,inline:oe.normal};this.options.pedantic?(t.block=me.pedantic,t.inline=oe.pedantic):this.options.gfm&&(t.block=me.gfm,this.options.breaks?t.inline=oe.breaks:t.inline=oe.gfm),this.tokenizer.rules=t}static get rules(){return{block:me,inline:oe}}static lex(e,t){return new M(t).lex(e)}static lexInline(e,t){return new M(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,r,u,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(i,l,a)=>l+"    ".repeat(a.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?t.push(n):(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(u=e,this.options.extensions&&this.options.extensions.startBlock){let i=1/0;const l=e.slice(1);let a;this.options.extensions.startBlock.forEach(c=>{a=c.call({lexer:this},l),typeof a=="number"&&a>=0&&(i=Math.min(i,a))}),i<1/0&&i>=0&&(u=e.substring(0,i+1))}if(this.state.top&&(n=this.tokenizer.paragraph(u)))r=t[t.length-1],o&&r.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),o=u.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);else if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,u,o,i,l,a=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(a))!=null;)a=a.slice(0,o.index)+"++"+a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(i||(l=""),i=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,a,l))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(u=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const p=e.slice(1);let D;this.options.extensions.startInline.forEach(h=>{D=h.call({lexer:this},p),typeof D=="number"&&D>=0&&(c=Math.min(c,D))}),c<1/0&&c>=0&&(u=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(u))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(l=n.raw.slice(-1)),i=!0,r=t[t.length-1],r&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class be{constructor(e){T(this,"options");this.options=e||re}code(e,t,n){var u;const r=(u=(t||"").match(/^\S*/))==null?void 0:u[0];return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="language-'+H(r)+'">'+(n?e:H(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:H(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const r=t?"ol":"ul";return"<"+r+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+r+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const r=At(e);if(r===null)return n;let u='<a href="'+(e=r)+'"';return t&&(u+=' title="'+t+'"'),u+=">"+n+"</a>",u}image(e,t,n){const r=At(e);if(r===null)return n;let u=`<img src="${e=r}" alt="${n}"`;return t&&(u+=` title="${t}"`),u+=">",u}text(e){return e}}class Ge{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class Q{constructor(e){T(this,"options");T(this,"renderer");T(this,"textRenderer");this.options=e||re,this.options.renderer=this.options.renderer||new be,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ge}static parse(e,t){return new Q(t).parse(e)}static parseInline(e,t){return new Q(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const u=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[u.type]){const o=u,i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=i||"";continue}}switch(u.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=u;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Lr(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=u;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=u;let i="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});i+=this.renderer.tablerow(l);let a="";for(let c=0;c<o.rows.length;c++){const p=o.rows[c];l="";for(let D=0;D<p.length;D++)l+=this.renderer.tablecell(this.parseInline(p[D].tokens),{header:!1,align:o.align[D]});a+=this.renderer.tablerow(l)}n+=this.renderer.table(i,a);continue}case"blockquote":{const o=u,i=this.parse(o.tokens);n+=this.renderer.blockquote(i);continue}case"list":{const o=u,i=o.ordered,l=o.start,a=o.loose;let c="";for(let p=0;p<o.items.length;p++){const D=o.items[p],h=D.checked,f=D.task;let d="";if(D.task){const x=this.renderer.checkbox(!!h);a?D.tokens.length>0&&D.tokens[0].type==="paragraph"?(D.tokens[0].text=x+" "+D.tokens[0].text,D.tokens[0].tokens&&D.tokens[0].tokens.length>0&&D.tokens[0].tokens[0].type==="text"&&(D.tokens[0].tokens[0].text=x+" "+D.tokens[0].tokens[0].text)):D.tokens.unshift({type:"text",text:x+" "}):d+=x+" "}d+=this.parse(D.tokens,a),c+=this.renderer.listitem(d,f,!!h)}n+=this.renderer.list(c,i,l);continue}case"html":{const o=u;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=u;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=u,i=o.tokens?this.parseInline(o.tokens):o.text;for(;r+1<e.length&&e[r+1].type==="text";)o=e[++r],i+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(i):i;continue}default:{const o='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const u=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[u.type]){const o=this.options.extensions.renderers[u.type].call({parser:this},u);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(u.type)){n+=o||"";continue}}switch(u.type){case"escape":{const o=u;n+=t.text(o.text);break}case"html":{const o=u;n+=t.html(o.text);break}case"link":{const o=u;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=u;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=u;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=u;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=u;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=u;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=u;n+=t.text(o.text);break}default:{const o='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class ce{constructor(e){T(this,"options");this.options=e||re}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}T(ce,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var ne,je,rn,Vt;const ee=new(Vt=class{constructor(...s){nt(this,ne);T(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});T(this,"options",this.setOptions);T(this,"parse",Fe(this,ne,je).call(this,M.lex,Q.parse));T(this,"parseInline",Fe(this,ne,je).call(this,M.lexInline,Q.parseInline));T(this,"Parser",Q);T(this,"Renderer",be);T(this,"TextRenderer",Ge);T(this,"Lexer",M);T(this,"Tokenizer",Ee);T(this,"Hooks",ce);this.use(...s)}walkTokens(s,e){var n,r;let t=[];for(const u of s)switch(t=t.concat(e.call(this,u)),u.type){case"table":{const o=u;for(const i of o.header)t=t.concat(this.walkTokens(i.tokens,e));for(const i of o.rows)for(const l of i)t=t.concat(this.walkTokens(l.tokens,e));break}case"list":{const o=u;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=u;(r=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&r[o.type]?this.defaults.extensions.childTokens[o.type].forEach(i=>{const l=o[i].flat(1/0);t=t.concat(this.walkTokens(l,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...s){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return s.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const u=e.renderers[r.name];e.renderers[r.name]=u?function(...o){let i=r.renderer.apply(this,o);return i===!1&&(i=u.apply(this,o)),i}:r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const u=e[r.level];u?u.unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),n.extensions=e),t.renderer){const r=this.defaults.renderer||new be(this.defaults);for(const u in t.renderer){if(!(u in r))throw new Error(`renderer '${u}' does not exist`);if(u==="options")continue;const o=u,i=t.renderer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c||""}}n.renderer=r}if(t.tokenizer){const r=this.defaults.tokenizer||new Ee(this.defaults);for(const u in t.tokenizer){if(!(u in r))throw new Error(`tokenizer '${u}' does not exist`);if(["options","rules","lexer"].includes(u))continue;const o=u,i=t.tokenizer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.tokenizer=r}if(t.hooks){const r=this.defaults.hooks||new ce;for(const u in t.hooks){if(!(u in r))throw new Error(`hook '${u}' does not exist`);if(u==="options")continue;const o=u,i=t.hooks[o],l=r[o];ce.passThroughHooks.has(u)?r[o]=a=>{if(this.defaults.async)return Promise.resolve(i.call(r,a)).then(p=>l.call(r,p));const c=i.call(r,a);return l.call(r,c)}:r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.hooks=r}if(t.walkTokens){const r=this.defaults.walkTokens,u=t.walkTokens;n.walkTokens=function(o){let i=[];return i.push(u.call(this,o)),r&&(i=i.concat(r.call(this,o))),i}}this.defaults={...this.defaults,...n}}),this}setOptions(s){return this.defaults={...this.defaults,...s},this}lexer(s,e){return M.lex(s,e??this.defaults)}parser(s,e){return Q.parse(s,e??this.defaults)}},ne=new WeakSet,je=function(s,e){return(t,n)=>{const r={...n},u={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(u.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),u.async=!0);const o=Fe(this,ne,rn).call(this,!!u.silent,!!u.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(u.hooks&&(u.hooks.options=u),u.async)return Promise.resolve(u.hooks?u.hooks.preprocess(t):t).then(i=>s(i,u)).then(i=>u.hooks?u.hooks.processAllTokens(i):i).then(i=>u.walkTokens?Promise.all(this.walkTokens(i,u.walkTokens)).then(()=>i):i).then(i=>e(i,u)).then(i=>u.hooks?u.hooks.postprocess(i):i).catch(o);try{u.hooks&&(t=u.hooks.preprocess(t));let i=s(t,u);u.hooks&&(i=u.hooks.processAllTokens(i)),u.walkTokens&&this.walkTokens(i,u.walkTokens);let l=e(i,u);return u.hooks&&(l=u.hooks.postprocess(l)),l}catch(i){return o(i)}}},rn=function(s,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,s){const n="<p>An error occurred:</p><pre>"+H(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Vt);function z(s,e){return ee.parse(s,e)}z.options=z.setOptions=function(s){return ee.setOptions(s),z.defaults=ee.defaults,xt(z.defaults),z},z.getDefaults=zr,z.defaults=re,z.use=function(...s){return ee.use(...s),z.defaults=ee.defaults,xt(z.defaults),z},z.walkTokens=function(s,e){return ee.walkTokens(s,e)},z.parseInline=ee.parseInline,z.Parser=Q,z.parser=Q.parse,z.Renderer=be,z.TextRenderer=Ge,z.Lexer=M,z.lexer=M.lex,z.Tokenizer=Ee,z.Hooks=ce,z.parse=z,z.options,z.setOptions,z.use,z.walkTokens,z.parseInline,Q.parse,M.lex;const Yr=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,eu=Object.hasOwnProperty;class tu{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let r=function(o,i){return typeof o!="string"?"":(i||(o=o.toLowerCase()),o.replace(Yr,"").replace(/ /g,"-"))}(e,t===!0);const u=r;for(;eu.call(n.occurrences,r);)n.occurrences[u]++,r=u+"-"+n.occurrences[u];return n.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}function nu(s){let e,t;return e=new ze({props:{tokens:s[0],renderers:s[1],options:s[2]}}),{c(){J(e.$$.fragment)},m(n,r){U(e,n,r),t=!0},p(n,[r]){const u={};1&r&&(u.tokens=n[0]),2&r&&(u.renderers=n[1]),4&r&&(u.options=n[2]),e.$set(u)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){X(e,n)}}}function ru(s,e,t){(function(){const a=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||a(c)},hn(()=>{console.warn=a})})();let n,r,u,{source:o}=e,{options:i={}}=e,{renderers:l={}}=e;return s.$$set=a=>{"source"in a&&t(3,o=a.source),"options"in a&&t(4,i=a.options),"renderers"in a&&t(5,l=a.renderers)},s.$$.update=()=>{var a;56&s.$$.dirty&&(t(0,(a=o,n=new M().lex(a))),t(1,r={heading:An,blockquote:bn,list:Pn,list_item:jn,br:Zn,code:Wn,codespan:Jn,table:Gn,html:er,paragraph:rr,link:or,text:ar,def:hr,del:fr,em:kr,hr:xr,strong:Er,image:vr,space:$t,escape:$t,...l}),t(2,u={baseUrl:"/",slugger:new tu,...i}))},[n,r,u,o,i,l]}class uu extends B{constructor(e){super(),y(this,e,ru,nu,b,{source:3,options:4,renderers:5})}}const su=s=>({}),zt=s=>({}),ou=s=>({}),St=s=>({}),iu=s=>({}),Tt=s=>({});function lu(s){let e,t,n,r,u,o,i,l,a,c,p,D;const h=s[13].topBarLeft,f=O(h,s,s[12],Tt),d=s[13].topBarRight,x=O(d,s,s[12],St);function F(g){s[16](g)}let _={options:{lineNumbers:"off",wrappingIndent:"same",padding:s[5],wordWrap:s[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:s[3].text,lang:s[4]||s[3].lang,height:s[6]};s[0]!==void 0&&(_.editorInstance=s[0]),o=new kn({props:_}),We.push(()=>Dn(o,"editorInstance",F));const E=s[13].actionsBar,v=O(E,s,s[12],zt);return{c(){e=w("div"),t=w("div"),n=w("div"),f&&f.c(),r=ue(),x&&x.c(),u=ue(),J(o.$$.fragment),l=ue(),a=w("div"),v&&v.c(),$(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),$(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),$(a,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),$(e,"class","c-codeblock svelte-mexfz1"),$(e,"role","button"),$(e,"tabindex","0")},m(g,I){C(g,e,I),L(e,t),L(t,n),f&&f.m(n,null),L(t,r),x&&x.m(t,null),L(e,u),U(o,e,null),L(e,l),L(e,a),v&&v.m(a,null),s[17](e),c=!0,p||(D=[st(window,"focus",s[15]),st(e,"mouseenter",s[14])],p=!0)},p(g,[I]){f&&f.p&&(!c||4096&I)&&j(f,h,g,g[12],c?q(h,g[12],I,iu):N(g[12]),Tt),x&&x.p&&(!c||4096&I)&&j(x,d,g,g[12],c?q(d,g[12],I,ou):N(g[12]),St);const P={};36&I&&(P.options={lineNumbers:"off",wrappingIndent:"same",padding:g[5],wordWrap:g[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&I&&(P.text=g[3].text),24&I&&(P.lang=g[4]||g[3].lang),64&I&&(P.height=g[6]),!i&&1&I&&(i=!0,P.editorInstance=g[0],dn(()=>i=!1)),o.$set(P),v&&v.p&&(!c||4096&I)&&j(v,E,g,g[12],c?q(E,g[12],I,su):N(g[12]),zt)},i(g){c||(k(f,g),k(x,g),k(o.$$.fragment,g),k(v,g),c=!0)},o(g){m(f,g),m(x,g),m(o.$$.fragment,g),m(v,g),c=!1},d(g){g&&A(e),f&&f.d(g),x&&x.d(g),X(o),v&&v.d(g),s[17](null),p=!1,fn(D)}}}function au(s,e,t){let n,{$$slots:r={},$$scope:u}=e,{scroll:o=!1}=e,{token:i}=e,{language:l}=e,{padding:a={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:p}=e,{height:D}=e;const h=mn.getContext().monaco;gn(s,h,F=>t(18,n=F));const f=$n(),d=()=>{if(!c)return;const F=c.getSelections();if(!(F!=null&&F.length))return;const _=c.getModel();if(F.map(E=>(_==null?void 0:_.getValueLengthInRange(E))||0).reduce((E,v)=>E+v,0)!==0)return F.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(E=>(_==null?void 0:_.getValueInRange(E))||"").join(`
`)},x=()=>{if(c)return c.getValue()||""};return s.$$set=F=>{"scroll"in F&&t(2,o=F.scroll),"token"in F&&t(3,i=F.token),"language"in F&&t(4,l=F.language),"padding"in F&&t(5,a=F.padding),"editorInstance"in F&&t(0,c=F.editorInstance),"element"in F&&t(1,p=F.element),"height"in F&&t(6,D=F.height),"$$scope"in F&&t(12,u=F.$$scope)},s.$$.update=()=>{var F;32&s.$$.dirty&&(F=a,c==null||c.updateOptions({padding:F})),65&s.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:D!==void 0?"auto":"hidden"}}))},[c,p,o,i,l,a,D,h,f,()=>c&&(d()||x())||"",d,x,u,r,function(F){Fn.call(this,s,F)},()=>f.requestLayout(),function(F){c=F,t(0,c)},function(F){We[F?"unshift":"push"](()=>{p=F,t(1,p)})}]}class Rt extends B{constructor(e){super(),y(this,e,au,lu,b,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const cu=s=>({codespanContents:2&s}),It=s=>({codespanContents:s[1]});function pu(s){let e,t,n;const r=s[4].default,u=O(r,s,s[3],It),o=u||function(i){let l;return{c(){l=te(i[1])},m(a,c){C(a,l,c)},p(a,c){2&c&&he(l,a[1])},d(a){a&&A(l)}}}(s);return{c(){e=w("span"),t=w("code"),o&&o.c(),$(t,"class","markdown-codespan svelte-1dofrdh")},m(i,l){C(i,e,l),L(e,t),o&&o.m(t,null),s[5](e),n=!0},p(i,[l]){u?u.p&&(!n||10&l)&&j(u,r,i,i[3],n?q(r,i[3],l,cu):N(i[3]),It):o&&o.p&&(!n||2&l)&&o.p(i,n?l:-1)},i(i){n||(k(o,i),n=!0)},o(i){m(o,i),n=!1},d(i){i&&A(e),o&&o.d(i),s[5](null)}}}function hu(s,e,t){let n,{$$slots:r={},$$scope:u}=e,{token:o}=e,{element:i}=e;return s.$$set=l=>{"token"in l&&t(2,o=l.token),"element"in l&&t(0,i=l.element),"$$scope"in l&&t(3,u=l.$$scope)},s.$$.update=()=>{4&s.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[i,n,o,u,r,function(l){We[l?"unshift":"push"](()=>{i=l,t(0,i)})}]}class Lt extends B{constructor(e){super(),y(this,e,hu,pu,b,{token:2,element:0})}}function Du(s){let e,t,n,r,u=s[0].text+"";return{c(){e=w("span"),t=te("~"),n=te(u),r=te("~")},m(o,i){C(o,e,i),L(e,t),L(e,n),L(e,r)},p(o,[i]){1&i&&u!==(u=o[0].text+"")&&he(n,u)},i:R,o:R,d(o){o&&A(e)}}}function du(s,e,t){let{token:n}=e;return s.$$set=r=>{"token"in r&&t(0,n=r.token)},[n]}class Pt extends B{constructor(e){super(),y(this,e,du,Du,b,{token:0})}}function fu(s){let e,t;const n=s[1].default,r=O(n,s,s[0],null);return{c(){e=w("p"),r&&r.c(),$(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(u,o){C(u,e,o),r&&r.m(e,null),t=!0},p(u,[o]){r&&r.p&&(!t||1&o)&&j(r,n,u,u[0],t?q(n,u[0],o,null):N(u[0]),null)},i(u){t||(k(r,u),t=!0)},o(u){m(r,u),t=!1},d(u){u&&A(e),r&&r.d(u)}}}function gu(s,e,t){let{$$slots:n={},$$scope:r}=e;return s.$$set=u=>{"$$scope"in u&&t(0,r=u.$$scope)},[r,n]}class _t extends B{constructor(e){super(),y(this,e,gu,fu,b,{})}}function Fu(s){let e,t,n;return t=new uu({props:{source:s[0],renderers:{codespan:Lt,code:Rt,paragraph:_t,del:Pt,...s[1]}}}),{c(){e=w("div"),J(t.$$.fragment),$(e,"class","c-markdown svelte-n6ddeo")},m(r,u){C(r,e,u),U(t,e,null),n=!0},p(r,[u]){const o={};1&u&&(o.source=r[0]),2&u&&(o.renderers={codespan:Lt,code:Rt,paragraph:_t,del:Pt,...r[1]}),t.$set(o)},i(r){n||(k(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&A(e),X(t)}}}function ku(s,e,t){let{markdown:n}=e,{renderers:r={}}=e;return s.$$set=u=>{"markdown"in u&&t(0,n=u.markdown),"renderers"in u&&t(1,r=u.renderers)},[n,r]}class Tu extends B{constructor(e){super(),y(this,e,ku,Fu,b,{markdown:0,renderers:1})}}function mu(s){let e,t;return{c(){e=Ae("svg"),t=Ae("path"),$(t,"fill-rule","evenodd"),$(t,"clip-rule","evenodd"),$(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),$(t,"fill","currentColor"),$(e,"class",s[0]),$(e,"width","15"),$(e,"height","15"),$(e,"viewBox","0 0 15 15"),$(e,"fill","none"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){C(n,e,r),L(e,t)},p(n,[r]){1&r&&$(e,"class",n[0])},i:R,o:R,d(n){n&&A(e)}}}function $u(s,e,t){let{class:n=""}=e;return s.$$set=r=>{"class"in r&&t(0,n=r.class)},[n]}class Ru extends B{constructor(e){super(),y(this,e,$u,mu,b,{class:0})}}function xu(s){let e,t;return{c(){e=Ae("svg"),t=Ae("path"),$(t,"fill-rule","evenodd"),$(t,"clip-rule","evenodd"),$(t,"d","M4.04896 3.25233C4.20904 3.1558 4.40798 3.15014 4.57329 3.23739L14.1733 8.30406C14.3482 8.39638 14.4577 8.57794 14.4577 8.77573C14.4577 8.97352 14.3482 9.15508 14.1733 9.2474L4.57329 14.3141C4.40798 14.4013 4.20904 14.3957 4.04896 14.2991C3.88888 14.2026 3.79102 14.0293 3.79102 13.8424V3.70906C3.79102 3.52214 3.88888 3.34885 4.04896 3.25233Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 1 16 16"),$(e,"fill","none"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){C(n,e,r),L(e,t)},p:R,i:R,o:R,d(n){n&&A(e)}}}class Iu extends B{constructor(e){super(),y(this,e,null,xu,b,{})}}function V(){}function Ot(s,e,t,n,r){for(var u,o=[];e;)o.push(e),u=e.previousComponent,delete e.previousComponent,e=u;o.reverse();for(var i=0,l=o.length,a=0,c=0;i<l;i++){var p=o[i];if(p.removed)p.value=s.join(n.slice(c,c+p.count)),c+=p.count;else{if(!p.added&&r){var D=t.slice(a,a+p.count);D=D.map(function(h,f){var d=n[c+f];return d.length>h.length?d:h}),p.value=s.join(D)}else p.value=s.join(t.slice(a,a+p.count));a+=p.count,p.added||(c+=p.count)}}return o}function jt(s,e){var t;for(t=0;t<s.length&&t<e.length;t++)if(s[t]!=e[t])return s.slice(0,t);return s.slice(0,t)}function Nt(s,e){var t;if(!s||!e||s[s.length-1]!=e[e.length-1])return"";for(t=0;t<s.length&&t<e.length;t++)if(s[s.length-(t+1)]!=e[e.length-(t+1)])return s.slice(-t);return s.slice(-t)}function Ne(s,e,t){if(s.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(s)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return t+s.slice(e.length)}function qe(s,e,t){if(!e)return s+t;if(s.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(s)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return s.slice(0,-e.length)+t}function ie(s,e){return Ne(s,e,"")}function $e(s,e){return qe(s,e,"")}function qt(s,e){return e.slice(0,function(t,n){var r=0;t.length>n.length&&(r=t.length-n.length);var u=n.length;t.length<n.length&&(u=t.length);var o=Array(u),i=0;o[0]=0;for(var l=1;l<u;l++){for(n[l]==n[i]?o[l]=o[i]:o[l]=i;i>0&&n[l]!=n[i];)i=o[i];n[l]==n[i]&&i++}i=0;for(var a=r;a<t.length;a++){for(;i>0&&t[a]!=n[i];)i=o[i];t[a]==n[i]&&i++}return i}(s,e))}V.prototype={diff:function(s,e){var t,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.callback;typeof n=="function"&&(r=n,n={});var u=this;function o(E){return E=u.postProcess(E,n),r?(setTimeout(function(){r(E)},0),!0):E}s=this.castInput(s,n),e=this.castInput(e,n),s=this.removeEmpty(this.tokenize(s,n));var i=(e=this.removeEmpty(this.tokenize(e,n))).length,l=s.length,a=1,c=i+l;n.maxEditLength!=null&&(c=Math.min(c,n.maxEditLength));var p=(t=n.timeout)!==null&&t!==void 0?t:1/0,D=Date.now()+p,h=[{oldPos:-1,lastComponent:void 0}],f=this.extractCommon(h[0],e,s,0,n);if(h[0].oldPos+1>=l&&f+1>=i)return o(Ot(u,h[0].lastComponent,e,s,u.useLongestToken));var d=-1/0,x=1/0;function F(){for(var E=Math.max(d,-a);E<=Math.min(x,a);E+=2){var v=void 0,g=h[E-1],I=h[E+1];g&&(h[E-1]=void 0);var P=!1;if(I){var Z=I.oldPos-E;P=I&&0<=Z&&Z<i}var se=g&&g.oldPos+1<l;if(P||se){if(v=!se||P&&g.oldPos<I.oldPos?u.addToPath(I,!0,!1,0,n):u.addToPath(g,!1,!0,1,n),f=u.extractCommon(v,e,s,E,n),v.oldPos+1>=l&&f+1>=i)return o(Ot(u,v.lastComponent,e,s,u.useLongestToken));h[E]=v,v.oldPos+1>=l&&(x=Math.min(x,E-1)),f+1>=i&&(d=Math.max(d,E+1))}else h[E]=void 0}a++}if(r)(function E(){setTimeout(function(){if(a>c||Date.now()>D)return r();F()||E()},0)})();else for(;a<=c&&Date.now()<=D;){var _=F();if(_)return _}},addToPath:function(s,e,t,n,r){var u=s.lastComponent;return u&&!r.oneChangePerToken&&u.added===e&&u.removed===t?{oldPos:s.oldPos+n,lastComponent:{count:u.count+1,added:e,removed:t,previousComponent:u.previousComponent}}:{oldPos:s.oldPos+n,lastComponent:{count:1,added:e,removed:t,previousComponent:u}}},extractCommon:function(s,e,t,n,r){for(var u=e.length,o=t.length,i=s.oldPos,l=i-n,a=0;l+1<u&&i+1<o&&this.equals(t[i+1],e[l+1],r);)l++,i++,a++,r.oneChangePerToken&&(s.lastComponent={count:1,previousComponent:s.lastComponent,added:!1,removed:!1});return a&&!r.oneChangePerToken&&(s.lastComponent={count:a,previousComponent:s.lastComponent,added:!1,removed:!1}),s.oldPos=i,l},equals:function(s,e,t){return t.comparator?t.comparator(s,e):s===e||t.ignoreCase&&s.toLowerCase()===e.toLowerCase()},removeEmpty:function(s){for(var e=[],t=0;t<s.length;t++)s[t]&&e.push(s[t]);return e},castInput:function(s){return s},tokenize:function(s){return Array.from(s)},join:function(s){return s.join("")},postProcess:function(s){return s}};var ve="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",Cu=new RegExp("[".concat(ve,"]+|\\s+|[^").concat(ve,"]"),"ug"),xe=new V;function Zt(s,e,t,n){if(e&&t){var r=e.value.match(/^\s*/)[0],u=e.value.match(/\s*$/)[0],o=t.value.match(/^\s*/)[0],i=t.value.match(/\s*$/)[0];if(s){var l=jt(r,o);s.value=qe(s.value,o,l),e.value=ie(e.value,l),t.value=ie(t.value,l)}if(n){var a=Nt(u,i);n.value=Ne(n.value,i,a),e.value=$e(e.value,a),t.value=$e(t.value,a)}}else if(t)s&&(t.value=t.value.replace(/^\s*/,"")),n&&(n.value=n.value.replace(/^\s*/,""));else if(s&&n){var c=n.value.match(/^\s*/)[0],p=e.value.match(/^\s*/)[0],D=e.value.match(/\s*$/)[0],h=jt(c,p);e.value=ie(e.value,h);var f=Nt(ie(c,h),D);e.value=$e(e.value,f),n.value=Ne(n.value,c,f),s.value=qe(s.value,c,c.slice(0,c.length-f.length))}else if(n){var d=n.value.match(/^\s*/)[0],x=qt(e.value.match(/\s*$/)[0],d);e.value=$e(e.value,x)}else if(s){var F=qt(s.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=ie(e.value,F)}}xe.equals=function(s,e,t){return t.ignoreCase&&(s=s.toLowerCase(),e=e.toLowerCase()),s.trim()===e.trim()},xe.tokenize=function(s){var e,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t.intlSegmenter){if(t.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(t.intlSegmenter.segment(s),function(u){return u.segment})}else e=s.match(Cu)||[];var n=[],r=null;return e.forEach(function(u){/\s/.test(u)?r==null?n.push(u):n.push(n.pop()+u):/\s/.test(r)?n[n.length-1]==r?n.push(n.pop()+u):n.push(r+u):n.push(u),r=u}),n},xe.join=function(s){return s.map(function(e,t){return t==0?e:e.replace(/^\s+/,"")}).join("")},xe.postProcess=function(s,e){if(!s||e.oneChangePerToken)return s;var t=null,n=null,r=null;return s.forEach(function(u){u.added?n=u:u.removed?r=u:((n||r)&&Zt(t,r,n,u),t=u,n=null,r=null)}),(n||r)&&Zt(t,r,n,null),s},new V().tokenize=function(s){var e=new RegExp("(\\r?\\n)|[".concat(ve,"]+|[^\\S\\n\\r]+|[^").concat(ve,"]"),"ug");return s.match(e)||[]};var Be=new V;function Ht(s,e,t){return Be.diff(s,e,t)}function Mt(s,e){var t=Object.keys(s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(s);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(s,r).enumerable})),t.push.apply(t,n)}return t}function ye(s){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Mt(Object(t),!0).forEach(function(n){Eu(s,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(t)):Mt(Object(t)).forEach(function(n){Object.defineProperty(s,n,Object.getOwnPropertyDescriptor(t,n))})}return s}function Au(s){var e=function(t,n){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var u=r.call(t,n||"default");if(typeof u!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(t)}(s,"string");return typeof e=="symbol"?e:e+""}function Ze(s){return Ze=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ze(s)}function Eu(s,e,t){return(e=Au(e))in s?Object.defineProperty(s,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):s[e]=t,s}function Le(s){return function(e){if(Array.isArray(e))return Pe(e)}(s)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(s)||function(e,t){if(e){if(typeof e=="string")return Pe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Pe(e,t)}}(s)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Pe(s,e){(e==null||e>s.length)&&(e=s.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=s[t];return n}Be.tokenize=function(s,e){e.stripTrailingCr&&(s=s.replace(/\r\n/g,`
`));var t=[],n=s.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var u=n[r];r%2&&!e.newlineIsToken?t[t.length-1]+=u:t.push(u)}return t},Be.equals=function(s,e,t){return t.ignoreWhitespace?(t.newlineIsToken&&s.includes(`
`)||(s=s.trim()),t.newlineIsToken&&e.includes(`
`)||(e=e.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(s.endsWith(`
`)&&(s=s.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),V.prototype.equals.call(this,s,e,t)},new V().tokenize=function(s){return s.split(/(\S.+?[.!?])(?=\s+|$)/)},new V().tokenize=function(s){return s.split(/([{}:;,]|\s+)/)};var le=new V;function He(s,e,t,n,r){var u,o;for(e=e||[],t=t||[],n&&(s=n(r,s)),u=0;u<e.length;u+=1)if(e[u]===s)return t[u];if(Object.prototype.toString.call(s)==="[object Array]"){for(e.push(s),o=new Array(s.length),t.push(o),u=0;u<s.length;u+=1)o[u]=He(s[u],e,t,n,r);return e.pop(),t.pop(),o}if(s&&s.toJSON&&(s=s.toJSON()),Ze(s)==="object"&&s!==null){e.push(s),o={},t.push(o);var i,l=[];for(i in s)Object.prototype.hasOwnProperty.call(s,i)&&l.push(i);for(l.sort(),u=0;u<l.length;u+=1)o[i=l[u]]=He(s[i],e,t,n,i);e.pop(),t.pop()}else o=s;return o}le.useLongestToken=!0,le.tokenize=Be.tokenize,le.castInput=function(s,e){var t=e.undefinedReplacement,n=e.stringifyReplacer,r=n===void 0?function(u,o){return o===void 0?t:o}:n;return typeof s=="string"?s:JSON.stringify(He(s,null,null,r),r,"  ")},le.equals=function(s,e,t){return V.prototype.equals.call(le,s.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),t)};var _e=new V;function Lu(s){var e=s.split(/\n/),t=[],n=0;function r(){var i={};for(t.push(i);n<e.length;){var l=e[n];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var a=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);a&&(i.index=a[1]),n++}for(u(i),u(i),i.hunks=[];n<e.length;){var c=e[n];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(o());else{if(c)throw new Error("Unknown line "+(n+1)+" "+JSON.stringify(c));n++}}}function u(i){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[n]);if(l){var a=l[1]==="---"?"old":"new",c=l[2].split("	",2),p=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(p)&&(p=p.substr(1,p.length-2)),i[a+"FileName"]=p,i[a+"Header"]=(c[1]||"").trim(),n++}}function o(){var i=n,l=e[n++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+l[1],oldLines:l[2]===void 0?1:+l[2],newStart:+l[3],newLines:l[4]===void 0?1:+l[4],lines:[]};a.oldLines===0&&(a.oldStart+=1),a.newLines===0&&(a.newStart+=1);for(var c=0,p=0;n<e.length&&(p<a.oldLines||c<a.newLines||(D=e[n])!==null&&D!==void 0&&D.startsWith("\\"));n++){var D,h=e[n].length==0&&n!=e.length-1?" ":e[n][0];if(h!=="+"&&h!=="-"&&h!==" "&&h!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(e[n]));a.lines.push(e[n]),h==="+"?c++:h==="-"?p++:h===" "&&(c++,p++)}if(c||a.newLines!==1||(a.newLines=0),p||a.oldLines!==1||(a.oldLines=0),c!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(p!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return a}for(;n<e.length;)r();return t}function Wt(s,e,t,n,r,u,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),o.context===void 0&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!o.callback)return l(Ht(t,n,o));var i=o.callback;function l(a){if(a){a.push({value:"",lines:[]});for(var c=[],p=0,D=0,h=[],f=1,d=1,x=function(){var P=a[F],Z=P.lines||function(fe){var sn=fe.endsWith(`
`),ge=fe.split(`
`).map(function(on){return on+`
`});return sn?ge.pop():ge.push(ge.pop().slice(0,-1)),ge}(P.value);if(P.lines=Z,P.added||P.removed){var se;if(!p){var Ke=a[F-1];p=f,D=d,Ke&&(h=o.context>0?I(Ke.lines.slice(-o.context)):[],p-=h.length,D-=h.length)}(se=h).push.apply(se,Le(Z.map(function(fe){return(P.added?"+":"-")+fe}))),P.added?d+=Z.length:f+=Z.length}else{if(p)if(Z.length<=2*o.context&&F<a.length-2){var Ye;(Ye=h).push.apply(Ye,Le(I(Z)))}else{var et,Te=Math.min(Z.length,o.context);(et=h).push.apply(et,Le(I(Z.slice(0,Te))));var un={oldStart:p,oldLines:f-p+Te,newStart:D,newLines:d-D+Te,lines:h};c.push(un),p=0,D=0,h=[]}f+=Z.length,d+=Z.length}},F=0;F<a.length;F++)x();for(var _=0,E=c;_<E.length;_++)for(var v=E[_],g=0;g<v.lines.length;g++)v.lines[g].endsWith(`
`)?v.lines[g]=v.lines[g].slice(0,-1):(v.lines.splice(g+1,0,"\\ No newline at end of file"),g++);return{oldFileName:s,newFileName:e,oldHeader:r,newHeader:u,hunks:c}}function I(P){return P.map(function(Z){return" "+Z})}}Ht(t,n,ye(ye({},o),{},{callback:function(a){var c=l(a);i(c)}}))}function Me(s){if(Array.isArray(s))return s.map(Me).join(`
`);var e=[];s.oldFileName==s.newFileName&&e.push("Index: "+s.oldFileName),e.push("==================================================================="),e.push("--- "+s.oldFileName+(s.oldHeader===void 0?"":"	"+s.oldHeader)),e.push("+++ "+s.newFileName+(s.newHeader===void 0?"":"	"+s.newHeader));for(var t=0;t<s.hunks.length;t++){var n=s.hunks[t];n.oldLines===0&&(n.oldStart-=1),n.newLines===0&&(n.newStart-=1),e.push("@@ -"+n.oldStart+","+n.oldLines+" +"+n.newStart+","+n.newLines+" @@"),e.push.apply(e,n.lines)}return e.join(`
`)+`
`}function wu(s,e,t,n,r,u,o){var i;if(typeof o=="function"&&(o={callback:o}),(i=o)===null||i===void 0||!i.callback){var l=Wt(s,e,t,n,r,u,o);return l?Me(l):void 0}var a=o.callback;Wt(s,e,t,n,r,u,ye(ye({},o),{},{callback:function(c){c?a(Me(c)):a()}}))}function Qt(s){let e=0;const t=1e4,n=s.length>t?s.substring(0,5e3)+s.substring(s.length-5e3):s;for(let r=0;r<n.length;r++)e=(e<<5)-e+n.charCodeAt(r),e|=0;return Math.abs(e).toString(36)}function bu(s,e,t,n,r={}){const{context:u=3,generateId:o=!0}=r,i=wu(s,e,t,n,"","",{context:u}),l=e||s;let a;return o?a=`${Qt(l)}-${Qt(t+n)}`:a=Math.random().toString(36).substring(2,15),{id:a,path:l,diff:i,originalCode:t,modifiedCode:n}}function Pu(s,e={}){return s.map(t=>bu(t.oldPath,t.newPath,t.oldContent,t.newContent,e))}function _u(s){const e=s.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function Ou(s){return!s.originalCode||s.originalCode.trim()===""}function ju(s){return!s.modifiedCode||s.modifiedCode.trim()===""}_e.tokenize=function(s){return s.slice()},_e.join=_e.removeEmpty=function(s){return s};const Nu=100,qu="Too many files changed to display in the diff view. Please review the files in the remote workspace directly to inspect changes.";export{Rt as C,Tu as M,Ru as O,Iu as P,Lt as a,_u as b,wu as c,ju as d,bu as e,Pu as f,Qt as g,Nu as h,Ou as i,qu as j,Lu as p};
