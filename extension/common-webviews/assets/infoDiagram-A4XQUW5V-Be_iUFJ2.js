import{_ as o,l as p,I as a,k as e,K as s}from"./AugmentMessage-DZ4G-ao3.js";import{p as n}from"./gitGraph-YCYPL57B-BSJrB670.js";import"./SpinnerAugment-CQKp6jSN.js";import"./CalloutAugment-ZPisEIAt.js";import"./TextTooltipAugment--NM_J2iY.js";import"./BaseButton-ESlFPUk1.js";import"./IconButtonAugment-D-fvrWAT.js";import"./Content-D7Q35t53.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-ChzPb9WB.js";import"./types-DwxhLPcD.js";import"./chat-types-DOHETl9Q.js";import"./file-paths-BcSg4gks.js";import"./folder-D9ce_3EI.js";import"./github-CwfQWdpa.js";import"./folder-opened-Bb7oiaOR.js";import"./check-DlU29TPV.js";import"./types-DDm27S8B.js";import"./index-C4SxYn1J.js";import"./utils-C8gPzElB.js";import"./ra-diff-ops-model-CRIDwIDf.js";import"./types-CGlLNakm.js";import"./index-DvKVcjj3.js";import"./CardAugment-KjDsYzQv.js";import"./isObjectLike-BYlJR0wA.js";import"./TextAreaAugment-b9B2aQlO.js";import"./diff-utils-Dvc7ppQm.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-csTQmXPq.js";import"./keypress-DD1aQVr0.js";import"./await_block-p2Uc7RoJ.js";import"./CollapseButtonAugment--cD032dy.js";import"./ButtonAugment-CAn8LxGl.js";import"./MaterialIcon-CprIOK2c.js";import"./CopyButton-BT3AalMT.js";import"./magnifying-glass-CXSTSDWT.js";import"./ellipsis-DA5Ek1es.js";import"./IconFilePath-QXTl3raU.js";import"./LanguageIcon-CBQUAmpN.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-BI7GEaM4.js";import"./lodash-8faY21Ia.js";import"./mcp-logo-DrfFqzDb.js";import"./terminal-CB1sTE6C.js";import"./pen-to-square-CIi2Dx1U.js";import"./augment-logo-DhUYorpN.js";import"./_baseUniq-BqQsSpqK.js";import"./_basePickBy-CYHpEGxH.js";import"./clone-CUTyGY41.js";var d={version:s},er={parser:{parse:o(async r=>{const t=await n("info",r);p.debug(t)},"parse")},db:{getVersion:o(()=>d.version,"getVersion")},renderer:{draw:o((r,t,m)=>{p.debug(`rendering info diagram
`+r);const i=a(t);e(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${m}`)},"draw")}};export{er as diagram};
