import{S as V,i as W,s as X,A as Y,e as z,q as Z,t as $,r as D,u as h,h as x,Z as B,a as p,j as E,V as F,ab as f,$ as w,W as j,X as m,Y as g,a0 as A,a1 as S,a2 as q,g as N,a8 as v,aa as G}from"./SpinnerAugment-CQKp6jSN.js";import"./BaseButton-ESlFPUk1.js";function H(a){let n,e;const u=a[9].default,t=w(u,a,a[8],null);let r=[a[1]],c={};for(let i=0;i<r.length;i+=1)c=p(c,r[i]);return{c(){n=j("div"),t&&t.c(),m(n,c),g(n,"svelte-t4hy1o",!0)},m(i,o){z(i,n,o),t&&t.m(n,null),e=!0},p(i,o){t&&t.p&&(!e||256&o)&&A(t,u,i,i[8],e?q(u,i[8],o,null):S(i[8]),null),m(n,c=N(r,[2&o&&i[1]])),g(n,"svelte-t4hy1o",!0)},i(i){e||(h(t,i),e=!0)},o(i){$(t,i),e=!1},d(i){i&&x(n),t&&t.d(i)}}}function I(a){let n,e,u,t;const r=a[9].default,c=w(r,a,a[8],null);let i=[a[1],{role:"button"},{tabindex:"0"}],o={};for(let l=0;l<i.length;l+=1)o=p(o,i[l]);return{c(){n=j("div"),c&&c.c(),m(n,o),g(n,"svelte-t4hy1o",!0)},m(l,d){z(l,n,d),c&&c.m(n,null),e=!0,u||(t=[v(n,"click",a[10]),v(n,"keyup",a[11]),v(n,"keydown",a[12]),v(n,"mousedown",a[13]),v(n,"mouseover",a[14]),v(n,"focus",a[15]),v(n,"mouseleave",a[16]),v(n,"blur",a[17]),v(n,"contextmenu",a[18])],u=!0)},p(l,d){c&&c.p&&(!e||256&d)&&A(c,r,l,l[8],e?q(r,l[8],d,null):S(l[8]),null),m(n,o=N(i,[2&d&&l[1],{role:"button"},{tabindex:"0"}])),g(n,"svelte-t4hy1o",!0)},i(l){e||(h(c,l),e=!0)},o(l){$(c,l),e=!1},d(l){l&&x(n),c&&c.d(l),u=!1,G(t)}}}function J(a){let n,e,u,t;const r=[I,H],c=[];function i(o,l){return o[0]?0:1}return n=i(a),e=c[n]=r[n](a),{c(){e.c(),u=Y()},m(o,l){c[n].m(o,l),z(o,u,l),t=!0},p(o,[l]){let d=n;n=i(o),n===d?c[n].p(o,l):(Z(),$(c[d],1,1,()=>{c[d]=null}),D(),e=c[n],e?e.p(o,l):(e=c[n]=r[n](o),e.c()),h(e,1),e.m(u.parentNode,u))},i(o){t||(h(e),t=!0)},o(o){$(e),t=!1},d(o){o&&x(u),c[n].d(o)}}}function K(a,n,e){let u,t,r;const c=["size","insetContent","variant","interactive","includeBackground"];let i=B(n,c),{$$slots:o={},$$scope:l}=n,{size:d=1}=n,{insetContent:k=!1}=n,{variant:y="surface"}=n,{interactive:b=!1}=n,{includeBackground:C=!0}=n;return a.$$set=s=>{n=p(p({},n),E(s)),e(19,i=B(n,c)),"size"in s&&e(2,d=s.size),"insetContent"in s&&e(3,k=s.insetContent),"variant"in s&&e(4,y=s.variant),"interactive"in s&&e(0,b=s.interactive),"includeBackground"in s&&e(5,C=s.includeBackground),"$$scope"in s&&e(8,l=s.$$scope)},a.$$.update=()=>{e(7,{class:u}=i,u),189&a.$$.dirty&&e(6,t=["c-card",`c-card--size-${d}`,`c-card--${y}`,k?"c-card--insetContent":"",b?"c-card--interactive":"",u,C?"c-card--with-background":""]),64&a.$$.dirty&&e(1,r={...F("accent"),class:t.join(" ")})},[b,r,d,k,y,C,t,u,l,o,function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)}]}class O extends V{constructor(n){super(),W(this,n,K,J,X,{size:2,insetContent:3,variant:4,interactive:0,includeBackground:5})}}export{O as C};
