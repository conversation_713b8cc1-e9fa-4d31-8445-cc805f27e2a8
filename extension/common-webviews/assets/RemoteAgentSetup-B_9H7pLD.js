var Cn=Object.defineProperty;var An=(r,t,n)=>t in r?Cn(r,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[t]=n;var at=(r,t,n)=>An(r,typeof t!="symbol"?t+"":t,n);import{S as ot,i as rt,s as ct,a as et,b as Mt,H as Et,w as Tt,x as Gt,y as Ht,h as b,d as yt,z as Ot,g as Vt,n as M,j as xt,$ as W,W as A,J as P,A as it,c as _,e as y,f as k,a0 as X,a1 as Y,a2 as J,u as m,q as O,t as f,r as V,_ as pe,T as Rt,E as x,F as R,I as L,K as G,M as K,ao as Nt,a8 as Bt,aa as $e,C as vt,D as bt,G as _t,Y as wt,ag as Ft,a6 as te,a5 as un,aj as jt,ab as Wt,ac as Bn,a7 as xe,ax as Xt,N as zt,al as Ct,ah as At,ak as Sn,ae as pn,af as Yt}from"./SpinnerAugment-CQKp6jSN.js";import{C as ee}from"./CalloutAugment-ZPisEIAt.js";import{k as kn}from"./types-DwxhLPcD.js";import{f as Jt}from"./index-CMtlLYew.js";import{B as qt}from"./ButtonAugment-CAn8LxGl.js";import{B as Un,A as $n,a as ce,b as ae}from"./main-panel-7MFJkt6m.js";import{C as mn,G as me,R as dn,T as In}from"./github-CwfQWdpa.js";import{M as de}from"./magnifying-glass-CXSTSDWT.js";import{d as Re,T as fn}from"./Content-D7Q35t53.js";import{G as gn}from"./folder-D9ce_3EI.js";import{e as Qt,u as hn,o as wn}from"./BaseButton-ESlFPUk1.js";import{D as ut,C as Fn,T as Pn}from"./index-DvKVcjj3.js";import{C as Dn}from"./CopyButton-BT3AalMT.js";import{R as ne}from"./check-DlU29TPV.js";import{T as It}from"./TextTooltipAugment--NM_J2iY.js";import{I as se}from"./IconButtonAugment-D-fvrWAT.js";import{A as Nn}from"./arrow-up-right-from-square-ChzPb9WB.js";import{T as vn}from"./terminal-CB1sTE6C.js";import{P as zn}from"./pen-to-square-CIi2Dx1U.js";import{T as bn}from"./Keybindings-CmEJIsef.js";import{R as Le}from"./types-DDm27S8B.js";import{E as Mn}from"./exclamation-triangle-CEPjk4z2.js";import{T as En}from"./StatusIndicator-CVSFyrPR.js";import"./chat-types-DOHETl9Q.js";import"./trash-can-YBGqp3xH.js";import"./design-system-init-D-hN7xfd.js";import"./ra-diff-ops-model-CRIDwIDf.js";import"./diff-utils-Dvc7ppQm.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-csTQmXPq.js";import"./index-C4SxYn1J.js";import"./isObjectLike-BYlJR0wA.js";import"./globals-D0QH3NT1.js";import"./await_block-p2Uc7RoJ.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-BI7GEaM4.js";import"./folder-opened-Bb7oiaOR.js";import"./CollapseButtonAugment--cD032dy.js";import"./ellipsis-DA5Ek1es.js";import"./MaterialIcon-CprIOK2c.js";import"./utils-C8gPzElB.js";import"./VSCodeCodicon-CONIBlZ6.js";import"./autofix-state-d-ymFdyn.js";import"./types-CGlLNakm.js";import"./augment-logo-DhUYorpN.js";import"./chat-flags-model-BhsWla-l.js";import"./TextAreaAugment-b9B2aQlO.js";import"./CardAugment-KjDsYzQv.js";function ie(r){const t=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(t)return{owner:t[1],name:t[2]}}function _n(r){const t=r.match(/^[^/]+\/HEAD\s*->\s*(.+)$/);if(t)return _n(t[1]);const n=function(e){const s=e.match(/^refs\/remotes\/([^/]+)\/(.+)$/);if(s)return{remote:s[1],branch:s[2]};const o=e.match(/^([^/]+)\/(.+)$/);if(o){const[,c,i]=o;if(["origin","upstream","fork","github","gitlab","bitbucket"].includes(c)||c.includes("."))return{remote:c,branch:i}}return null}(r);return n?n.branch:r}function Tn(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=et(s,e[o]);return{c(){t=Mt("svg"),n=new Et(!0),this.h()},l(o){t=Tt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Gt(t);n=Ht(c,!0),c.forEach(b),this.h()},h(){n.a=null,yt(t,s)},m(o,c){Ot(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',t)},p(o,[c]){yt(t,s=Vt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function Gn(r,t,n){return r.$$set=e=>{n(0,t=et(et({},t),xt(e)))},[t=xt(t)]}class Hn extends ot{constructor(t){super(),rt(this,t,Gn,Tn,ct,{})}}function On(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=et(s,e[o]);return{c(){t=Mt("svg"),n=new Et(!0),this.h()},l(o){t=Tt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Gt(t);n=Ht(c,!0),c.forEach(b),this.h()},h(){n.a=null,yt(t,s)},m(o,c){Ot(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 465c9.4 9.4 24.6 9.4 33.9 0L465 273c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9zM47 81l192 192c9.4 9.4 24.6 9.4 33.9 0L465 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',t)},p(o,[c]){yt(t,s=Vt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function Vn(r,t,n){return r.$$set=e=>{n(0,t=et(et({},t),xt(e)))},[t=xt(t)]}class jn extends ot{constructor(t){super(),rt(this,t,Vn,On,ct,{})}}const qn=r=>({}),Ce=r=>({}),Kn=r=>({}),Ae=r=>({}),Wn=r=>({}),Be=r=>({}),Xn=r=>({}),Se=r=>({});function Yn(r){let t;return{c(){t=G(r[0])},m(n,e){y(n,t,e)},p(n,e){1&e&&K(t,n[0])},d(n){n&&b(t)}}}function ke(r){let t,n;const e=r[3].subtitle,s=W(e,r,r[4],Ae),o=s||function(c){let i,a;return i=new Rt({props:{size:2,$$slots:{default:[Jn]},$$scope:{ctx:c}}}),{c(){x(i.$$.fragment)},m(l,u){R(i,l,u),a=!0},p(l,u){const d={};18&u&&(d.$$scope={dirty:u,ctx:l}),i.$set(d)},i(l){a||(m(i.$$.fragment,l),a=!0)},o(l){f(i.$$.fragment,l),a=!1},d(l){L(i,l)}}}(r);return{c(){t=A("div"),o&&o.c(),_(t,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){y(c,t,i),o&&o.m(t,null),n=!0},p(c,i){s?s.p&&(!n||16&i)&&X(s,e,c,c[4],n?J(e,c[4],i,Kn):Y(c[4]),Ae):o&&o.p&&(!n||2&i)&&o.p(c,n?i:-1)},i(c){n||(m(o,c),n=!0)},o(c){f(o,c),n=!1},d(c){c&&b(t),o&&o.d(c)}}}function Jn(r){let t;return{c(){t=G(r[1])},m(n,e){y(n,t,e)},p(n,e){2&e&&K(t,n[1])},d(n){n&&b(t)}}}function Ue(r){let t,n;const e=r[3].iconRight,s=W(e,r,r[4],Ce);return{c(){t=A("div"),s&&s.c(),_(t,"class","c-card-button__icon-right svelte-z367s9")},m(o,c){y(o,t,c),s&&s.m(t,null),n=!0},p(o,c){s&&s.p&&(!n||16&c)&&X(s,e,o,o[4],n?J(e,o[4],c,qn):Y(o[4]),Ce)},i(o){n||(m(s,o),n=!0)},o(o){f(s,o),n=!1},d(o){o&&b(t),s&&s.d(o)}}}function Qn(r){let t,n,e,s,o,c,i,a;const l=r[3].iconLeft,u=W(l,r,r[4],Se),d=r[3].title,w=W(d,r,r[4],Be),g=w||function($){let v,B;return v=new Rt({props:{size:2,$$slots:{default:[Yn]},$$scope:{ctx:$}}}),{c(){x(v.$$.fragment)},m(F,E){R(v,F,E),B=!0},p(F,E){const I={};17&E&&(I.$$scope={dirty:E,ctx:F}),v.$set(I)},i(F){B||(m(v.$$.fragment,F),B=!0)},o(F){f(v.$$.fragment,F),B=!1},d(F){L(v,F)}}}(r);let h=r[1]&&ke(r),p=r[2].iconRight&&Ue(r);return{c(){t=A("div"),u&&u.c(),n=P(),e=A("div"),s=A("div"),g&&g.c(),o=P(),h&&h.c(),c=P(),p&&p.c(),i=it(),_(t,"class","c-card-button__icon-left svelte-z367s9"),_(s,"class","c-card-button__title svelte-z367s9"),_(e,"class","c-card-button__content svelte-z367s9")},m($,v){y($,t,v),u&&u.m(t,null),y($,n,v),y($,e,v),k(e,s),g&&g.m(s,null),k(e,o),h&&h.m(e,null),y($,c,v),p&&p.m($,v),y($,i,v),a=!0},p($,[v]){u&&u.p&&(!a||16&v)&&X(u,l,$,$[4],a?J(l,$[4],v,Xn):Y($[4]),Se),w?w.p&&(!a||16&v)&&X(w,d,$,$[4],a?J(d,$[4],v,Wn):Y($[4]),Be):g&&g.p&&(!a||1&v)&&g.p($,a?v:-1),$[1]?h?(h.p($,v),2&v&&m(h,1)):(h=ke($),h.c(),m(h,1),h.m(e,null)):h&&(O(),f(h,1,1,()=>{h=null}),V()),$[2].iconRight?p?(p.p($,v),4&v&&m(p,1)):(p=Ue($),p.c(),m(p,1),p.m(i.parentNode,i)):p&&(O(),f(p,1,1,()=>{p=null}),V())},i($){a||(m(u,$),m(g,$),m(h),m(p),a=!0)},o($){f(u,$),f(g,$),f(h),f(p),a=!1},d($){$&&(b(t),b(n),b(e),b(c),b(i)),u&&u.d($),g&&g.d($),h&&h.d(),p&&p.d($)}}}function Zn(r,t,n){let{$$slots:e={},$$scope:s}=t;const o=pe(e);let{title:c="Select an option"}=t,{subtitle:i=""}=t;return r.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,s=a.$$scope)},[c,i,o,e,s]}class yn extends ot{constructor(t){super(),rt(this,t,Zn,Qn,ct,{title:0,subtitle:1})}}const ts=r=>({}),Ie=r=>({slot:"iconLeft"}),es=r=>({}),Fe=r=>({slot:"iconRight"});function Pe(r,t,n){const e=r.slice();return e[19]=t[n],e}const ns=r=>({}),De=r=>({}),ss=r=>({}),Ne=r=>({}),os=r=>({}),ze=r=>({slot:"iconLeft"}),rs=r=>({}),Me=r=>({slot:"title"}),cs=r=>({}),Ee=r=>({slot:"iconRight"});function as(r){let t,n,e,s,o;return n=new yn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[us],iconLeft:[ls]},$$scope:{ctx:r}}}),{c(){t=A("button"),x(n.$$.fragment),_(t,"class","c-card-button__display svelte-1km5ln2"),_(t,"type","button"),t.disabled=r[10]},m(c,i){y(c,t,i),R(n,t,null),e=!0,s||(o=[Bt(t,"click",r[16]),Bt(t,"keydown",r[17])],s=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!e||1024&i)&&(t.disabled=c[10])},i(c){e||(m(n.$$.fragment,c),e=!0)},o(c){f(n.$$.fragment,c),e=!1},d(c){c&&b(t),L(n),s=!1,$e(o)}}}function is(r){let t,n,e;function s(c){r[15](c)}let o={onOpenChange:r[9],$$slots:{default:[bs]},$$scope:{ctx:r}};return r[1]!==void 0&&(o.requestClose=r[1]),t=new ut.Root({props:o}),vt.push(()=>bt(t,"requestClose",s)),{c(){x(t.$$.fragment)},m(c,i){R(t,c,i),e=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],_t(()=>n=!1)),t.$set(a)},i(c){e||(m(t.$$.fragment,c),e=!0)},o(c){f(t.$$.fragment,c),e=!1},d(c){L(t,c)}}}function ls(r){let t;const n=r[13].iconLeft,e=W(n,r,r[18],Ie);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||262144&o)&&X(e,n,s,s[18],t?J(n,s[18],o,ts):Y(s[18]),Ie)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function us(r){let t;const n=r[13].iconRight,e=W(n,r,r[18],Fe);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||262144&o)&&X(e,n,s,s[18],t?J(n,s[18],o,es):Y(s[18]),Fe)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function ps(r){let t;const n=r[13].iconLeft,e=W(n,r,r[18],ze);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||262144&o)&&X(e,n,s,s[18],t?J(n,s[18],o,os):Y(s[18]),ze)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function $s(r){let t;const n=r[13].title,e=W(n,r,r[18],Me),s=e||function(o){let c;return{c(){c=G(o[3])},m(i,a){y(i,c,a)},p(i,a){8&a&&K(c,i[3])},d(i){i&&b(c)}}}(r);return{c(){s&&s.c()},m(o,c){s&&s.m(o,c),t=!0},p(o,c){e?e.p&&(!t||262144&c)&&X(e,n,o,o[18],t?J(n,o[18],c,rs):Y(o[18]),Me):s&&s.p&&(!t||8&c)&&s.p(o,t?c:-1)},i(o){t||(m(s,o),t=!0)},o(o){f(s,o),t=!1},d(o){s&&s.d(o)}}}function ms(r){let t;const n=r[13].iconRight,e=W(n,r,r[18],Ee),s=e||function(o){let c,i;return c=new mn({}),{c(){x(c.$$.fragment)},m(a,l){R(c,a,l),i=!0},i(a){i||(m(c.$$.fragment,a),i=!0)},o(a){f(c.$$.fragment,a),i=!1},d(a){L(c,a)}}}();return{c(){s&&s.c()},m(o,c){s&&s.m(o,c),t=!0},p(o,c){e&&e.p&&(!t||262144&c)&&X(e,n,o,o[18],t?J(n,o[18],c,cs):Y(o[18]),Ee)},i(o){t||(m(s,o),t=!0)},o(o){f(s,o),t=!1},d(o){s&&s.d(o)}}}function ds(r){let t,n,e,s;return n=new yn({props:{subtitle:r[4],$$slots:{iconRight:[ms],title:[$s],iconLeft:[ps]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"class","c-card-button__display svelte-1km5ln2"),_(t,"role","button"),_(t,"tabindex",e=r[10]?-1:0),wt(t,"disabled",r[10])},m(o,c){y(o,t,c),R(n,t,null),s=!0},p(o,c){const i={};16&c&&(i.subtitle=o[4]),262152&c&&(i.$$scope={dirty:c,ctx:o}),n.$set(i),(!s||1024&c&&e!==(e=o[10]?-1:0))&&_(t,"tabindex",e),(!s||1024&c)&&wt(t,"disabled",o[10])},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){f(n.$$.fragment,o),s=!1},d(o){o&&b(t),L(n)}}}function fs(r){let t,n;return t=new ut.Label({props:{$$slots:{default:[hs]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};262400&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function gs(r){let t,n,e=[],s=new Map,o=Qt(r[6]);const c=i=>i[7](i[19]);for(let i=0;i<o.length;i+=1){let a=Pe(r,o,i),l=c(a);s.set(l,e[i]=Te(l,a))}return{c(){for(let i=0;i<e.length;i+=1)e[i].c();t=it()},m(i,a){for(let l=0;l<e.length;l+=1)e[l]&&e[l].m(i,a);y(i,t,a),n=!0},p(i,a){2241&a&&(o=Qt(i[6]),O(),e=hn(e,a,c,1,i,o,s,t.parentNode,wn,Te,t,Pe),V())},i(i){if(!n){for(let a=0;a<o.length;a+=1)m(e[a]);n=!0}},o(i){for(let a=0;a<e.length;a+=1)f(e[a]);n=!1},d(i){i&&b(t);for(let a=0;a<e.length;a+=1)e[a].d(i)}}}function hs(r){let t;return{c(){t=G(r[8])},m(n,e){y(n,t,e)},p(n,e){256&e&&K(t,n[8])},d(n){n&&b(t)}}}function ws(r){let t,n,e=r[7](r[19])+"";return{c(){t=G(e),n=P()},m(s,o){y(s,t,o),y(s,n,o)},p(s,o){192&o&&e!==(e=s[7](s[19])+"")&&K(t,e)},d(s){s&&(b(t),b(n))}}}function Te(r,t){let n,e,s;function o(){return t[14](t[19])}return e=new ut.Item({props:{onSelect:o,highlight:t[0]===t[19],$$slots:{default:[ws]},$$scope:{ctx:t}}}),{key:r,first:null,c(){n=it(),x(e.$$.fragment),this.first=n},m(c,i){y(c,n,i),R(e,c,i),s=!0},p(c,i){t=c;const a={};64&i&&(a.onSelect=o),65&i&&(a.highlight=t[0]===t[19]),262336&i&&(a.$$scope={dirty:i,ctx:t}),e.$set(a)},i(c){s||(m(e.$$.fragment,c),s=!0)},o(c){f(e.$$.fragment,c),s=!1},d(c){c&&b(n),L(e,c)}}}function vs(r){let t,n,e,s,o;const c=r[13]["dropdown-top"],i=W(c,r,r[18],Ne),a=r[13]["dropdown-content"],l=W(a,r,r[18],De),u=l||function(d){let w,g,h,p;const $=[gs,fs],v=[];function B(F,E){return F[6].length>0?0:1}return w=B(d),g=v[w]=$[w](d),{c(){g.c(),h=it()},m(F,E){v[w].m(F,E),y(F,h,E),p=!0},p(F,E){let I=w;w=B(F),w===I?v[w].p(F,E):(O(),f(v[I],1,1,()=>{v[I]=null}),V(),g=v[w],g?g.p(F,E):(g=v[w]=$[w](F),g.c()),m(g,1),g.m(h.parentNode,h))},i(F){p||(m(g),p=!0)},o(F){f(g),p=!1},d(F){F&&b(h),v[w].d(F)}}}(r);return{c(){t=A("div"),n=A("div"),i&&i.c(),e=P(),s=A("div"),u&&u.c(),_(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),_(s,"class","c-card-button__dropdown-content svelte-1km5ln2"),_(t,"class","c-card__dropdown-contents svelte-1km5ln2")},m(d,w){y(d,t,w),k(t,n),i&&i.m(n,null),k(t,e),k(t,s),u&&u.m(s,null),o=!0},p(d,w){i&&i.p&&(!o||262144&w)&&X(i,c,d,d[18],o?J(c,d[18],w,ss):Y(d[18]),Ne),l?l.p&&(!o||262144&w)&&X(l,a,d,d[18],o?J(a,d[18],w,ns):Y(d[18]),De):u&&u.p&&(!o||449&w)&&u.p(d,o?w:-1)},i(d){o||(m(i,d),m(u,d),o=!0)},o(d){f(i,d),f(u,d),o=!1},d(d){d&&b(t),i&&i.d(d),u&&u.d(d)}}}function bs(r){let t,n,e,s;return t=new ut.Trigger({props:{$$slots:{default:[ds]},$$scope:{ctx:r}}}),e=new ut.Content({props:{align:"start",side:"bottom",$$slots:{default:[vs]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),n=P(),x(e.$$.fragment)},m(o,c){R(t,o,c),y(o,n,c),R(e,o,c),s=!0},p(o,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:o}),t.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:o}),e.$set(a)},i(o){s||(m(t.$$.fragment,o),m(e.$$.fragment,o),s=!0)},o(o){f(t.$$.fragment,o),f(e.$$.fragment,o),s=!1},d(o){o&&b(n),L(t,o),L(e,o)}}}function _s(r){let t,n,e,s;const o=[is,as],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(r),e=c[n]=o[n](r),{c(){t=A("div"),e.c(),_(t,"class","c-card-button svelte-1km5ln2")},m(a,l){y(a,t,l),c[n].m(t,null),s=!0},p(a,[l]){let u=n;n=i(a),n===u?c[n].p(a,l):(O(),f(c[u],1,1,()=>{c[u]=null}),V(),e=c[n],e?e.p(a,l):(e=c[n]=o[n](a),e.c()),m(e,1),e.m(t,null))},i(a){s||(m(e),s=!0)},o(a){f(e),s=!1},d(a){a&&b(t),c[n].d()}}}function ys(r,t,n){let{$$slots:e={},$$scope:s}=t,{type:o="button"}=t,{title:c="Select an option"}=t,{subtitle:i=""}=t,{onClick:a=()=>{}}=t,{items:l=[]}=t,{selectedItem:u}=t,{formatItemLabel:d=B=>(B==null?void 0:B.toString())||""}=t,{noItemsLabel:w="No items found"}=t,{onDropdownOpenChange:g=()=>{}}=t,{requestClose:h=()=>{}}=t,{disabled:p=!1}=t;function $(B){n(0,u=B),v("select",B)}const v=Nt();return r.$$set=B=>{"type"in B&&n(2,o=B.type),"title"in B&&n(3,c=B.title),"subtitle"in B&&n(4,i=B.subtitle),"onClick"in B&&n(5,a=B.onClick),"items"in B&&n(6,l=B.items),"selectedItem"in B&&n(0,u=B.selectedItem),"formatItemLabel"in B&&n(7,d=B.formatItemLabel),"noItemsLabel"in B&&n(8,w=B.noItemsLabel),"onDropdownOpenChange"in B&&n(9,g=B.onDropdownOpenChange),"requestClose"in B&&n(1,h=B.requestClose),"disabled"in B&&n(10,p=B.disabled),"$$scope"in B&&n(18,s=B.$$scope)},[u,h,o,c,i,a,l,d,w,g,p,$,v,e,B=>$(B),function(B){h=B,n(1,h)},()=>{a(),v("click")},B=>{B.key!=="Enter"&&B.key!==" "||(a(),v("click"))},s]}class xn extends ot{constructor(t){super(),rt(this,t,ys,_s,ct,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function xs(r){let t,n;return t=new xn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Us],iconLeft:[Ls]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};16388&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Rs(r){let t,n,e,s;t=new xn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[6],$$slots:{iconRight:[Ds],iconLeft:[Is]},$$scope:{ctx:r}}});let o=r[3]&&Ge(r);return{c(){x(t.$$.fragment),n=P(),o&&o.c(),e=it()},m(c,i){R(t,c,i),y(c,n,i),o&&o.m(c,i),y(c,e,i),s=!0},p(c,i){const a={};2&i&&(a.title=c[1]?"Cancel":"Connect to GitHub"),16386&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a),c[3]?o?(o.p(c,i),8&i&&m(o,1)):(o=Ge(c),o.c(),m(o,1),o.m(e.parentNode,e)):o&&(O(),f(o,1,1,()=>{o=null}),V())},i(c){s||(m(t.$$.fragment,c),m(o),s=!0)},o(c){f(t.$$.fragment,c),f(o),s=!1},d(c){c&&(b(n),b(e)),L(t,c),o&&o.d(c)}}}function Ls(r){let t,n;return t=new me({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Cs(r){let t,n;return t=new Rt({props:{size:1,weight:"medium",$$slots:{default:[Bs]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function As(r){let t,n,e,s;return t=new jt({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),e=new Rt({props:{size:1,weight:"medium",$$slots:{default:[Ss]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),n=P(),x(e.$$.fragment)},m(o,c){R(t,o,c),y(o,n,c),R(e,o,c),s=!0},i(o){s||(m(t.$$.fragment,o),m(e.$$.fragment,o),s=!0)},o(o){f(t.$$.fragment,o),f(e.$$.fragment,o),s=!1},d(o){o&&b(n),L(t,o),L(e,o)}}}function Bs(r){let t;return{c(){t=G("Revoke Access")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function Ss(r){let t;return{c(){t=G("Revoking...")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function ks(r){let t,n,e,s;const o=[As,Cs],c=[];function i(a,l){return a[2]?0:1}return t=i(r),n=c[t]=o[t](r),{c(){n.c(),e=it()},m(a,l){c[t].m(a,l),y(a,e,l),s=!0},p(a,l){let u=t;t=i(a),t!==u&&(O(),f(c[u],1,1,()=>{c[u]=null}),V(),n=c[t],n||(n=c[t]=o[t](a),n.c()),m(n,1),n.m(e.parentNode,e))},i(a){s||(m(n),s=!0)},o(a){f(n),s=!1},d(a){a&&b(e),c[t].d(a)}}}function Us(r){let t,n,e;return n=new ut.Item({props:{color:"error",onSelect:r[8],$$slots:{default:[ks]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"slot","dropdown-content")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p(s,o){const c={};16388&o&&(c.$$scope={dirty:o,ctx:s}),n.$set(c)},i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function Is(r){let t,n;return t=new me({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Fs(r){let t,n;return t=new Fn({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ps(r){let t,n;return t=new jt({props:{size:1,useCurrentColor:!0}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ds(r){let t,n,e,s;const o=[Ps,Fs],c=[];function i(a,l){return a[1]?0:1}return n=i(r),e=c[n]=o[n](r),{c(){t=A("div"),e.c(),_(t,"slot","iconRight")},m(a,l){y(a,t,l),c[n].m(t,null),s=!0},p(a,l){let u=n;n=i(a),n!==u&&(O(),f(c[u],1,1,()=>{c[u]=null}),V(),e=c[n],e||(e=c[n]=o[n](a),e.c()),m(e,1),e.m(t,null))},i(a){s||(m(e),s=!0)},o(a){f(e),s=!1},d(a){a&&b(t),c[n].d()}}}function Ge(r){let t,n,e,s,o,c;n=new Rt({props:{size:1,$$slots:{default:[Ns]},$$scope:{ctx:r}}});let i=r[4]&&He(r);return o=new Rt({props:{size:1,$$slots:{default:[Es]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),e=P(),i&&i.c(),s=P(),x(o.$$.fragment),_(t,"class","github-auth-error svelte-c4wp0y")},m(a,l){y(a,t,l),R(n,t,null),k(t,e),i&&i.m(t,null),k(t,s),R(o,t,null),c=!0},p(a,l){const u={};16384&l&&(u.$$scope={dirty:l,ctx:a}),n.$set(u),a[4]?i?(i.p(a,l),16&l&&m(i,1)):(i=He(a),i.c(),m(i,1),i.m(t,s)):i&&(O(),f(i,1,1,()=>{i=null}),V());const d={};16392&l&&(d.$$scope={dirty:l,ctx:a}),o.$set(d)},i(a){c||(m(n.$$.fragment,a),m(i),m(o.$$.fragment,a),c=!0)},o(a){f(n.$$.fragment,a),f(i),f(o.$$.fragment,a),c=!1},d(a){a&&b(t),L(n),i&&i.d(),L(o)}}}function Ns(r){let t;return{c(){t=G("An error occurred while authenticating with GitHub.")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function He(r){let t,n,e,s,o,c,i;return t=new Rt({props:{size:1,$$slots:{default:[zs]},$$scope:{ctx:r}}}),s=new Rt({props:{size:1,class:"github-auth-error-url",$$slots:{default:[Ms]},$$scope:{ctx:r}}}),c=new Dn({props:{text:r[4],tooltipNested:!1}}),{c(){x(t.$$.fragment),n=P(),e=A("div"),x(s.$$.fragment),o=P(),x(c.$$.fragment),_(e,"class","github-auth-error-url-container svelte-c4wp0y")},m(a,l){R(t,a,l),y(a,n,l),y(a,e,l),R(s,e,null),k(e,o),R(c,e,null),i=!0},p(a,l){const u={};16384&l&&(u.$$scope={dirty:l,ctx:a}),t.$set(u);const d={};16400&l&&(d.$$scope={dirty:l,ctx:a}),s.$set(d);const w={};16&l&&(w.text=a[4]),c.$set(w)},i(a){i||(m(t.$$.fragment,a),m(s.$$.fragment,a),m(c.$$.fragment,a),i=!0)},o(a){f(t.$$.fragment,a),f(s.$$.fragment,a),f(c.$$.fragment,a),i=!1},d(a){a&&(b(n),b(e)),L(t,a),L(s),L(c)}}}function zs(r){let t;return{c(){t=G("Visit or copy the following URL in your browser to authenticate manually:")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function Ms(r){let t,n;return{c(){t=A("a"),n=G(r[4]),_(t,"href",r[4]),_(t,"target","_blank")},m(e,s){y(e,t,s),k(t,n)},p(e,s){16&s&&K(n,e[4]),16&s&&_(t,"href",e[4])},d(e){e&&b(t)}}}function Es(r){let t;return{c(){t=G(r[3])},m(n,e){y(n,t,e)},p(n,e){8&e&&K(t,n[3])},d(n){n&&b(t)}}}function Ts(r){let t,n,e,s,o;const c=[Rs,xs],i=[];function a(l,u){return l[0]?1:0}return e=a(r),s=i[e]=c[e](r),{c(){t=A("div"),n=A("div"),s.c(),_(n,"class","github-auth-button"),_(t,"class","github-auth-card svelte-c4wp0y")},m(l,u){y(l,t,u),k(t,n),i[e].m(n,null),o=!0},p(l,[u]){let d=e;e=a(l),e===d?i[e].p(l,u):(O(),f(i[d],1,1,()=>{i[d]=null}),V(),s=i[e],s?s.p(l,u):(s=i[e]=c[e](l),s.c()),m(s,1),s.m(n,null))},i(l){o||(m(s),o=!0)},o(l){f(s),o=!1},d(l){l&&b(t),i[e].d()}}}function Gs(r,t,n){const e=Nt(),s=Ft(gn.key);let o,c,i=!1,a=!1,l=!1,u=null,d=null;async function w(){if(!l){n(2,l=!0);try{const g=await s.revokeGithubAccess();g.success?(n(0,i=!1),e("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",g.message)}catch(g){console.error("Error revoking GitHub access:",g)}finally{n(2,l=!1)}}}return te(async()=>{await async function(){try{const g=await s.isGithubAuthenticated();g!==i?(n(0,i=g),e("authStateChange",{isAuthenticated:i})):n(0,i=g)}catch(g){console.error("Failed to check GitHub authentication status:",g),n(0,i=!1),e("authStateChange",{isAuthenticated:!1})}}()}),un(()=>{u&&(clearTimeout(u),u=null),d&&(clearInterval(d),d=null)}),[i,a,l,o,c,()=>{},async function(){if(n(3,o=void 0),n(4,c=void 0),a)return n(1,a=!1),void(u&&(clearTimeout(u),u=null));n(1,a=!0);try{const{success:g,message:h,url:p}=await s.authenticateGithub();if(!g)throw n(4,c=p),new Error(h);d=setInterval(async()=>{try{await s.isGithubAuthenticated()&&(n(0,i=!0),n(1,a=!1),e("authStateChange",{isAuthenticated:!0}),d&&clearInterval(d),u&&(clearTimeout(u),u=null))}catch($){console.error("Failed to check GitHub authentication status:",$)}},5e3),u=setTimeout(()=>{d&&clearInterval(d),n(1,a=!1),u=null},6e4)}catch(g){console.error("Failed to authenticate with GitHub:",g),n(3,o=`Error: ${g instanceof Error?g.message:String(g)}`),n(1,a=!1)}},w,()=>{w()}]}class Hs extends ot{constructor(t){super(),rt(this,t,Gs,Ts,ct,{})}}const Os=r=>({}),Oe=r=>({});function Ve(r,t,n){const e=r.slice();return e[27]=t[n],e[29]=n,e}const Vs=r=>({item:64&r}),je=r=>({item:r[27]}),js=r=>({}),qe=r=>({}),qs=r=>({}),Ke=r=>({}),Ks=r=>({}),We=r=>({}),Ws=r=>({}),Xe=r=>({}),Xs=r=>({}),Ye=r=>({});function Ys(r){let t,n,e,s,o,c,i,a,l,u,d,w,g,h;const p=[Zs,Qs],$=[];function v(I,z){return I[4]?0:1}s=v(r),o=$[s]=p[s](r);const B=[eo,to],F=[];function E(I,z){return I[17].title?0:1}return a=E(r),l=F[a]=B[a](r),w=new mn({}),{c(){t=A("div"),n=A("div"),e=A("div"),o.c(),c=P(),i=A("span"),l.c(),u=P(),d=A("div"),x(w.$$.fragment),_(e,"class","c-searchable-dropdown__icon svelte-145zgu0"),_(i,"class","c-searchable-dropdown__button-text svelte-145zgu0"),_(n,"class","c-searchable-dropdown__icon-text svelte-145zgu0"),_(d,"class","c-searchable-dropdown__chevron svelte-145zgu0"),_(t,"class","c-searchable-dropdown__button svelte-145zgu0"),_(t,"role","button"),_(t,"tabindex",g=r[5]?-1:0),wt(t,"c-searchable-dropdown__button--disabled",r[5])},m(I,z){y(I,t,z),k(t,n),k(n,e),$[s].m(e,null),k(n,c),k(n,i),F[a].m(i,null),k(t,u),k(t,d),R(w,d,null),h=!0},p(I,z){let U=s;s=v(I),s===U?$[s].p(I,z):(O(),f($[U],1,1,()=>{$[U]=null}),V(),o=$[s],o?o.p(I,z):(o=$[s]=p[s](I),o.c()),m(o,1),o.m(e,null));let tt=a;a=E(I),a===tt?F[a].p(I,z):(O(),f(F[tt],1,1,()=>{F[tt]=null}),V(),l=F[a],l?l.p(I,z):(l=F[a]=B[a](I),l.c()),m(l,1),l.m(i,null)),(!h||32&z&&g!==(g=I[5]?-1:0))&&_(t,"tabindex",g),(!h||32&z)&&wt(t,"c-searchable-dropdown__button--disabled",I[5])},i(I){h||(m(o),m(l),m(w.$$.fragment,I),h=!0)},o(I){f(o),f(l),f(w.$$.fragment,I),h=!1},d(I){I&&b(t),$[s].d(),F[a].d(),L(w)}}}function Js(r){let t,n,e,s,o,c,i,a;const l=r[18].searchIcon,u=W(l,r,r[25],Ye),d=u||function(g){let h;const p=g[18].icon,$=W(p,g,g[25],Xe);return{c(){$&&$.c()},m(v,B){$&&$.m(v,B),h=!0},p(v,B){$&&$.p&&(!h||33554432&B)&&X($,p,v,v[25],h?J(p,v[25],B,Ws):Y(v[25]),Xe)},i(v){h||(m($,v),h=!0)},o(v){f($,v),h=!1},d(v){$&&$.d(v)}}}(r);let w=r[17].inputButton&&Je(r);return{c(){t=A("div"),n=A("div"),d&&d.c(),e=P(),s=A("input"),o=P(),w&&w.c(),_(n,"class","c-searchable-dropdown__icon svelte-145zgu0"),_(s,"type","text"),_(s,"class","c-searchable-dropdown__trigger-input svelte-145zgu0"),_(s,"placeholder",r[3]),_(t,"class","c-searchable-dropdown__input-container svelte-145zgu0")},m(g,h){y(g,t,h),k(t,n),d&&d.m(n,null),k(t,e),k(t,s),xe(s,r[0]),k(t,o),w&&w.m(t,null),c=!0,i||(a=[Bt(s,"input",r[21]),Bt(s,"input",r[22]),Bt(s,"click",Xt(r[19])),Bt(s,"mousedown",Xt(r[20]))],i=!0)},p(g,h){u?u.p&&(!c||33554432&h)&&X(u,l,g,g[25],c?J(l,g[25],h,Xs):Y(g[25]),Ye):d&&d.p&&(!c||33554432&h)&&d.p(g,c?h:-1),(!c||8&h)&&_(s,"placeholder",g[3]),1&h&&s.value!==g[0]&&xe(s,g[0]),g[17].inputButton?w?(w.p(g,h),131072&h&&m(w,1)):(w=Je(g),w.c(),m(w,1),w.m(t,null)):w&&(O(),f(w,1,1,()=>{w=null}),V())},i(g){c||(m(d,g),m(w),c=!0)},o(g){f(d,g),f(w),c=!1},d(g){g&&b(t),d&&d.d(g),w&&w.d(),i=!1,$e(a)}}}function Qs(r){let t;const n=r[18].icon,e=W(n,r,r[25],Ke);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||33554432&o)&&X(e,n,s,s[25],t?J(n,s[25],o,qs):Y(s[25]),Ke)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function Zs(r){let t,n;return t=new jt({props:{size:1,useCurrentColor:!0}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function to(r){let t,n=(r[4]?r[11]:r[2])+"";return{c(){t=G(n)},m(e,s){y(e,t,s)},p(e,s){2068&s&&n!==(n=(e[4]?e[11]:e[2])+"")&&K(t,n)},i:M,o:M,d(e){e&&b(t)}}}function eo(r){let t;const n=r[18].title,e=W(n,r,r[25],qe);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||33554432&o)&&X(e,n,s,s[25],t?J(n,s[25],o,js):Y(s[25]),qe)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function Je(r){let t;const n=r[18].inputButton,e=W(n,r,r[25],We);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||33554432&o)&&X(e,n,s,s[25],t?J(n,s[25],o,Ks):Y(s[25]),We)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function no(r){let t,n,e,s;const o=[Js,Ys],c=[];function i(a,l){return a[12]?0:1}return t=i(r),n=c[t]=o[t](r),{c(){n.c(),e=it()},m(a,l){c[t].m(a,l),y(a,e,l),s=!0},p(a,l){let u=t;t=i(a),t===u?c[t].p(a,l):(O(),f(c[u],1,1,()=>{c[u]=null}),V(),n=c[t],n?n.p(a,l):(n=c[t]=o[t](a),n.c()),m(n,1),n.m(e.parentNode,e))},i(a){s||(m(n),s=!0)},o(a){f(n),s=!1},d(a){a&&b(e),c[t].d(a)}}}function Qe(r){let t,n;return t=new ut.Content({props:{side:"bottom",align:"start",$$slots:{default:[lo]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};33689298&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function so(r){let t,n;return t=new ut.Item({props:{disabled:!0,$$slots:{default:[co]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};33555456&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function oo(r){let t,n,e=[],s=new Map,o=Qt(r[6]);const c=i=>i[27]===null?`null-item-${i[29]}`:i[8](i[27],i[29]);for(let i=0;i<o.length;i+=1){let a=Ve(r,o,i),l=c(a);s.set(l,e[i]=Ze(l,a))}return{c(){for(let i=0;i<e.length;i+=1)e[i].c();t=it()},m(i,a){for(let l=0;l<e.length;l+=1)e[l]&&e[l].m(i,a);y(i,t,a),n=!0},p(i,a){33620930&a&&(o=Qt(i[6]),O(),e=hn(e,a,c,1,i,o,s,t.parentNode,wn,Ze,t,Ve),V())},i(i){if(!n){for(let a=0;a<o.length;a+=1)m(e[a]);n=!0}},o(i){for(let a=0;a<e.length;a+=1)f(e[a]);n=!1},d(i){i&&b(t);for(let a=0;a<e.length;a+=1)e[a].d(i)}}}function ro(r){let t,n;return t=new ut.Item({props:{disabled:!0,$$slots:{default:[io]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};33556480&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function co(r){let t;return{c(){t=G(r[10])},m(n,e){y(n,t,e)},p(n,e){1024&e&&K(t,n[10])},d(n){n&&b(t)}}}function ao(r){let t,n;const e=r[18].item,s=W(e,r,r[25],je),o=s||function(c){let i,a=c[7](c[27])+"";return{c(){i=G(a)},m(l,u){y(l,i,u)},p(l,u){192&u&&a!==(a=l[7](l[27])+"")&&K(i,a)},d(l){l&&b(i)}}}(r);return{c(){o&&o.c(),t=P()},m(c,i){o&&o.m(c,i),y(c,t,i),n=!0},p(c,i){s?s.p&&(!n||33554496&i)&&X(s,e,c,c[25],n?J(e,c[25],i,Vs):Y(c[25]),je):o&&o.p&&(!n||192&i)&&o.p(c,n?i:-1)},i(c){n||(m(o,c),n=!0)},o(c){f(o,c),n=!1},d(c){c&&b(t),o&&o.d(c)}}}function Ze(r,t){let n,e,s;function o(){return t[23](t[27])}return e=new ut.Item({props:{onSelect:o,highlight:t[9]?t[9](t[27],t[1]):!!t[1]&&t[7](t[1])===t[7](t[27]),$$slots:{default:[ao]},$$scope:{ctx:t}}}),{key:r,first:null,c(){n=it(),x(e.$$.fragment),this.first=n},m(c,i){y(c,n,i),R(e,c,i),s=!0},p(c,i){t=c;const a={};64&i&&(a.onSelect=o),706&i&&(a.highlight=t[9]?t[9](t[27],t[1]):!!t[1]&&t[7](t[1])===t[7](t[27])),33554624&i&&(a.$$scope={dirty:i,ctx:t}),e.$set(a)},i(c){s||(m(e.$$.fragment,c),s=!0)},o(c){f(e.$$.fragment,c),s=!1},d(c){c&&b(n),L(e,c)}}}function io(r){let t,n,e,s,o,c;return n=new jt({props:{size:1,useCurrentColor:!0}}),{c(){t=A("div"),x(n.$$.fragment),e=P(),s=A("span"),o=G(r[11]),_(t,"class","c-searchable-dropdown__loading svelte-145zgu0")},m(i,a){y(i,t,a),R(n,t,null),k(t,e),k(t,s),k(s,o),c=!0},p(i,a){(!c||2048&a)&&K(o,i[11])},i(i){c||(m(n.$$.fragment,i),c=!0)},o(i){f(n.$$.fragment,i),c=!1},d(i){i&&b(t),L(n)}}}function tn(r){let t;const n=r[18].footer,e=W(n,r,r[25],Oe);return{c(){e&&e.c()},m(s,o){e&&e.m(s,o),t=!0},p(s,o){e&&e.p&&(!t||33554432&o)&&X(e,n,s,s[25],t?J(n,s[25],o,Os):Y(s[25]),Oe)},i(s){t||(m(e,s),t=!0)},o(s){f(e,s),t=!1},d(s){e&&e.d(s)}}}function lo(r){let t,n,e,s,o,c;const i=[ro,oo,so],a=[];function l(d,w){return d[4]?0:d[6].length>0?1:d[10]?2:-1}~(t=l(r))&&(n=a[t]=i[t](r));let u=r[17].footer&&tn(r);return{c(){n&&n.c(),e=P(),u&&u.c(),s=P(),o=A("div"),Bn(o,"margin-bottom","var(--ds-spacing-2)")},m(d,w){~t&&a[t].m(d,w),y(d,e,w),u&&u.m(d,w),y(d,s,w),y(d,o,w),c=!0},p(d,w){let g=t;t=l(d),t===g?~t&&a[t].p(d,w):(n&&(O(),f(a[g],1,1,()=>{a[g]=null}),V()),~t?(n=a[t],n?n.p(d,w):(n=a[t]=i[t](d),n.c()),m(n,1),n.m(e.parentNode,e)):n=null),d[17].footer?u?(u.p(d,w),131072&w&&m(u,1)):(u=tn(d),u.c(),m(u,1),u.m(s.parentNode,s)):u&&(O(),f(u,1,1,()=>{u=null}),V())},i(d){c||(m(n),m(u),c=!0)},o(d){f(n),f(u),c=!1},d(d){d&&(b(e),b(s),b(o)),~t&&a[t].d(d),u&&u.d(d)}}}function uo(r){let t,n,e,s;t=new ut.Trigger({props:{$$slots:{default:[no]},$$scope:{ctx:r}}});let o=!r[5]&&Qe(r);return{c(){x(t.$$.fragment),n=P(),o&&o.c(),e=it()},m(c,i){R(t,c,i),y(c,n,i),o&&o.m(c,i),y(c,e,i),s=!0},p(c,i){const a={};33691709&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a),c[5]?o&&(O(),f(o,1,1,()=>{o=null}),V()):o?(o.p(c,i),32&i&&m(o,1)):(o=Qe(c),o.c(),m(o,1),o.m(e.parentNode,e))},i(c){s||(m(t.$$.fragment,c),m(o),s=!0)},o(c){f(t.$$.fragment,c),f(o),s=!1},d(c){c&&(b(n),b(e)),L(t,c),o&&o.d(c)}}}function po(r){let t,n,e,s;function o(i){r[24](i)}let c={onOpenChange:r[14],$$slots:{default:[uo]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new ut.Root({props:c}),vt.push(()=>bt(n,"requestClose",o)),{c(){t=A("div"),x(n.$$.fragment),_(t,"class","c-searchable-dropdown svelte-145zgu0")},m(i,a){y(i,t,a),R(n,t,null),s=!0},p(i,[a]){const l={};33693439&a&&(l.$$scope={dirty:a,ctx:i}),!e&&8192&a&&(e=!0,l.requestClose=i[13],_t(()=>e=!1)),n.$set(l)},i(i){s||(m(n.$$.fragment,i),s=!0)},o(i){f(n.$$.fragment,i),s=!1},d(i){i&&b(t),L(n)}}}function $o(r,t,n){let{$$slots:e={},$$scope:s}=t;const o=pe(e);let{title:c=""}=t,{placeholder:i="Search..."}=t,{isLoading:a=!1}=t,{disabled:l=!1}=t,{searchValue:u=""}=t,{items:d=[]}=t,{selectedItem:w=null}=t,{itemLabelFn:g=U=>(U==null?void 0:U.toString())||""}=t,{itemKeyFn:h=U=>(U==null?void 0:U.toString())||""}=t,{isItemSelected:p}=t,{noItemsLabel:$="No items found"}=t,{loadingLabel:v="Loading..."}=t,B=!1,F=()=>{};const E=Nt();function I(U){n(0,u=U),E("search",U)}function z(U){n(1,w=U),E("select",U),F()}return r.$$set=U=>{"title"in U&&n(2,c=U.title),"placeholder"in U&&n(3,i=U.placeholder),"isLoading"in U&&n(4,a=U.isLoading),"disabled"in U&&n(5,l=U.disabled),"searchValue"in U&&n(0,u=U.searchValue),"items"in U&&n(6,d=U.items),"selectedItem"in U&&n(1,w=U.selectedItem),"itemLabelFn"in U&&n(7,g=U.itemLabelFn),"itemKeyFn"in U&&n(8,h=U.itemKeyFn),"isItemSelected"in U&&n(9,p=U.isItemSelected),"noItemsLabel"in U&&n(10,$=U.noItemsLabel),"loadingLabel"in U&&n(11,v=U.loadingLabel),"$$scope"in U&&n(25,s=U.$$scope)},[u,w,c,i,a,l,d,g,h,p,$,v,B,F,function(U){if(!l){if(n(12,B=U),U&&w){const tt=g(w);n(0,u=tt),E("search",""),setTimeout(()=>{const nt=document.querySelector(".c-searchable-dropdown__trigger-input");nt&&nt.select()},0)}else U&&(n(0,u=""),E("search",""));E("openChange",U)}},I,z,o,e,function(U){Wt.call(this,r,U)},function(U){Wt.call(this,r,U)},function(){u=this.value,n(0,u)},U=>I(U.currentTarget.value),U=>z(U),function(U){F=U,n(13,F)},s]}class le extends ot{constructor(t){super(),rt(this,t,$o,po,ct,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}const Zt=class Zt{constructor(t,n=Zt._defaultPagesPerLoad){at(this,"_selectedRepo",zt(void 0));at(this,"_selectedBranch",zt(void 0));at(this,"_allRepos",zt([]));at(this,"_allBranches",zt([]));at(this,"_addedRepos",[]);at(this,"_error");at(this,"_isDevDeploy",!1);at(this,"_loadedBranchPages",0);at(this,"_hasMoreBranchPages",!1);at(this,"_lastUsedRepoUrl");at(this,"_lastUsedBranchName");this.gitRefModel=t,this._pagesPerLoad=n}get selectedRepo(){return this._selectedRepo}set selectedRepo(t){this._selectedRepo.set(t),this._allBranches.set([])}get selectedBranch(){return this._selectedBranch}set selectedBranch(t){this._selectedBranch.set(t)}get allRepos(){return this._allRepos}get allBranches(){return this._allBranches}get error(){return this._error}get isDevDeploy(){return this._isDevDeploy}set lastUsedRepoUrl(t){this._lastUsedRepoUrl=t}get lastUsedRepoUrl(){return this._lastUsedRepoUrl}set lastUsedBranchName(t){this._lastUsedBranchName=t}get lastUsedBranchName(){return this._lastUsedBranchName}async updateReposList(){const{repos:t,error:n,isDevDeploy:e}=await this.gitRefModel.listUserRepos();if(e)return this._isDevDeploy=!0,void this._allRepos.set(await this.fetchLocalRepos());if(n)this._error=n;else{this._isDevDeploy=e||!1;try{const s=await this.getCurrentWorkspaceRepo();if(s&&!t.some(o=>o.name===s.name&&o.owner===s.owner)){const{repo:o,error:c}=await this.gitRefModel.getGithubRepo(s);c?console.error("Failed to fetch GitHub repo:",c):o&&t.unshift(o)}}catch(s){console.error("Failed to fetch GitHub repo:",s)}this._allRepos.set([...this._addedRepos,...t])}}async preselectRepo(){if(Ct(this._selectedRepo))return;const t=s=>{const o=Ct(this._allRepos).find(c=>c.html_url===s);o&&this._selectedRepo.set(o)},n=await this.getCurrentWorkspaceRepo(),e=Ct(this._allRepos);if(n){const s=e.find(o=>o.name===n.name&&o.owner===n.owner);if(s)return void this._selectedRepo.set(s)}this._lastUsedRepoUrl?t(this._lastUsedRepoUrl):this._selectedRepo.set(e[0])}addRepo(t){this._addedRepos.push(t),this._allRepos.update(n=>n.some(e=>e.name===t.name&&e.owner===t.owner)?n:[t,...n])}async updateBranches(t=!1,n){return!!Ct(this._selectedRepo)&&(this._isDevDeploy?(this._allBranches.set(await this.fetchLocalBranches()),!1):(this._loadedBranchPages=0,this._hasMoreBranchPages=!1,this._allBranches.set([]),await this.updateBranchesAtPage(1,t,n),this._hasMoreBranchPages))}async loadMoreBranches(){return!!this._hasMoreBranchPages&&(await this.updateBranchesAtPage(this._loadedBranchPages+1),this._hasMoreBranchPages)}preselectBranch(){const t=Ct(this._selectedRepo);if(!t||Ct(this._selectedBranch))return!1;const n=Ct(this._allBranches);if(this._lastUsedBranchName){const e=n.find(s=>s.name===this._lastUsedBranchName);if(e)return this._selectedBranch.set(e),!0}if(t.default_branch){const e=n.find(s=>s.name===t.default_branch);return e?(this._selectedBranch.set(e),!0):(this._selectedBranch.set({name:t.default_branch,commit:{sha:"",url:""},protected:!1}),!0)}return this._selectedBranch.set(n[0]),!0}async fetchLocalRepos(){console.warn("Fetching repos from local git environment.");const{remoteUrl:t,error:n}=await this.gitRefModel.getRemoteUrl(),e=ie(t);if(!e||n)return this._error=n??"Failed to parse remote URL in your local git repo.",[];const{owner:s,name:o}=e;return[{name:o,owner:s,html_url:t}]}async fetchLocalBranches(){console.warn("Fetching branches from local git environment.");const t=function(s){return s.filter(o=>o.isRemote)}((await this.gitRefModel.listBranches()).branches),n=t.find(s=>s.isDefault),e=s=>({name:_n(s.name),commit:{sha:"",url:""},protected:!1});return n&&this._selectedBranch.set(e(n)),t.map(e)}async getCurrentWorkspaceRepo(){const{remoteUrl:t,error:n}=await this.gitRefModel.getRemoteUrl();if(n)return;const e=ie(t);if(!e)return;const{owner:s,name:o}=e;return{name:o,owner:s,html_url:t}}async updateBranchesAtPage(t,n=!1,e){const s=Ct(this._selectedRepo);if(s&&!(t<this._loadedBranchPages))do{if(s!==Ct(this._selectedRepo)){this._allBranches.set([]);break}const o=await this.gitRefModel.listRepoBranches(s,t);if(o.error){this._error=o.error;break}this._allBranches.update(c=>[...c,...o.branches]),t=o.nextPage,this._loadedBranchPages++,this._hasMoreBranchPages=o.hasNextPage,n&&this.preselectBranch()&&(e==null||e())}while(this._loadedBranchPages%this._pagesPerLoad!=0&&this._hasMoreBranchPages)}};at(Zt,"_defaultPagesPerLoad",20);let ue=Zt;function mo(r){let t,n,e;return n=new ee({props:{color:r[7],variant:"soft",size:2,$$slots:{default:[bo]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"class","c-commit-ref-selector__error svelte-btbfel")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p(s,o){const c={};128&o[0]&&(c.color=s[7]),8195&o[0]|512&o[2]&&(c.$$scope={dirty:o,ctx:s}),n.$set(c)},i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function fo(r){var h;let t,n,e,s,o,c,i,a,l;function u(p){r[37](p)}let d={title:r[11],placeholder:"Search repositories or paste a GitHub URL...",itemKeyFn:Do,isLoading:r[9],disabled:!r[17].length,items:r[8],selectedItem:r[4],itemLabelFn:No,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[yo],icon:[_o]},$$scope:{ctx:r}};function w(p){r[42](p)}r[12]!==void 0&&(d.searchValue=r[12]),e=new le({props:d}),vt.push(()=>bt(e,"searchValue",u)),e.$on("openChange",r[38]),e.$on("search",r[39]),e.$on("select",r[40]);let g={title:((h=r[5])==null?void 0:h.name)||"Choose branch...",itemKeyFn:zo,placeholder:"Search branches...",isLoading:r[16],disabled:r[14],items:r[19],selectedItem:r[5],itemLabelFn:Mo,noItemsLabel:r[2]?"":"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[Fo],inputButton:[Ao],searchIcon:[Ro],icon:[xo]},$$scope:{ctx:r}};return r[18]!==void 0&&(g.searchValue=r[18]),i=new le({props:g}),vt.push(()=>bt(i,"searchValue",w)),i.$on("openChange",r[43]),i.$on("search",r[44]),i.$on("select",r[45]),{c(){t=A("div"),n=A("div"),x(e.$$.fragment),o=P(),c=A("div"),x(i.$$.fragment),_(n,"class","c-commit-ref-selector__selector svelte-btbfel"),_(c,"class","c-commit-ref-selector__selector svelte-btbfel"),_(t,"class","c-commit-ref-selector__selectors-container svelte-btbfel")},m(p,$){y(p,t,$),k(t,n),R(e,n,null),k(t,o),k(t,c),R(i,c,null),l=!0},p(p,$){var F;const v={};2048&$[0]&&(v.title=p[11]),512&$[0]&&(v.isLoading=p[9]),131072&$[0]&&(v.disabled=!p[17].length),256&$[0]&&(v.items=p[8]),16&$[0]&&(v.selectedItem=p[4]),512&$[2]&&(v.$$scope={dirty:$,ctx:p}),!s&&4096&$[0]&&(s=!0,v.searchValue=p[12],_t(()=>s=!1)),e.$set(v);const B={};32&$[0]&&(B.title=((F=p[5])==null?void 0:F.name)||"Choose branch..."),65536&$[0]&&(B.isLoading=p[16]),16384&$[0]&&(B.disabled=p[14]),524288&$[0]&&(B.items=p[19]),32&$[0]&&(B.selectedItem=p[5]),4&$[0]&&(B.noItemsLabel=p[2]?"":"No branches found"),1100&$[0]|512&$[2]&&(B.$$scope={dirty:$,ctx:p}),!a&&262144&$[0]&&(a=!0,B.searchValue=p[18],_t(()=>a=!1)),i.$set(B)},i(p){l||(m(e.$$.fragment,p),m(i.$$.fragment,p),l=!0)},o(p){f(e.$$.fragment,p),f(i.$$.fragment,p),l=!1},d(p){p&&b(t),L(e),L(i)}}}function go(r){let t,n;return t=new qt({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[vo],default:[wo]},$$scope:{ctx:r}}}),t.$on("click",r[28]),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};2&s[0]&&(o.loading=e[1]),512&s[2]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function ho(r){let t,n;return t=new Hs({}),t.$on("authStateChange",r[46]),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function wo(r){let t;return{c(){t=G("Reload available repos and branches")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function vo(r){let t,n,e;return n=new dn({}),{c(){t=A("span"),x(n.$$.fragment),_(t,"slot","iconLeft"),_(t,"class","svelte-btbfel")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p:M,i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function bo(r){let t,n,e,s,o,c,i;const a=[ho,go],l=[];function u(d,w){return d[13]?1:0}return o=u(r),c=l[o]=a[o](r),{c(){t=A("div"),n=A("div"),e=G(r[0]),s=P(),c.c(),_(n,"class","c-commit-ref-selector__error-message svelte-btbfel"),_(t,"class","c-commit-ref-selector__error-content svelte-btbfel")},m(d,w){y(d,t,w),k(t,n),k(n,e),k(t,s),l[o].m(t,null),i=!0},p(d,w){(!i||1&w[0])&&K(e,d[0]);let g=o;o=u(d),o===g?l[o].p(d,w):(O(),f(l[g],1,1,()=>{l[g]=null}),V(),c=l[o],c?c.p(d,w):(c=l[o]=a[o](d),c.c()),m(c,1),c.m(t,null))},i(d){i||(m(c),i=!0)},o(d){f(c),i=!1},d(d){d&&b(t),l[o].d()}}}function _o(r){let t,n;return t=new me({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function yo(r){let t,n;return t=new de({props:{slot:"searchIcon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function xo(r){let t,n;return t=new Un({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ro(r){let t,n;return t=new de({props:{slot:"searchIcon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Lo(r){let t,n;return t=new Hn({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Co(r){let t,n;return t=new se({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Lo]},$$scope:{ctx:r}}}),t.$on("click",r[33]),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};512&s[2]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ao(r){let t,n,e;return n=new It({props:{content:"Refresh branches",triggerOn:[fn.Hover],nested:!1,$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"slot","inputButton"),_(t,"class","c-commit-ref-selector__refresh-button svelte-btbfel")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p(s,o){const c={};512&o[2]&&(c.$$scope={dirty:o,ctx:s}),n.$set(c)},i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function Bo(r){let t,n,e,s,o,c,i;return e=new jt({props:{size:1}}),c=new Rt({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[ko]},$$scope:{ctx:r}}}),{c(){t=A("div"),n=A("div"),x(e.$$.fragment),s=P(),o=A("div"),x(c.$$.fragment),_(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),_(o,"class","c-commit-ref-selector__item-content svelte-btbfel"),_(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled svelte-btbfel")},m(a,l){y(a,t,l),k(t,n),R(e,n,null),k(t,s),k(t,o),R(c,o,null),i=!0},p(a,l){const u={};512&l[2]&&(u.$$scope={dirty:l,ctx:a}),c.$set(u)},i(a){i||(m(e.$$.fragment,a),m(c.$$.fragment,a),i=!0)},o(a){f(e.$$.fragment,a),f(c.$$.fragment,a),i=!1},d(a){a&&b(t),L(e),L(c)}}}function So(r){let t,n;return t=new It({props:{content:`${r[3].length} branches loaded`,triggerOn:[fn.Hover],nested:!1,$$slots:{default:[Io]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};8&s[0]&&(o.content=`${e[3].length} branches loaded`),1092&s[0]|512&s[2]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function ko(r){let t;return{c(){t=G("Loading branches...")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function Uo(r){let t;return{c(){t=G("Load more branches")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function Io(r){let t,n,e,s,o,c,i,a,l;return e=new jn({}),c=new Rt({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Uo]},$$scope:{ctx:r}}}),{c(){t=A("button"),n=A("div"),x(e.$$.fragment),s=P(),o=A("div"),x(c.$$.fragment),_(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),_(o,"class","c-commit-ref-selector__item-content svelte-btbfel"),_(t,"type","button"),_(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-btbfel")},m(u,d){y(u,t,d),k(t,n),R(e,n,null),k(t,s),k(t,o),R(c,o,null),i=!0,a||(l=Bt(t,"click",r[41]),a=!0)},p(u,d){const w={};512&d[2]&&(w.$$scope={dirty:d,ctx:u}),c.$set(w)},i(u){i||(m(e.$$.fragment,u),m(c.$$.fragment,u),i=!0)},o(u){f(e.$$.fragment,u),f(c.$$.fragment,u),i=!1},d(u){u&&b(t),L(e),L(c),a=!1,l()}}}function Fo(r){let t,n,e,s;const o=[So,Bo],c=[];function i(a,l){return a[10]&&!a[2]?0:a[2]?1:-1}return~(t=i(r))&&(n=c[t]=o[t](r)),{c(){n&&n.c(),e=it()},m(a,l){~t&&c[t].m(a,l),y(a,e,l),s=!0},p(a,l){let u=t;t=i(a),t===u?~t&&c[t].p(a,l):(n&&(O(),f(c[u],1,1,()=>{c[u]=null}),V()),~t?(n=c[t],n?n.p(a,l):(n=c[t]=o[t](a),n.c()),m(n,1),n.m(e.parentNode,e)):n=null)},i(a){s||(m(n),s=!0)},o(a){f(n),s=!1},d(a){a&&b(e),~t&&c[t].d(a)}}}function Po(r){let t,n,e,s,o;const c=[fo,mo],i=[];function a(l,u){return l[15]?l[15]?1:-1:0}return~(e=a(r))&&(s=i[e]=c[e](r)),{c(){t=A("div"),n=A("div"),s&&s.c(),_(n,"class","c-commit-ref-selector__content svelte-btbfel"),_(t,"class","c-commit-ref-selector svelte-btbfel")},m(l,u){y(l,t,u),k(t,n),~e&&i[e].m(n,null),o=!0},p(l,u){let d=e;e=a(l),e===d?~e&&i[e].p(l,u):(s&&(O(),f(i[d],1,1,()=>{i[d]=null}),V()),~e?(s=i[e],s?s.p(l,u):(s=i[e]=c[e](l),s.c()),m(s,1),s.m(n,null)):s=null)},i(l){o||(m(s),o=!0)},o(l){f(s),o=!1},d(l){l&&b(t),~e&&i[e].d()}}}const Do=r=>`${r.owner}-${r.name}`,No=r=>`${r.owner}/${r.name}`,zo=(r,t)=>`${r.name}-${r.commit.sha}=${t}`,Mo=r=>r.name.replace("origin/","");function Eo(r,t,n){let e,s,o,c,i,a,l,u,d;const w=Ft(gn.key),g=Nt(),h=Ft(ne.key),p=new ue(w),$=p.allRepos;At(r,$,C=>n(17,c=C));const v=p.allBranches;At(r,v,C=>n(3,i=C));const B=p.selectedRepo;At(r,B,C=>n(4,a=C));const F=p.selectedBranch;At(r,F,C=>n(5,l=C));let{errorMessage:E=""}=t,{isLoading:I=!1}=t,{lastUsedBranchName:z=null}=t,{lastUsedRepoUrl:U=null}=t,tt="warning",nt=c,H=!1,st=!1,St=!1;const gt=new Set;let N="Choose repository...",Q=!1;const ht=new Map;let Z=!1,lt="";const Lt=zt("");function pt(C){n(12,lt=C),Q=!0,Ln(C)}At(r,Lt,C=>n(18,u=C));const Pt=Re(function(C){Lt.set(C),n(36,Z=!0)},300,{leading:!1,trailing:!0}),S={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};te(async()=>{await re()});const T=Sn([v,B,Lt],([C,D,kt])=>{if(Z&&kt.trim()!=="")return mt=kt,C.filter(dt=>dt.name.toLowerCase().includes(mt.toLowerCase()));if(D!=null&&D.default_branch)return[C.find(ft=>ft.name===(D==null?void 0:D.default_branch))||{name:D.default_branch,commit:{sha:"",url:""},protected:!1},...C.filter(ft=>ft.name!==(D==null?void 0:D.default_branch))];return C;var mt});async function j(){n(9,st=!0),await p.updateReposList(),function(){gt.clear();const C=new Set;c.forEach(D=>{C.has(D.name)?gt.add(D.name):C.add(D.name)})}(),await p.preselectRepo(),n(9,st=!1)}function $t(){B&&async function(){a&&(n(2,H=!0),n(10,St=await p.updateBranches(!0,Kt)),n(2,H=!1))}().then(()=>{n(1,I=!1),e||Kt()}).catch(C=>{console.error("Error fetching all branches:",C),Dt(`Failed to fetch branches: ${C instanceof Error?C.message:String(C)}`)})}At(r,T,C=>n(19,d=C));let q=!0;const Ut=async()=>{try{n(13,q=await w.isGithubAuthenticated()),q||Dt("Please authenticate with GitHub to use this feature.","info")}catch(C){console.error("Failed to check GitHub authentication status:",C),Dt("Please authenticate with GitHub to use this feature."),n(13,q=!1)}};async function oe(){n(1,I=!0),n(15,e=!1),n(0,E=""),n(7,tt="warning");try{if(await Ut(),!q)return void n(1,I=!1);n(9,st=!0),n(2,H=!0),await j(),e||(n(9,st=!1),$t()),e||Kt()}catch(C){console.error("Error fetching git data:",C),Dt(S.failedToFetchBranches)}finally{n(1,I=!1)}}async function re(){n(1,I=!0);try{await oe()}catch(C){console.error("Error fetching and syncing branches:",C),Dt("Failed to fetch repos and branches. Please try again.")}finally{n(1,I=!1)}}function Dt(C,D="warning"){console.error("Error:",C),n(15,e=!0),n(0,E=C),n(7,tt=D)}async function fe(C){n(6,p.selectedBranch=C,p),n(36,Z=!1),we((l==null?void 0:l.name)??"");const D=h.creationMetrics;h.setCreationMetrics({changedRepo:(D==null?void 0:D.changedRepo)??!1,changedBranch:!0}),Kt()}async function ge(C){n(2,H=!0),n(6,p.selectedRepo=C,p),n(6,p.selectedBranch=void 0,p),Q=!1,Rn(""),$t();const D=h.creationMetrics;h.setCreationMetrics({changedRepo:!0,changedBranch:(D==null?void 0:D.changedBranch)??!1})}function he(C,D){C||(D==="repo"?Q=!1:(D==="branch"||(Q=!1),n(36,Z=!1)))}function Kt(){if(!(a!=null&&a.html_url)||!l)return;const C={github_commit_ref:{repository_url:a.html_url,git_ref:l.name}};g("commitRefChange",{commitRef:C,selectedBranch:l})}const we=C=>{Lt.set(C)},Rn=C=>{n(12,lt=C)},Ln=Re(async function(C=""){n(15,e=!1);try{if(Q){const D=C||lt;if(/^(?:https?:\/\/)?(?:www\.)?github\.com\/[^/]+\/[^/]+/.test(D)){const kt=ie(D);if(kt){const{repo:mt,error:dt}=await w.getGithubRepo(kt);if(!dt){const ft=`${mt.owner}/${mt.name}`;return ht.has(ft)||(ht.set(ft,mt),p.addRepo(mt)),void n(8,nt=[mt])}}}n(8,nt=function(kt,mt){if(mt==="")return kt;const dt=mt.toLowerCase().split("/",2).filter(ft=>ft.length>0);return kt.filter(ft=>{const _e=ft.owner.toLowerCase(),ye=ft.name.toLowerCase();return dt.length===1?_e.includes(dt[0])||ye.includes(dt[0]):dt.length!==2||dt[0]===_e&&ye.includes(dt[1])})}(c,D))}else n(8,nt=c)}catch(D){console.error("Error fetching repos:",D),n(8,nt=[]),Dt(S.failedToFetchFromRemote)}},300,{leading:!1,trailing:!0});function ve(C){C&&o||he(C,"branch")}function be(C){C&&!c.length||he(C,"repo")}return r.$$set=C=>{"errorMessage"in C&&n(0,E=C.errorMessage),"isLoading"in C&&n(1,I=C.isLoading),"lastUsedBranchName"in C&&n(34,z=C.lastUsedBranchName),"lastUsedRepoUrl"in C&&n(35,U=C.lastUsedRepoUrl)},r.$$.update=()=>{if(16&r.$$.dirty[1]&&n(6,p.lastUsedRepoUrl=U??void 0,p),8&r.$$.dirty[1]&&n(6,p.lastUsedBranchName=z??void 0,p),1&r.$$.dirty[0]&&n(15,e=E!==""),52&r.$$.dirty[0]&&n(16,s=a&&H&&!l),16&r.$$.dirty[0])if(a){const{owner:C,name:D}=a;n(11,N=gt.has(D)?`${C}/${D}`:D)}else n(11,N="Choose repository...");32&r.$$.dirty[0]|32&r.$$.dirty[1]&&we(Z?"":(l==null?void 0:l.name)??""),24&r.$$.dirty[0]|32&r.$$.dirty[1]&&n(14,o=!a||!Z&&!i.length)},[E,I,H,i,a,l,p,tt,nt,st,St,N,lt,q,o,e,s,c,u,d,$,v,B,F,Lt,pt,Pt,T,re,fe,ge,ve,be,function(){$t()},z,U,Z,function(C){lt=C,n(12,lt)},C=>be(C.detail),C=>pt(C.detail),C=>ge(C.detail),async()=>{n(2,H=!0),n(10,St=await p.loadMoreBranches()),n(2,H=!1)},function(C){u=C,Lt.set(u)},C=>ve(C.detail),C=>Pt(C.detail),C=>fe(C.detail),async C=>{const{isAuthenticated:D}=C.detail;D!==q&&(n(13,q=D),D&&await re())}]}class To extends ot{constructor(t){super(),rt(this,t,Eo,Po,ct,{errorMessage:0,isLoading:1,lastUsedBranchName:34,lastUsedRepoUrl:35},null,[-1,-1,-1])}}function Go(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=et(s,e[o]);return{c(){t=Mt("svg"),n=new Et(!0),this.h()},l(o){t=Tt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Gt(t);n=Ht(c,!0),c.forEach(b),this.h()},h(){n.a=null,yt(t,s)},m(o,c){Ot(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',t)},p(o,[c]){yt(t,s=Vt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function Ho(r,t,n){return r.$$set=e=>{n(0,t=et(et({},t),xt(e)))},[t=xt(t)]}class Oo extends ot{constructor(t){super(),rt(this,t,Ho,Go,ct,{})}}function Vo(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=et(s,e[o]);return{c(){t=Mt("svg"),n=new Et(!0),this.h()},l(o){t=Tt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Gt(t);n=Ht(c,!0),c.forEach(b),this.h()},h(){n.a=null,yt(t,s)},m(o,c){Ot(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',t)},p(o,[c]){yt(t,s=Vt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function jo(r,t,n){return r.$$set=e=>{n(0,t=et(et({},t),xt(e)))},[t=xt(t)]}class qo extends ot{constructor(t){super(),rt(this,t,jo,Vo,ct,{})}}const Ko=r=>({}),en=r=>({});function nn(r){let t,n;const e=r[12].icon,s=W(e,r,r[11],en);return{c(){t=A("div"),s&&s.c(),_(t,"class","c-setup-script-selector__icon svelte-udt6j8")},m(o,c){y(o,t,c),s&&s.m(t,null),n=!0},p(o,c){s&&s.p&&(!n||2048&c)&&X(s,e,o,o[11],n?J(e,o[11],c,Ko):Y(o[11]),en)},i(o){n||(m(s,o),n=!0)},o(o){f(s,o),n=!1},d(o){o&&b(t),s&&s.d(o)}}}function Wo(r){let t,n,e,s,o;return{c(){t=A("span"),n=G(r[0]),e=P(),s=A("span"),o=G(r[1]),_(t,"class","c-setup-script-selector__script-name svelte-udt6j8"),_(s,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){y(c,t,i),k(t,n),y(c,e,i),y(c,s,i),k(s,o)},p(c,i){1&i&&K(n,c[0]),2&i&&K(o,c[1])},i:M,o:M,d(c){c&&(b(t),b(e),b(s))}}}function Xo(r){let t,n,e,s,o,c,i,a,l;function u(h){r[15](h)}function d(h){r[16](h)}let w={size:1,variant:"surface"};r[6]!==void 0&&(w.value=r[6]),r[5]!==void 0&&(w.textInput=r[5]),e=new Pn({props:w}),vt.push(()=>bt(e,"value",u)),vt.push(()=>bt(e,"textInput",d)),e.$on("keydown",r[8]),e.$on("blur",r[9]);let g=r[7]&&function(h){let p;return{c(){p=A("span"),p.textContent=`${h[7]}`,_(p,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){y($,p,v)},p:M,d($){$&&b(p)}}}(r);return{c(){t=A("div"),n=A("div"),x(e.$$.fragment),c=P(),g&&g.c(),_(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),_(n,"role","presentation"),_(t,"class","c-setup-script-selector__rename-input svelte-udt6j8"),_(t,"role","presentation")},m(h,p){y(h,t,p),k(t,n),R(e,n,null),k(n,c),g&&g.m(n,null),i=!0,a||(l=[Bt(t,"click",Xt(r[13])),Bt(t,"mousedown",Xt(r[14]))],a=!0)},p(h,p){const $={};!s&&64&p&&(s=!0,$.value=h[6],_t(()=>s=!1)),!o&&32&p&&(o=!0,$.textInput=h[5],_t(()=>o=!1)),e.$set($),h[7]&&g.p(h,p)},i(h){i||(m(e.$$.fragment,h),i=!0)},o(h){f(e.$$.fragment,h),i=!1},d(h){h&&b(t),L(e),g&&g.d(),a=!1,$e(l)}}}function Yo(r){let t,n,e,s,o,c,i,a,l=r[10].icon&&nn(r);const u=[Xo,Wo],d=[];function w(p,$){return p[3]?0:1}s=w(r),o=d[s]=u[s](r);const g=r[12].default,h=W(g,r,r[11],null);return{c(){t=A("div"),l&&l.c(),n=P(),e=A("div"),o.c(),c=P(),i=A("div"),h&&h.c(),_(e,"class","c-setup-script-selector__script-info svelte-udt6j8"),_(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),_(t,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),_(t,"role","presentation"),wt(t,"c-setup-script-selector__script-item-content--renaming",r[3]),wt(t,"c-setup-script-selector__script-item-content--is-path",r[2]),wt(t,"c-setup-script-selector__script-item-content--selected",r[4])},m(p,$){y(p,t,$),l&&l.m(t,null),k(t,n),k(t,e),d[s].m(e,null),k(t,c),k(t,i),h&&h.m(i,null),a=!0},p(p,[$]){p[10].icon?l?(l.p(p,$),1024&$&&m(l,1)):(l=nn(p),l.c(),m(l,1),l.m(t,n)):l&&(O(),f(l,1,1,()=>{l=null}),V());let v=s;s=w(p),s===v?d[s].p(p,$):(O(),f(d[v],1,1,()=>{d[v]=null}),V(),o=d[s],o?o.p(p,$):(o=d[s]=u[s](p),o.c()),m(o,1),o.m(e,null)),h&&h.p&&(!a||2048&$)&&X(h,g,p,p[11],a?J(g,p[11],$,null):Y(p[11]),null),(!a||8&$)&&wt(t,"c-setup-script-selector__script-item-content--renaming",p[3]),(!a||4&$)&&wt(t,"c-setup-script-selector__script-item-content--is-path",p[2]),(!a||16&$)&&wt(t,"c-setup-script-selector__script-item-content--selected",p[4])},i(p){a||(m(l),m(o),m(h,p),a=!0)},o(p){f(l),f(o),f(h,p),a=!1},d(p){p&&b(t),l&&l.d(),d[s].d(),h&&h.d(p)}}}function Jo(r,t,n){let{$$slots:e={},$$scope:s}=t;const o=pe(e);let{name:c}=t,{path:i}=t,{isPath:a=!1}=t,{isRenaming:l=!1}=t,{isSelected:u=!1}=t;const d=Nt(),{baseName:w,extension:g}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let h,p=w;return r.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,i=$.path),"isPath"in $&&n(2,a=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,u=$.isSelected),"$$scope"in $&&n(11,s=$.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,i,a,l,u,h,p,g,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),p.trim()&&p!==w){const v=p.trim()+g;d("rename",{oldName:c,newName:v})}else d("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),d("cancelRename"));else $.stopPropagation()},function(){d("cancelRename")},o,s,e,function($){Wt.call(this,r,$)},function($){Wt.call(this,r,$)},function($){p=$,n(6,p)},function($){h=$,n(5,h)}]}class Qo extends ot{constructor(t){super(),rt(this,t,Jo,Yo,ct,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function sn(r){let t,n,e,s,o,c,i,a,l,u;function d(g){r[36](g)}let w={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[5],itemLabelFn:Sr,itemKeyFn:kr,isItemSelected:Ur,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[gr,({item:g})=>({46:g}),({item:g})=>[0,g?32768:0]],searchIcon:[cr],icon:[rr],title:[or]},$$scope:{ctx:r}};return r[2]!==void 0&&(w.searchValue=r[2]),e=new le({props:w}),vt.push(()=>bt(e,"searchValue",d)),e.$on("openChange",r[37]),e.$on("search",r[38]),e.$on("select",r[39]),i=new It({props:{content:r[10],nested:!1,$$slots:{default:[br]},$$scope:{ctx:r}}}),l=new It({props:{content:"Open a new file for you to write a setup script that you can edit directly.",nested:!1,$$slots:{default:[Rr]},$$scope:{ctx:r}}}),{c(){t=A("div"),n=A("div"),x(e.$$.fragment),o=P(),c=A("div"),x(i.$$.fragment),a=P(),x(l.$$.fragment),_(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),_(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),_(t,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(g,h){y(g,t,h),k(t,n),R(e,n,null),k(n,o),k(n,c),R(i,c,null),k(c,a),R(l,c,null),u=!0},p(g,h){const p={};2&h[0]&&(p.isLoading=g[1]),128&h[0]&&(p.items=g[7]),32&h[0]&&(p.selectedItem=g[5]),888&h[0]|98304&h[1]&&(p.$$scope={dirty:h,ctx:g}),!s&&4&h[0]&&(s=!0,p.searchValue=g[2],_t(()=>s=!1)),e.$set(p);const $={};1024&h[0]&&($.content=g[10]),2048&h[0]|65536&h[1]&&($.$$scope={dirty:h,ctx:g}),i.$set($);const v={};65536&h[1]&&(v.$$scope={dirty:h,ctx:g}),l.$set(v)},i(g){u||(m(e.$$.fragment,g),m(i.$$.fragment,g),m(l.$$.fragment,g),u=!0)},o(g){f(e.$$.fragment,g),f(i.$$.fragment,g),f(l.$$.fragment,g),u=!1},d(g){g&&b(t),L(e),L(i),L(l)}}}function Zo(r){let t,n;return t=new bn({props:{$$slots:{text:[er]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};8&s[0]|65536&s[1]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function tr(r){let t,n;return t=new bn({props:{$$slots:{grayText:[sr],text:[nr]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};768&s[0]|65536&s[1]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function er(r){let t,n;return{c(){t=A("span"),n=G(r[3]),_(t,"slot","text")},m(e,s){y(e,t,s),k(t,n)},p(e,s){8&s[0]&&K(n,e[3])},d(e){e&&b(t)}}}function nr(r){let t,n;return{c(){t=A("span"),n=G(r[9]),_(t,"slot","text")},m(e,s){y(e,t,s),k(t,n)},p(e,s){512&s[0]&&K(n,e[9])},d(e){e&&b(t)}}}function sr(r){let t,n;return{c(){t=A("span"),n=G(r[8]),_(t,"slot","grayText")},m(e,s){y(e,t,s),k(t,n)},p(e,s){256&s[0]&&K(n,e[8])},d(e){e&&b(t)}}}function or(r){let t,n,e,s;const o=[tr,Zo],c=[];function i(a,l){return a[4]?0:1}return n=i(r),e=c[n]=o[n](r),{c(){t=A("div"),e.c(),_(t,"slot","title")},m(a,l){y(a,t,l),c[n].m(t,null),s=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(O(),f(c[u],1,1,()=>{c[u]=null}),V(),e=c[n],e?e.p(a,l):(e=c[n]=o[n](a),e.c()),m(e,1),e.m(t,null))},i(a){s||(m(e),s=!0)},o(a){f(e),s=!1},d(a){a&&b(t),c[n].d()}}}function rr(r){let t,n;return t=new vn({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function cr(r){let t,n;return t=new de({props:{slot:"searchIcon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function ar(r){var e;let t,n;return t=new Qo({props:{name:r[46].name,path:r[46].path,isPath:!0,isRenaming:((e=r[6])==null?void 0:e.path)===r[46].path,isSelected:!(!r[5]||r[5].path!==r[46].path),$$slots:{default:[fr]},$$scope:{ctx:r}}}),t.$on("rename",function(...s){return r[35](r[46],...s)}),t.$on("cancelRename",r[22]),{c(){x(t.$$.fragment)},m(s,o){R(t,s,o),n=!0},p(s,o){var i;r=s;const c={};32768&o[1]&&(c.name=r[46].name),32768&o[1]&&(c.path=r[46].path),64&o[0]|32768&o[1]&&(c.isRenaming=((i=r[6])==null?void 0:i.path)===r[46].path),32&o[0]|32768&o[1]&&(c.isSelected=!(!r[5]||r[5].path!==r[46].path)),98304&o[1]&&(c.$$scope={dirty:o,ctx:r}),t.$set(c)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){f(t.$$.fragment,s),n=!1},d(s){L(t,s)}}}function ir(r){let t,n,e,s;return n=new vn({}),{c(){t=A("div"),x(n.$$.fragment),e=G(`
                  Use basic environment`),_(t,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(o,c){y(o,t,c),R(n,t,null),k(t,e),s=!0},p:M,i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){f(n.$$.fragment,o),s=!1},d(o){o&&b(t),L(n)}}}function lr(r){let t,n;return t=new Nn({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function ur(r){let t,n;return t=new se({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[lr]},$$scope:{ctx:r}}}),t.$on("click",function(...e){return r[32](r[46],...e)}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){r=e;const o={};65536&s[1]&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function pr(r){let t,n;return t=new Oo({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function $r(r){let t,n;return t=new se({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[pr]},$$scope:{ctx:r}}}),t.$on("click",function(...e){return r[33](r[46],...e)}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){r=e;const o={};65536&s[1]&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function mr(r){let t,n;return t=new In({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function dr(r){let t,n;return t=new se({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[mr]},$$scope:{ctx:r}}}),t.$on("click",function(...e){return r[34](r[46],...e)}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){r=e;const o={};65536&s[1]&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function fr(r){let t,n,e,s,o,c;return t=new It({props:{content:"Open script in editor",nested:!1,$$slots:{default:[ur]},$$scope:{ctx:r}}}),e=new It({props:{content:"Rename script",nested:!1,$$slots:{default:[$r]},$$scope:{ctx:r}}}),o=new It({props:{content:"Delete script",nested:!1,$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),n=P(),x(e.$$.fragment),s=P(),x(o.$$.fragment)},m(i,a){R(t,i,a),y(i,n,a),R(e,i,a),y(i,s,a),R(o,i,a),c=!0},p(i,a){const l={};98304&a[1]&&(l.$$scope={dirty:a,ctx:i}),t.$set(l);const u={};98304&a[1]&&(u.$$scope={dirty:a,ctx:i}),e.$set(u);const d={};98304&a[1]&&(d.$$scope={dirty:a,ctx:i}),o.$set(d)},i(i){c||(m(t.$$.fragment,i),m(e.$$.fragment,i),m(o.$$.fragment,i),c=!0)},o(i){f(t.$$.fragment,i),f(e.$$.fragment,i),f(o.$$.fragment,i),c=!1},d(i){i&&(b(n),b(s)),L(t,i),L(e,i),L(o,i)}}}function gr(r){let t,n,e,s;const o=[ir,ar],c=[];function i(a,l){return a[46]===null?0:1}return t=i(r),n=c[t]=o[t](r),{c(){n.c(),e=it()},m(a,l){c[t].m(a,l),y(a,e,l),s=!0},p(a,l){let u=t;t=i(a),t===u?c[t].p(a,l):(O(),f(c[u],1,1,()=>{c[u]=null}),V(),n=c[t],n?n.p(a,l):(n=c[t]=o[t](a),n.c()),m(n,1),n.m(e.parentNode,e))},i(a){s||(m(n),s=!0)},o(a){f(n),s=!1},d(a){a&&b(e),c[t].d(a)}}}function hr(r){let t,n;return{c(){t=G("Auto-generate"),n=A("span"),n.textContent="a script",_(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(e,s){y(e,t,s),y(e,n,s)},p:M,d(e){e&&(b(t),b(n))}}}function wr(r){let t,n;return t=new qo({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function vr(r){let t,n;return t=new $n({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function br(r){let t,n;return t=new qt({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[vr],iconLeft:[wr],default:[hr]},$$scope:{ctx:r}}}),t.$on("click",r[15]),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};2048&s[0]&&(o.disabled=e[11]),65536&s[1]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function _r(r){let t,n,e;return{c(){t=G("Write "),n=A("span"),n.textContent="a script",e=G("by hand"),_(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(s,o){y(s,t,o),y(s,n,o),y(s,e,o)},p:M,d(s){s&&(b(t),b(n),b(e))}}}function yr(r){let t,n;return t=new zn({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function xr(r){let t,n;return t=new $n({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Rr(r){let t,n;return t=new qt({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[xr],iconLeft:[yr],default:[_r]},$$scope:{ctx:r}}}),t.$on("click",r[16]),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};65536&s[1]&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function on(r){let t,n,e;return n=new ee({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[Ar]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"class","c-setup-script-selector__error svelte-3cd2r2")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p(s,o){const c={};3&o[0]|65536&o[1]&&(c.$$scope={dirty:o,ctx:s}),n.$set(c)},i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function Lr(r){let t;return{c(){t=G("Refresh")},m(n,e){y(n,t,e)},d(n){n&&b(t)}}}function Cr(r){let t,n,e;return n=new dn({}),{c(){t=A("span"),x(n.$$.fragment),_(t,"slot","iconLeft")},m(s,o){y(s,t,o),R(n,t,null),e=!0},p:M,i(s){e||(m(n.$$.fragment,s),e=!0)},o(s){f(n.$$.fragment,s),e=!1},d(s){s&&b(t),L(n)}}}function Ar(r){let t,n,e,s,o,c;return o=new qt({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[Cr],default:[Lr]},$$scope:{ctx:r}}}),o.$on("click",r[18]),{c(){t=A("div"),n=A("div"),e=G(r[0]),s=P(),x(o.$$.fragment),_(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),_(t,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(i,a){y(i,t,a),k(t,n),k(n,e),k(t,s),R(o,t,null),c=!0},p(i,a){(!c||1&a[0])&&K(e,i[0]);const l={};2&a[0]&&(l.loading=i[1]),65536&a[1]&&(l.$$scope={dirty:a,ctx:i}),o.$set(l)},i(i){c||(m(o.$$.fragment,i),c=!0)},o(i){f(o.$$.fragment,i),c=!1},d(i){i&&b(t),L(o)}}}function Br(r){let t,n,e,s,o=(!r[12]||r[0]===r[17].noScriptsFound)&&sn(r),c=r[12]&&r[0]!==r[17].noScriptsFound&&on(r);return{c(){t=A("div"),n=A("div"),o&&o.c(),e=P(),c&&c.c(),_(n,"class","c-setup-script-selector__content svelte-3cd2r2"),_(t,"class","c-setup-script-selector svelte-3cd2r2")},m(i,a){y(i,t,a),k(t,n),o&&o.m(n,null),k(n,e),c&&c.m(n,null),s=!0},p(i,a){i[12]&&i[0]!==i[17].noScriptsFound?o&&(O(),f(o,1,1,()=>{o=null}),V()):o?(o.p(i,a),4097&a[0]&&m(o,1)):(o=sn(i),o.c(),m(o,1),o.m(n,e)),i[12]&&i[0]!==i[17].noScriptsFound?c?(c.p(i,a),4097&a[0]&&m(c,1)):(c=on(i),c.c(),m(c,1),c.m(n,null)):c&&(O(),f(c,1,1,()=>{c=null}),V())},i(i){s||(m(o),m(c),s=!0)},o(i){f(o),f(c),s=!1},d(i){i&&b(t),o&&o.d(),c&&c.d()}}}const Sr=r=>(r==null?void 0:r.name)||"",kr=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,Ur=(r,t)=>r===null&&t===null||!(!r||!t)&&r.path===t.path;function Ir(r,t,n){let e,s,o,c,i,a,l,u,d,w,{errorMessage:g=""}=t,{isLoading:h=!1}=t,{lastUsedScriptPath:p=null}=t,{disableNewAgentCreation:$=!1}=t;const v=Ft(ne.key);At(r,v,S=>n(31,w=S));const B=Nt(),F=Ft("chatModel").extensionClient,E=S=>{F.openFile({repoRoot:"",pathName:S.path,allowOutOfWorkspace:!0,openLocalUri:S.location==="home"})};let I=[],z="",U=null,tt=I,nt=!0;const H={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function st(){n(0,g="");try{const S=e==null?void 0:e.path;if(n(29,I=await v.listSetupScripts()),nt)if(p&&I.length>0){const T=I.find(j=>j.path===p);T&&(n(5,e=T),Pt())}else p===null&&(n(5,e=null),Pt());else if(S){const T=I.find(j=>j.path===S);T&&n(5,e=T)}nt=!1,I.length===0?n(0,g=H.noScriptsFound):n(0,g="")}catch(S){console.error("Error fetching setup scripts:",S),n(0,g=H.failedToFetchScripts)}}async function St(S,T){T&&T.stopPropagation();try{const j=await v.deleteSetupScript(S.name,S.location);j.success?((e==null?void 0:e.path)===S.path&&pt(null),await st()):(console.error("Failed to delete script:",j.error),ht(`Failed to delete script: ${j.error||"Unknown error"}`))}catch(j){console.error("Error deleting script:",j),ht(`Error deleting script: ${j instanceof Error?j.message:String(j)}`)}}async function gt(S,T){T&&T.stopPropagation(),n(6,U=S)}async function N(S,T){const{oldName:j,newName:$t}=T.detail;try{const q=await v.renameSetupScript(j,$t,S.location);if(q.success){await st();const Ut=I.find(oe=>oe.path===q.path);Ut&&pt(Ut)}else console.error("Failed to rename script:",q.error),ht(`Failed to rename script: ${q.error||"Unknown error"}`)}catch(q){console.error("Error renaming script:",q),ht(`Error renaming script: ${q instanceof Error?q.message:String(q)}`)}finally{Q()}}function Q(){n(6,U=null)}function ht(S){n(0,g=S)}function Z(S){n(2,z=S)}function lt(S){pt(S)}function Lt(S){S&&(st(),n(2,z=""))}async function pt(S){n(5,e=S),Pt(),v.saveLastRemoteAgentSetup(null,null,(e==null?void 0:e.path)||null)}function Pt(){B("setupScriptChange",{script:e})}return te(async()=>{var S;await st(),p===null?pt(null):(S=w.newAgentDraft)!=null&&S.setupScript&&!e&&pt(w.newAgentDraft.setupScript)}),r.$$set=S=>{"errorMessage"in S&&n(0,g=S.errorMessage),"isLoading"in S&&n(1,h=S.isLoading),"lastUsedScriptPath"in S&&n(27,p=S.lastUsedScriptPath),"disableNewAgentCreation"in S&&n(28,$=S.disableNewAgentCreation)},r.$$.update=()=>{var S,T,j;if(1&r.$$.dirty[1]&&n(5,e=((S=w.newAgentDraft)==null?void 0:S.setupScript)??null),1&r.$$.dirty[0]&&n(12,s=g!==""),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(11,o=$||((T=w.newAgentDraft)==null?void 0:T.isDisabled)||!w.newAgentDraft),268435456&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(10,c=w.newAgentDraft?(j=w.newAgentDraft)!=null&&j.isDisabled?"Please resolve the issues with your workspace selection":$?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),536870916&r.$$.dirty[0])if(z.trim()!==""){const $t="Use basic environment".toLowerCase().includes(z.toLowerCase()),q=I.filter(Ut=>Ut.name.toLowerCase().includes(z.toLowerCase())||Ut.path.toLowerCase().includes(z.toLowerCase()));n(7,tt=$t?[null,...q]:q)}else n(7,tt=[null,...I]);34&r.$$.dirty[0]&&n(30,i=()=>h?"...":e?e.isGenerateOption?e.name:e.location==="home"?"~/.augment/env/"+e.name:e.path:"Use basic environment"),1073741824&r.$$.dirty[0]&&n(3,a=i()),32&r.$$.dirty[0]&&n(4,l=!!(e!=null&&e.path)),24&r.$$.dirty[0]&&n(9,u=l?a.split("/").pop():a),24&r.$$.dirty[0]&&n(8,d=l?a.slice(0,a.lastIndexOf("/")):"")},[g,h,z,a,l,e,U,tt,d,u,c,o,s,v,E,async()=>{try{const S=w.newAgentDraft;S&&v.setNewAgentDraft({...S,isSetupScriptAgent:!0});const T=await v.createRemoteAgentFromDraft("SETUP_MODE");return T&&v.setCurrentAgent(T),T}catch(S){console.error("Failed to select setup script generation:",S)}},async()=>{try{const S="setup.sh",T=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,j=await v.saveSetupScript(S,T,"home");if(j.success&&j.path){await st();const $t=I.find(q=>q.path===j.path);$t&&(pt($t),E($t))}else console.error("Failed to create manual setup script:",j.error),n(0,g=`Failed to create manual setup script: ${j.error||"Unknown error"}`)}catch(S){console.error("Error creating manual setup script:",S),n(0,g=`Error creating manual setup script: ${S instanceof Error?S.message:String(S)}`)}},H,st,St,gt,N,Q,Z,lt,Lt,pt,p,$,I,i,w,(S,T)=>{T.stopPropagation(),E(S),pt(S)},(S,T)=>{T.stopPropagation(),gt(S)},(S,T)=>{T.stopPropagation(),St(S)},(S,T)=>N(S,T),function(S){z=S,n(2,z)},S=>Lt(S.detail),S=>Z(S.detail),S=>lt(S.detail)]}class Fr extends ot{constructor(t){super(),rt(this,t,Ir,Br,ct,{errorMessage:0,isLoading:1,lastUsedScriptPath:27,disableNewAgentCreation:28},null,[-1,-1])}}function Pr(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=et(s,e[o]);return{c(){t=Mt("svg"),n=new Et(!0),this.h()},l(o){t=Tt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Gt(t);n=Ht(c,!0),c.forEach(b),this.h()},h(){n.a=null,yt(t,s)},m(o,c){Ot(o,t,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',t)},p(o,[c]){yt(t,s=Vt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&o[0]]))},i:M,o:M,d(o){o&&b(t)}}}function Dr(r,t,n){return r.$$set=e=>{n(0,t=et(et({},t),xt(e)))},[t=xt(t)]}class Nr extends ot{constructor(t){super(),rt(this,t,Dr,Pr,ct,{})}}function rn(r){let t,n;return t=new ee({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[Hr],default:[Gr]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p(e,s){const o={};16414&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function zr(r){let t;return{c(){t=G(r[4])},m(n,e){y(n,t,e)},p(n,e){16&e&&K(t,n[4])},d(n){n&&b(t)}}}function Mr(r){let t,n;return t=new En({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Er(r){let t,n;return t=new Nr({}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Tr(r){let t,n,e,s;const o=[Er,Mr],c=[];function i(a,l){return a[1]?0:1}return n=i(r),e=c[n]=o[n](r),{c(){t=A("div"),e.c(),_(t,"slot","iconLeft")},m(a,l){y(a,t,l),c[n].m(t,null),s=!0},p(a,l){let u=n;n=i(a),n!==u&&(O(),f(c[u],1,1,()=>{c[u]=null}),V(),e=c[n],e||(e=c[n]=o[n](a),e.c()),m(e,1),e.m(t,null))},i(a){s||(m(e),s=!0)},o(a){f(e),s=!1},d(a){a&&b(t),c[n].d()}}}function Gr(r){let t,n,e,s,o,c,i=(r[2]?ce:ae).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return o=new qt({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[Tr],default:[zr]},$$scope:{ctx:r}}}),o.$on("click",r[11]),{c(){t=A("div"),n=A("p"),e=G(i),s=P(),x(o.$$.fragment),_(n,"class","svelte-f3wuoa"),_(t,"class","agent-limit-message svelte-f3wuoa")},m(a,l){y(a,t,l),k(t,n),k(n,e),k(t,s),R(o,t,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?ce:ae).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&K(e,i);const u={};16402&l&&(u.$$scope={dirty:l,ctx:a}),o.$set(u)},i(a){c||(m(o.$$.fragment,a),c=!0)},o(a){f(o.$$.fragment,a),c=!1},d(a){a&&b(t),L(o)}}}function Hr(r){let t,n;return t=new Mn({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(e,s){R(t,e,s),n=!0},p:M,i(e){n||(m(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Or(r){let t,n,e=!!r[0]&&rn(r);return{c(){e&&e.c(),t=it()},m(s,o){e&&e.m(s,o),y(s,t,o),n=!0},p(s,[o]){s[0]?e?(e.p(s,o),1&o&&m(e,1)):(e=rn(s),e.c(),m(e,1),e.m(t.parentNode,t)):e&&(O(),f(e,1,1,()=>{e=null}),V())},i(s){n||(m(e),n=!0)},o(s){f(e),n=!1},d(s){s&&b(t),e&&e.d(s)}}}function cn(r){if(!r)return;const t=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:t.length>30?t.substring(0,27)+"...":t}}function an(r,t){return r.replace("%MAX_AGENTS%",t.toString())}function Vr(r,t,n){let e,s,o,{agentLimitErrorMessage:c}=t;const i=Ft(ne.key);At(r,i,$=>n(3,o=$));let a,l,u,d=!1,w=[];function g(){return o.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!d&&(a!=null&&a.id))try{d=!0,await i.deleteAgent(a.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{d=!1}}async function p(){if(!d&&(l!=null&&l.id))try{d=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{d=!1}}return r.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,e=!!o.maxRemoteAgents&&o.agentOverviews.length>=o.maxRemoteAgents),8&r.$$.dirty&&n(1,s=!!o.maxActiveRemoteAgents&&o.agentOverviews.filter($=>$.workspace_status===Le.workspaceRunning).length>=o.maxActiveRemoteAgents),1806&r.$$.dirty)if(e)n(10,w=g()),n(8,a=cn(w[0])),n(0,c=an(ce,o.maxRemoteAgents)),n(4,u="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(s){n(10,w=g());const $=w.filter(v=>v.workspace_status===Le.workspaceRunning);n(9,l=cn($[0])),n(0,c=an(ae,o.maxActiveRemoteAgents)),n(4,u="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,s,e,o,u,i,h,p,a,l,w,()=>{s?p():h()}]}class jr extends ot{constructor(t){super(),rt(this,t,Vr,Or,ct,{agentLimitErrorMessage:0})}}function ln(r){let t,n,e,s;return n=new ee({props:{color:"error",variant:"soft",size:2,$$slots:{default:[qr]},$$scope:{ctx:r}}}),{c(){t=A("div"),x(n.$$.fragment),_(t,"class","error-message svelte-gbnap0")},m(o,c){y(o,t,c),R(n,t,null),s=!0},p(o,c){const i={};131080&c&&(i.$$scope={dirty:c,ctx:o}),n.$set(i)},i(o){s||(m(n.$$.fragment,o),o&&pn(()=>{s&&(e||(e=Yt(t,Jt,{y:10},!0)),e.run(1))}),s=!0)},o(o){f(n.$$.fragment,o),o&&(e||(e=Yt(t,Jt,{y:10},!1)),e.run(0)),s=!1},d(o){o&&b(t),L(n),o&&e&&e.end()}}}function qr(r){let t,n=r[3].remoteAgentCreationError+"";return{c(){t=G(n)},m(e,s){y(e,t,s)},p(e,s){8&s&&n!==(n=e[3].remoteAgentCreationError+"")&&K(t,n)},d(e){e&&b(t)}}}function Kr(r){let t,n,e,s,o,c,i,a,l,u,d,w,g,h,p,$,v,B,F,E,I,z,U;function tt(N){r[14](N)}let nt={};r[2]!==void 0&&(nt.agentLimitErrorMessage=r[2]),i=new jr({props:nt}),vt.push(()=>bt(i,"agentLimitErrorMessage",tt));let H=r[3].remoteAgentCreationError&&ln(r);function st(N){r[15](N)}function St(N){r[16](N)}let gt={lastUsedRepoUrl:r[4],lastUsedBranchName:r[5]};return r[0]!==void 0&&(gt.errorMessage=r[0]),r[1]!==void 0&&(gt.isLoading=r[1]),p=new To({props:gt}),vt.push(()=>bt(p,"errorMessage",st)),vt.push(()=>bt(p,"isLoading",St)),p.$on("commitRefChange",r[8]),z=new Fr({props:{lastUsedScriptPath:r[6]}}),z.$on("setupScriptChange",r[9]),{c(){t=A("div"),n=A("div"),e=A("div"),e.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-gbnap0">in parallel</strong>, in an
        <strong class="svelte-gbnap0">isolated environment</strong>
        that will keep running, <strong class="svelte-gbnap0">even when you shut off your laptop</strong>.</p>`,s=P(),o=A("div"),c=A("div"),x(i.$$.fragment),u=P(),H&&H.c(),d=P(),w=A("div"),w.textContent="Start from any GitHub repo and branch:",g=P(),h=A("div"),x(p.$$.fragment),B=P(),F=A("div"),F.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,E=P(),I=A("div"),x(z.$$.fragment),_(e,"class","main-description svelte-gbnap0"),_(c,"class","error-message svelte-gbnap0"),_(w,"class","description svelte-gbnap0"),_(h,"class","commit-ref-selector svelte-gbnap0"),_(F,"class","description svelte-gbnap0"),_(I,"class","setup-script svelte-gbnap0"),_(o,"class","form-fields"),_(n,"class","content svelte-gbnap0"),_(t,"class","remote-agent-setup svelte-gbnap0")},m(N,Q){y(N,t,Q),k(t,n),k(n,e),k(n,s),k(n,o),k(o,c),R(i,c,null),k(o,u),H&&H.m(o,null),k(o,d),k(o,w),k(o,g),k(o,h),R(p,h,null),k(o,B),k(o,F),k(o,E),k(o,I),R(z,I,null),U=!0},p(N,[Q]){const ht={};!a&&4&Q&&(a=!0,ht.agentLimitErrorMessage=N[2],_t(()=>a=!1)),i.$set(ht),N[3].remoteAgentCreationError?H?(H.p(N,Q),8&Q&&m(H,1)):(H=ln(N),H.c(),m(H,1),H.m(o,d)):H&&(O(),f(H,1,1,()=>{H=null}),V());const Z={};16&Q&&(Z.lastUsedRepoUrl=N[4]),32&Q&&(Z.lastUsedBranchName=N[5]),!$&&1&Q&&($=!0,Z.errorMessage=N[0],_t(()=>$=!1)),!v&&2&Q&&(v=!0,Z.isLoading=N[1],_t(()=>v=!1)),p.$set(Z);const lt={};64&Q&&(lt.lastUsedScriptPath=N[6]),z.$set(lt)},i(N){U||(m(i.$$.fragment,N),N&&pn(()=>{U&&(l||(l=Yt(c,Jt,{y:10},!0)),l.run(1))}),m(H),m(p.$$.fragment,N),m(z.$$.fragment,N),U=!0)},o(N){f(i.$$.fragment,N),N&&(l||(l=Yt(c,Jt,{y:10},!1)),l.run(0)),f(H),f(p.$$.fragment,N),f(z.$$.fragment,N),U=!1},d(N){N&&b(t),L(i),N&&l&&l.end(),H&&H.d(),L(p),L(z)}}}function Wr(r,t,n){let e,s,o,c,i;const a=Ft(ne.key);At(r,a,p=>n(3,i=p));let l,u="",d=!1,w=null,g=null,h=null;return te(async()=>{try{const p=await a.getLastRemoteAgentSetup();n(4,w=p.lastRemoteAgentGitRepoUrl),n(5,g=p.lastRemoteAgentGitBranch),n(6,h=p.lastRemoteAgentSetupScript),a.setHasEverUsedRemoteAgent(!0),await a.reportRemoteAgentEvent({eventName:kn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(p){console.error("Failed to load last remote agent setup:",p)}}),un(()=>{a.setNewAgentDraft(null),a.setCreationMetrics(void 0)}),r.$$.update=()=>{var p,$;8&r.$$.dirty&&n(13,e=((p=i.newAgentDraft)==null?void 0:p.commitRef)??null),8&r.$$.dirty&&n(12,s=(($=i.newAgentDraft)==null?void 0:$.selectedBranch)??null),12295&r.$$.dirty&&n(11,o=(()=>{var F;const v=(F=e==null?void 0:e.github_commit_ref)==null?void 0:F.repository_url,B=s==null?void 0:s.name;return u||l||(d?"Loading repos and branches...":"")||!v&&"Please select a repository"||!B&&"Please select a branch"||(!(!d&&v&&B)&&v&&B?"Loading branch data...":"")||""})()),2048&r.$$.dirty&&n(10,c=!!o),1024&r.$$.dirty&&a.newAgentDraft&&a.newAgentDraft.isDisabled!==c&&a.setNewAgentDraft({...a.newAgentDraft,isDisabled:c})},[u,d,l,i,w,g,h,a,async function(p){a.setRemoteAgentCreationError(null);const $=a.newAgentDraft;$?a.setNewAgentDraft({...$,commitRef:p.detail.commitRef,selectedBranch:p.detail.selectedBranch,isDisabled:c}):a.setNewAgentDraft({commitRef:p.detail.commitRef,selectedBranch:p.detail.selectedBranch,setupScript:null,isDisabled:c,enableNotification:!0})},function(p){a.setRemoteAgentCreationError(null);const $=a.newAgentDraft;$?a.setNewAgentDraft({...$,setupScript:p.detail.script,isDisabled:c}):a.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:p.detail.script,isDisabled:c,enableNotification:!0})},c,o,s,e,function(p){l=p,n(2,l)},function(p){u=p,n(0,u)},function(p){d=p,n(1,d)}]}class qc extends ot{constructor(t){super(),rt(this,t,Wr,Kr,ct,{})}}export{qc as default};
