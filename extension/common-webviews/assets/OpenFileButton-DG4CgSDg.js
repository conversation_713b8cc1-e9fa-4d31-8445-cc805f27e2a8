import{S as X,i as Y,s as oo,C as io,D as lo,W as P,E as D,c as j,e as I,F,G as ao,u as m,t as d,h as N,I as V,Z as B,_ as eo,a as q,j as ro,A as no,q as E,r as M,ab as h,g as so,a4 as co,$ as L,a0 as T,a1 as b,a2 as z,J as G,n as to,ag as uo,ah as po}from"./SpinnerAugment-CQKp6jSN.js";import{C as $o,g as fo,R as mo}from"./check-DlU29TPV.js";import"./BaseButton-ESlFPUk1.js";import{A as ho}from"./arrow-up-right-from-square-ChzPb9WB.js";import{T as Co}from"./Content-D7Q35t53.js";import{B as go}from"./ButtonAugment-CAn8LxGl.js";import{I as ko}from"./IconButtonAugment-D-fvrWAT.js";import{T as vo}from"./TextTooltipAugment--NM_J2iY.js";const yo=s=>({}),H=s=>({}),xo=s=>({}),J=s=>({slot:"iconRight"}),Oo=s=>({}),Z=s=>({}),wo=s=>({}),K=s=>({});function Lo(s){let o,n;const e=[s[8],{color:s[4]},{variant:s[7]}];let c={$$slots:{iconRight:[Ro],iconLeft:[No],default:[bo]},$$scope:{ctx:s}};for(let t=0;t<e.length;t+=1)c=q(c,e[t]);return o=new go({props:c}),o.$on("click",s[10]),o.$on("keyup",s[29]),o.$on("keydown",s[30]),o.$on("mousedown",s[31]),o.$on("mouseover",s[32]),o.$on("focus",s[33]),o.$on("mouseleave",s[34]),o.$on("blur",s[35]),o.$on("contextmenu",s[36]),{c(){D(o.$$.fragment)},m(t,i){F(o,t,i),n=!0},p(t,i){const a=400&i[0]?so(e,[256&i[0]&&co(t[8]),16&i[0]&&{color:t[4]},128&i[0]&&{variant:t[7]}]):{};2058&i[0]|128&i[1]&&(a.$$scope={dirty:i,ctx:t}),o.$set(a)},i(t){n||(m(o.$$.fragment,t),n=!0)},o(t){d(o.$$.fragment,t),n=!1},d(t){V(o,t)}}}function To(s){let o,n;const e=[s[8],{color:s[4]},{variant:s[7]}];let c={$$slots:{default:[So]},$$scope:{ctx:s}};for(let t=0;t<e.length;t+=1)c=q(c,e[t]);return o=new ko({props:c}),o.$on("click",s[10]),o.$on("keyup",s[21]),o.$on("keydown",s[22]),o.$on("mousedown",s[23]),o.$on("mouseover",s[24]),o.$on("focus",s[25]),o.$on("mouseleave",s[26]),o.$on("blur",s[27]),o.$on("contextmenu",s[28]),{c(){D(o.$$.fragment)},m(t,i){F(o,t,i),n=!0},p(t,i){const a=400&i[0]?so(e,[256&i[0]&&co(t[8]),16&i[0]&&{color:t[4]},128&i[0]&&{variant:t[7]}]):{};128&i[1]&&(a.$$scope={dirty:i,ctx:t}),o.$set(a)},i(t){n||(m(o.$$.fragment,t),n=!0)},o(t){d(o.$$.fragment,t),n=!1},d(t){V(o,t)}}}function bo(s){let o;const n=s[20].default,e=L(n,s,s[38],null);return{c(){e&&e.c()},m(c,t){e&&e.m(c,t),o=!0},p(c,t){e&&e.p&&(!o||128&t[1])&&T(e,n,c,c[38],o?z(n,c[38],t,null):b(c[38]),null)},i(c){o||(m(e,c),o=!0)},o(c){d(e,c),o=!1},d(c){e&&e.d(c)}}}function zo(s){let o;const n=s[20].iconLeft,e=L(n,s,s[38],H);return{c(){e&&e.c()},m(c,t){e&&e.m(c,t),o=!0},p(c,t){e&&e.p&&(!o||128&t[1])&&T(e,n,c,c[38],o?z(n,c[38],t,yo):b(c[38]),H)},i(c){o||(m(e,c),o=!0)},o(c){d(e,c),o=!1},d(c){e&&e.d(c)}}}function Io(s){let o,n;return o=new $o({}),{c(){D(o.$$.fragment)},m(e,c){F(o,e,c),n=!0},p:to,i(e){n||(m(o.$$.fragment,e),n=!0)},o(e){d(o.$$.fragment,e),n=!1},d(e){V(o,e)}}}function No(s){let o,n,e,c;const t=[Io,zo],i=[];function a(r,p){return r[11].iconLeft&&r[3]==="success"&&r[1]?0:1}return n=a(s),e=i[n]=t[n](s),{c(){o=P("svelte.fragment"),e.c(),j(o,"slot","iconLeft")},m(r,p){I(r,o,p),i[n].m(o,null),c=!0},p(r,p){let u=n;n=a(r),n===u?i[n].p(r,p):(E(),d(i[u],1,1,()=>{i[u]=null}),M(),e=i[n],e?e.p(r,p):(e=i[n]=t[n](r),e.c()),m(e,1),e.m(o,null))},i(r){c||(m(e),c=!0)},o(r){d(e),c=!1},d(r){r&&N(o),i[n].d()}}}function Ro(s){let o;const n=s[20].iconRight,e=L(n,s,s[38],J);return{c(){e&&e.c()},m(c,t){e&&e.m(c,t),o=!0},p(c,t){e&&e.p&&(!o||128&t[1])&&T(e,n,c,c[38],o?z(n,c[38],t,xo):b(c[38]),J)},i(c){o||(m(e,c),o=!0)},o(c){d(e,c),o=!1},d(c){e&&e.d(c)}}}function So(s){let o,n,e;const c=s[20].iconLeft,t=L(c,s,s[38],K),i=s[20].default,a=L(i,s,s[38],null),r=s[20].iconRight,p=L(r,s,s[38],Z);return{c(){t&&t.c(),o=G(),a&&a.c(),n=G(),p&&p.c()},m(u,f){t&&t.m(u,f),I(u,o,f),a&&a.m(u,f),I(u,n,f),p&&p.m(u,f),e=!0},p(u,f){t&&t.p&&(!e||128&f[1])&&T(t,c,u,u[38],e?z(c,u[38],f,wo):b(u[38]),K),a&&a.p&&(!e||128&f[1])&&T(a,i,u,u[38],e?z(i,u[38],f,null):b(u[38]),null),p&&p.p&&(!e||128&f[1])&&T(p,r,u,u[38],e?z(r,u[38],f,Oo):b(u[38]),Z)},i(u){e||(m(t,u),m(a,u),m(p,u),e=!0)},o(u){d(t,u),d(a,u),d(p,u),e=!1},d(u){u&&(N(o),N(n)),t&&t.d(u),a&&a.d(u),p&&p.d(u)}}}function Do(s){let o,n,e,c;const t=[To,Lo],i=[];function a(r,p){return r[0]?0:1}return o=a(s),n=i[o]=t[o](s),{c(){n.c(),e=no()},m(r,p){i[o].m(r,p),I(r,e,p),c=!0},p(r,p){let u=o;o=a(r),o===u?i[o].p(r,p):(E(),d(i[u],1,1,()=>{i[u]=null}),M(),n=i[o],n?n.p(r,p):(n=i[o]=t[o](r),n.c()),m(n,1),n.m(e.parentNode,e))},i(r){c||(m(n),c=!0)},o(r){d(n),c=!1},d(r){r&&N(e),i[o].d(r)}}}function Fo(s){let o,n,e,c;function t(a){s[37](a)}let i={onOpenChange:s[9],content:s[6],triggerOn:[Co.Hover],nested:s[2],$$slots:{default:[Do]},$$scope:{ctx:s}};return s[5]!==void 0&&(i.requestClose=s[5]),n=new vo({props:i}),io.push(()=>lo(n,"requestClose",t)),{c(){o=P("div"),D(n.$$.fragment),j(o,"class","c-successful-button svelte-1dvyzw2")},m(a,r){I(a,o,r),F(n,o,null),c=!0},p(a,r){const p={};64&r[0]&&(p.content=a[6]),4&r[0]&&(p.nested=a[2]),2459&r[0]|128&r[1]&&(p.$$scope={dirty:r,ctx:a}),!e&&32&r[0]&&(e=!0,p.requestClose=a[5],ao(()=>e=!1)),n.$set(p)},i(a){c||(m(n.$$.fragment,a),c=!0)},o(a){d(n.$$.fragment,a),c=!1},d(a){a&&N(o),V(n)}}}function Vo(s,o,n){let e,c,t;const i=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","replaceIconOnSuccess","tooltipNested"];let a=B(o,i),{$$slots:r={},$$scope:p}=o;const u=eo(r);let f,y,{defaultColor:O}=o,{tooltip:C}=o,{stateVariant:x}=o,{onClick:R}=o,{tooltipDuration:S=1500}=o,{icon:k=!1}=o,{stickyColor:w=!0}=o,{persistOnTooltipClose:$=!1}=o,{replaceIconOnSuccess:v=!1}=o,{tooltipNested:_}=o,g="neutral",W=O,A=C==null?void 0:C.neutral;return s.$$set=l=>{o=q(q({},o),ro(l)),n(40,a=B(o,i)),"defaultColor"in l&&n(12,O=l.defaultColor),"tooltip"in l&&n(13,C=l.tooltip),"stateVariant"in l&&n(14,x=l.stateVariant),"onClick"in l&&n(15,R=l.onClick),"tooltipDuration"in l&&n(16,S=l.tooltipDuration),"icon"in l&&n(0,k=l.icon),"stickyColor"in l&&n(17,w=l.stickyColor),"persistOnTooltipClose"in l&&n(18,$=l.persistOnTooltipClose),"replaceIconOnSuccess"in l&&n(1,v=l.replaceIconOnSuccess),"tooltipNested"in l&&n(2,_=l.tooltipNested),"$$scope"in l&&n(38,p=l.$$scope)},s.$$.update=()=>{n(19,{variant:e,...c}=a,e,(n(8,c),n(40,a))),540680&s.$$.dirty[0]&&n(7,t=(x==null?void 0:x[g])??e),4104&s.$$.dirty[0]&&n(4,W=g==="success"?"success":g==="failure"?"error":O)},[k,v,_,g,W,f,A,t,c,function(l){$||l||(clearTimeout(y),y=void 0,n(6,A=C==null?void 0:C.neutral),w||n(3,g="neutral"))},async function(l){try{n(3,g=await R(l)??"neutral")}catch{n(3,g="failure")}n(6,A=C==null?void 0:C[g]),clearTimeout(y),y=setTimeout(()=>{f==null||f(),w||n(3,g="neutral")},S)},u,O,C,x,R,S,w,$,e,r,function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){f=l,n(5,f)},p]}class _o extends X{constructor(o){super(),Y(this,o,Vo,Fo,oo,{defaultColor:12,tooltip:13,stateVariant:14,onClick:15,tooltipDuration:16,icon:0,stickyColor:17,persistOnTooltipClose:18,replaceIconOnSuccess:1,tooltipNested:2},null,[-1,-1])}}const jo=s=>({}),Q=s=>({});function U(s){let o,n,e,c;return n=new _o({props:{defaultColor:s[1],stickyColor:s[3],size:s[0],variant:s[2],tooltip:{neutral:"Open File In Editor",success:"Opening file..."},stateVariant:{success:"soft"},onClick:s[4],icon:!s[7].text,$$slots:{iconLeft:[Ao],default:[qo]},$$scope:{ctx:s}}}),{c(){o=P("span"),D(n.$$.fragment),j(o,"class",e="c-open-file-button-container c-open-file-button__size--"+s[0]+" svelte-pdfhuj")},m(t,i){I(t,o,i),F(n,o,null),c=!0},p(t,i){const a={};2&i&&(a.defaultColor=t[1]),8&i&&(a.stickyColor=t[3]),1&i&&(a.size=t[0]),4&i&&(a.variant=t[2]),16&i&&(a.onClick=t[4]),128&i&&(a.icon=!t[7].text),32768&i&&(a.$$scope={dirty:i,ctx:t}),n.$set(a),(!c||1&i&&e!==(e="c-open-file-button-container c-open-file-button__size--"+t[0]+" svelte-pdfhuj"))&&j(o,"class",e)},i(t){c||(m(n.$$.fragment,t),c=!0)},o(t){d(n.$$.fragment,t),c=!1},d(t){t&&N(o),V(n)}}}function qo(s){let o;const n=s[14].text,e=L(n,s,s[15],Q);return{c(){e&&e.c()},m(c,t){e&&e.m(c,t),o=!0},p(c,t){e&&e.p&&(!o||32768&t)&&T(e,n,c,c[15],o?z(n,c[15],t,jo):b(c[15]),Q)},i(c){o||(m(e,c),o=!0)},o(c){d(e,c),o=!1},d(c){e&&e.d(c)}}}function Ao(s){let o,n;return o=new ho({props:{slot:"iconLeft"}}),{c(){D(o.$$.fragment)},m(e,c){F(o,e,c),n=!0},p:to,i(e){n||(m(o.$$.fragment,e),n=!0)},o(e){d(o.$$.fragment,e),n=!1},d(e){V(o,e)}}}function Po(s){let o,n,e=s[5]&&U(s);return{c(){e&&e.c(),o=no()},m(c,t){e&&e.m(c,t),I(c,o,t),n=!0},p(c,[t]){c[5]?e?(e.p(c,t),32&t&&m(e,1)):(e=U(c),e.c(),m(e,1),e.m(o.parentNode,o)):e&&(E(),d(e,1,1,()=>{e=null}),M())},i(c){n||(m(e),n=!0)},o(c){d(e),n=!1},d(c){c&&N(o),e&&e.d(c)}}}function Eo(s,o,n){let e,c,t,i,{$$slots:a={},$$scope:r}=o;const p=eo(a);let{path:u}=o,{start:f=0}=o,{stop:y=0}=o,{size:O=0}=o,{color:C="neutral"}=o,{variant:x="ghost-block"}=o,{stickyColor:R=!1}=o,{onOpenLocalFile:S=async function($){var _,g;if((_=$==null?void 0:$.stopPropagation)==null||_.call($),(g=$==null?void 0:$.preventDefault)==null||g.call($),!u)return;const v=await(k==null?void 0:k.extensionClient.resolvePath({rootPath:"",relPath:u}));return k==null||k.extensionClient.openFile({repoRoot:(v==null?void 0:v.repoRoot)??"",pathName:(v==null?void 0:v.pathName)??"",range:{start:Math.max(f,0),stop:Math.max(y,0)}}),"success"}}=o;const k=fo(),w=uo(mo.key);return po(s,w,$=>n(13,i=$)),s.$$set=$=>{"path"in $&&n(8,u=$.path),"start"in $&&n(9,f=$.start),"stop"in $&&n(10,y=$.stop),"size"in $&&n(0,O=$.size),"color"in $&&n(1,C=$.color),"variant"in $&&n(2,x=$.variant),"stickyColor"in $&&n(3,R=$.stickyColor),"onOpenLocalFile"in $&&n(4,S=$.onOpenLocalFile),"$$scope"in $&&n(15,r=$.$$scope)},s.$$.update=()=>{8192&s.$$.dirty&&n(11,e=i==null?void 0:i.isRemoteAgentSshWindow),8192&s.$$.dirty&&n(12,c=!!(i!=null&&i.isActive)),6144&s.$$.dirty&&n(5,t=!c||e)},[O,C,x,R,S,t,w,p,u,f,y,e,c,i,a,r]}class Uo extends X{constructor(o){super(),Y(this,o,Eo,Po,oo,{path:8,start:9,stop:10,size:0,color:1,variant:2,stickyColor:3,onOpenLocalFile:4})}}export{Uo as O,_o as S};
