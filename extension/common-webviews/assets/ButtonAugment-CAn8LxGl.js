import{S as H,i as K,s as M,a as x,E as A,F as D,g as N,a4 as O,u as d,t as m,I as G,Z as E,_ as P,j as Q,ab as g,W as h,J as F,c as f,a3 as I,e as p,f as J,q as b,r as z,h as v,$ as C,a0 as j,a1 as w,a2 as y,T as U}from"./SpinnerAugment-CQKp6jSN.js";import{B as V}from"./BaseButton-ESlFPUk1.js";const X=o=>({}),S=o=>({}),Y=o=>({}),T=o=>({});function W(o){let t,l;const c=o[10].iconLeft,a=C(c,o,o[20],T);return{c(){t=h("div"),a&&a.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,s){p(i,t,s),a&&a.m(t,null),l=!0},p(i,s){a&&a.p&&(!l||1048576&s)&&j(a,c,i,i[20],l?y(c,i[20],s,Y):w(i[20]),T)},i(i){l||(d(a,i),l=!0)},o(i){m(a,i),l=!1},d(i){i&&v(t),a&&a.d(i)}}}function Z(o){let t,l,c;return l=new U({props:{size:o[0]===.5?1:o[0],weight:o[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:o}}}),{c(){t=h("div"),A(l.$$.fragment),f(t,"class","c-button--text svelte-1u3rjsd")},m(a,i){p(a,t,i),D(l,t,null),c=!0},p(a,i){const s={};1&i&&(s.size=a[0]===.5?1:a[0]),2&i&&(s.weight=a[1]==="ghost"?"regular":"medium"),1048576&i&&(s.$$scope={dirty:i,ctx:a}),l.$set(s)},i(a){c||(d(l.$$.fragment,a),c=!0)},o(a){m(l.$$.fragment,a),c=!1},d(a){a&&v(t),G(l)}}}function tt(o){let t;const l=o[10].default,c=C(l,o,o[20],null);return{c(){c&&c.c()},m(a,i){c&&c.m(a,i),t=!0},p(a,i){c&&c.p&&(!t||1048576&i)&&j(c,l,a,a[20],t?y(l,a[20],i,null):w(a[20]),null)},i(a){t||(d(c,a),t=!0)},o(a){m(c,a),t=!1},d(a){c&&c.d(a)}}}function _(o){let t,l;const c=o[10].iconRight,a=C(c,o,o[20],S);return{c(){t=h("div"),a&&a.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,s){p(i,t,s),a&&a.m(t,null),l=!0},p(i,s){a&&a.p&&(!l||1048576&s)&&j(a,c,i,i[20],l?y(c,i[20],s,X):w(i[20]),S)},i(i){l||(d(a,i),l=!0)},o(i){m(a,i),l=!1},d(i){i&&v(t),a&&a.d(i)}}}function it(o){let t,l,c,a,i,s=o[9].iconLeft&&W(o),r=o[9].default&&Z(o),u=o[9].iconRight&&_(o);return{c(){t=h("div"),s&&s.c(),l=F(),r&&r.c(),c=F(),u&&u.c(),f(t,"class",a=I(`c-button--content c-button--size-${o[0]}`)+" svelte-1u3rjsd")},m(n,$){p(n,t,$),s&&s.m(t,null),J(t,l),r&&r.m(t,null),J(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?s?(s.p(n,$),512&$&&d(s,1)):(s=W(n),s.c(),d(s,1),s.m(t,l)):s&&(b(),m(s,1,1,()=>{s=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=Z(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),m(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=_(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),m(u,1,1,()=>{u=null}),z()),(!i||1&$&&a!==(a=I(`c-button--content c-button--size-${n[0]}`)+" svelte-1u3rjsd"))&&f(t,"class",a)},i(n){i||(d(s),d(r),d(u),i=!0)},o(n){m(s),m(r),m(u),i=!1},d(n){n&&v(t),s&&s.d(),r&&r.d(),u&&u.d()}}}function at(o){let t,l;const c=[{size:o[0]},{variant:o[1]},{color:o[2]},{highContrast:o[3]},{disabled:o[4]},{loading:o[6]},{alignment:o[7]},{radius:o[5]},o[8]];let a={$$slots:{default:[it]},$$scope:{ctx:o}};for(let i=0;i<c.length;i+=1)a=x(a,c[i]);return t=new V({props:a}),t.$on("click",o[11]),t.$on("keyup",o[12]),t.$on("keydown",o[13]),t.$on("mousedown",o[14]),t.$on("mouseover",o[15]),t.$on("focus",o[16]),t.$on("mouseleave",o[17]),t.$on("blur",o[18]),t.$on("contextmenu",o[19]),{c(){A(t.$$.fragment)},m(i,s){D(t,i,s),l=!0},p(i,[s]){const r=511&s?N(c,[1&s&&{size:i[0]},2&s&&{variant:i[1]},4&s&&{color:i[2]},8&s&&{highContrast:i[3]},16&s&&{disabled:i[4]},64&s&&{loading:i[6]},128&s&&{alignment:i[7]},32&s&&{radius:i[5]},256&s&&O(i[8])]):{};1049091&s&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){m(t.$$.fragment,i),l=!1},d(i){G(t,i)}}}function ot(o,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let a=E(t,c),{$$slots:i={},$$scope:s}=t;const r=P(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="neutral"}=t,{highContrast:k=!1}=t,{disabled:L=!1}=t,{radius:R="medium"}=t,{loading:B=!1}=t,{alignment:q="center"}=t;return o.$$set=e=>{t=x(x({},t),Q(e)),l(8,a=E(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,k=e.highContrast),"disabled"in e&&l(4,L=e.disabled),"radius"in e&&l(5,R=e.radius),"loading"in e&&l(6,B=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,s=e.$$scope)},[u,n,$,k,L,R,B,q,a,r,i,function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},function(e){g.call(this,o,e)},s]}class et extends H{constructor(t){super(),K(this,t,ot,at,M,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
