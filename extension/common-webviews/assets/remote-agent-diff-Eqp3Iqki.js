var Di=Object.defineProperty;var Xt=r=>{throw TypeError(r)};var Fi=(r,e,t)=>e in r?Di(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var ge=(r,e,t)=>Fi(r,typeof e!="symbol"?e+"":e,t),xi=(r,e,t)=>e.has(r)||Xt("Cannot "+t);var Kt=(r,e,t)=>e.has(r)?Xt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var ht=(r,e,t)=>(xi(r,e,"access private method"),t);import{N as We,S as se,i as ie,s as ee,W as y,c as x,Y as Ae,e as h,f as B,n as te,h as m,C as Oe,K as N,M as le,E as z,F as L,u as f,t as g,I as M,J as j,ac as Ee,q as W,r as Q,ao as Ss,ah as Te,a5 as Ps,a6 as tt,L as Me,A as xe,ap as xt,an as Mt,ag as Je,D as Ye,G as Xe,ai as Is,T as $e,aC as Se,a8 as lt,aa as qs,ae as ki,af as en,b as js,$ as _e,a as Vs,az as kt,a0 as Be,a1 as ze,a2 as Le,g as Us,aA as tn,aB as Ci,ab as vi,ad as wi,aj as Pt}from"./SpinnerAugment-CQKp6jSN.js";import"./design-system-init-D-hN7xfd.js";import{g as mt,p as yi,a as Ai}from"./index-DhtTPDph.js";import"./design-system-init-CWrSWzOd.js";import{W as He,e as de,u as Zs,o as Hs,h as Ws}from"./BaseButton-ESlFPUk1.js";import{T as qe,M as bi}from"./TextTooltipAugment--NM_J2iY.js";import{s as nn}from"./index-CMtlLYew.js";import{c as Ct,p as Ei,g as je,M as _i,O as It,P as ct,b as Bi,i as zi,d as Li}from"./diff-utils-Dvc7ppQm.js";import{a as Qs,C as Gs,T as Js,b as Mi}from"./CollapseButtonAugment--cD032dy.js";import{a as qt,g as jt,b as Ys,S as Ri,M as Ni}from"./index-csTQmXPq.js";import{I as dt,A as Ti}from"./IconButtonAugment-D-fvrWAT.js";import{V as pt}from"./VSCodeCodicon-CONIBlZ6.js";import{B as Ue}from"./ButtonAugment-CAn8LxGl.js";import{M as bt}from"./MaterialIcon-CprIOK2c.js";import{n as Xs,a as Qe,g as oe}from"./file-paths-BcSg4gks.js";import{T as Ke}from"./Content-D7Q35t53.js";import{F as Oi}from"./types-DDm27S8B.js";import{L as Si}from"./LanguageIcon-CBQUAmpN.js";import{g as Pi}from"./globals-D0QH3NT1.js";import{R as Ii}from"./ra-diff-ops-model-CRIDwIDf.js";import{E as qi}from"./expand-BOHHm5_f.js";import{E as Ks}from"./exclamation-triangle-CEPjk4z2.js";import"./toggleHighContrast-Th-X2FgN.js";import{F as ji}from"./Filespan-BI7GEaM4.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C4SxYn1J.js";class vt{constructor(e){ge(this,"_opts",null);ge(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:He.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===He.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}ge(vt,"key","remoteAgentDiffModel");class st{constructor(e){ge(this,"_applyingFilePaths",We([]));ge(this,"_appliedFilePaths",We([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:He.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:He.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,t){try{const n=await this._asyncMsgSender.send({type:He.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}},1e5);return{explanation:n.data.explanation,error:n.data.error}}catch(n){return console.error("Failed to get descriptions:",n),{explanation:[],error:`Failed to get descriptions: ${n instanceof Error?n.message:String(n)}`}}}async applyChanges(e,t,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{const s=await this._asyncMsgSender.send({type:He.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4),{success:i,hasConflicts:o,error:l}=s.data;return i?this._appliedFilePaths.update(a=>[...a.filter(u=>u!==e),e]):l&&console.error("Failed to apply changes:",l),{success:i,hasConflicts:o,error:l}}catch(s){return console.error("applyChanges error",s),{success:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}async openFile(e){try{const t=await this._asyncMsgSender.send({type:He.openFileRequest,data:{path:e}},1e4);return t.data.success||console.error("Failed to open file:",t.data.error),t.data.success}catch(t){console.error("openFile error",t)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:He.reportAgentChangesApplied})}}ge(st,"key","remoteAgentsDiffOpsModel");function sn(r,e,t,n,s={}){const{context:i=3,generateId:o=!0}=s,l=Ct(r,e,t,n,"","",{context:i}),a=e||r;let u;return o?u=`${je(a)}-${je(t+n)}`:u=Math.random().toString(36).substring(2,15),{id:u,path:a,diff:l,originalCode:t,modifiedCode:n}}function Rt(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function ei(r){return!r.originalCode||r.originalCode.trim()===""}function ti(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class Vi{static generateDiff(e,t,n,s){return sn(e,t,n,s)}static generateDiffs(e){return function(t,n={}){return t.map(s=>sn(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(e)}static getDiffStats(e){return Rt(e)}static getDiffObjectStats(e){return Rt(e.diff)}static isNewFile(e){return ei(e)}static isDeletedFile(e){return ti(e)}}function Ui(r){let e;return{c(){e=N(r[1])},m(t,n){h(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&m(e)}}}function Zi(r){let e;return{c(){e=N(r[1])},m(t,n){h(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&m(e)}}}function Hi(r){let e,t,n;function s(l,a){return l[2]?Zi:Ui}let i=s(r),o=i(r);return{c(){e=y("span"),t=y("code"),o.c(),x(t,"class","markdown-codespan svelte-11ta4gi"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){h(l,e,a),B(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:te,o:te,d(l){l&&m(e),o.d(),r[6](null)}}}function Wi(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(u){Oe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}let Qi=class extends se{constructor(r){super(),ie(this,r,Wi,Hi,ee,{token:5,element:0})}};function Gi(r){let e,t;return e=new _i({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ji(r,e,t){let{markdown:n}=e;const s={codespan:Qi};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let ni=class extends se{constructor(r){super(),ie(this,r,Ji,Gi,ee,{markdown:0})}};function rn(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function on(r){let e,t,n,s,i;t=new dt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ki]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=de(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=ln(rn(r,o,u));const a=u=>g(l[u],1,1,()=>{l[u]=null});return{c(){e=y("div"),z(t.$$.fragment),n=j(),s=y("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(s,"class","descriptions svelte-14s1ghg"),Ee(s,"transform","translateY("+-r[4]+"px)")},m(u,c){h(u,e,c),L(t,e,null),h(u,n,c),h(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=de(u[1]),p=0;p<o.length;p+=1){const k=rn(u,o,p);l[p]?(l[p].p(k,c),f(l[p],1)):(l[p]=ln(k),l[p].c(),f(l[p],1),l[p].m(s,null))}for(W(),p=o.length;p<l.length;p+=1)a(p);Q()}(!i||16&c[0])&&Ee(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);i=!0}},o(u){g(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)g(l[c]);i=!1},d(u){u&&(m(e),m(n),m(s)),M(t),Me(l,u)}}}function Yi(r){let e,t;return e=new pt({props:{icon:"book"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Xi(r){let e,t;return e=new pt({props:{icon:"x"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ki(r){let e,t,n,s;const i=[Xi,Yi],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function ln(r){let e,t,n,s;return t=new ni({props:{markdown:r[47].text}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),x(e,"class","description svelte-14s1ghg"),Ee(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ee(e,"--user-theme-sidebar-background","transparent")},m(i,o){h(i,e,o),L(t,e,null),B(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ee(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&m(e),M(t)}}}function er(r){let e,t,n,s,i=r[1].length>0&&on(r);return{c(){e=y("div"),t=y("div"),n=j(),i&&i.c(),x(t,"class","editor-container svelte-14s1ghg"),Ee(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){h(o,e,l),B(e,t),r[23](t),B(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ee(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=on(o),i.c(),f(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){g(i),s=!1},d(o){o&&m(e),r[23](null),i&&i.d()}}}function tr(r,e,t){let n,s,i;const o=Ss();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:k=[]}=e,{theme:w}=e,{areDescriptionsVisible:v=!0}=e,{isNewFile:b=!1}=e,{isDeletedFile:A=!1}=e;const _=qt.getContext().monaco;let $,D,F,E;Te(r,_,C=>t(22,n=C));let T,I=[];const q=jt();let U,H=We(0);Te(r,H,C=>t(4,s=C));let K=b?20*a.split(`
`).length+40:100;const ce=n?n.languages.getLanguages().map(C=>C.id):[];function ue(C,R){var S,G;if(R){const V=(S=R.split(".").pop())==null?void 0:S.toLowerCase();if(V){const X=(G=n==null?void 0:n.languages.getLanguages().find(Z=>{var Y;return(Y=Z.extensions)==null?void 0:Y.includes("."+V)}))==null?void 0:G.id;if(X&&ce.includes(X))return X}}return"plaintext"}const ae=We({});Te(r,ae,C=>t(5,i=C));let J=null;function fe(){if(!$)return;I=I.filter(S=>(S.dispose(),!1));const C=$.getOriginalEditor(),R=$.getModifiedEditor();I.push(C.onDidScrollChange(()=>{xt(H,s=C.getScrollTop(),s)}),R.onDidScrollChange(()=>{xt(H,s=R.getScrollTop(),s)}))}function he(){if(!$||!T)return;const C=$.getOriginalEditor(),R=$.getModifiedEditor();I.push(R.onDidContentSizeChange(()=>q.requestLayout()),C.onDidContentSizeChange(()=>q.requestLayout()),$.onDidUpdateDiff(()=>q.requestLayout()),R.onDidChangeHiddenAreas(()=>q.requestLayout()),C.onDidChangeHiddenAreas(()=>q.requestLayout()),R.onDidLayoutChange(()=>q.requestLayout()),C.onDidLayoutChange(()=>q.requestLayout()),R.onDidFocusEditorWidget(()=>{ke(!0)}),C.onDidFocusEditorWidget(()=>{ke(!0)}),R.onDidBlurEditorWidget(()=>{ke(!1)}),C.onDidBlurEditorWidget(()=>{ke(!1)}),R.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const S=(E==null?void 0:E.getValue())||"";if(S===a)return;const G=S.replace(p.join(""),"").replace(k.join(""),"");o("codeChange",{modifiedCode:G});const V=setTimeout(()=>{Ce=!1},500);I.push({dispose:()=>clearTimeout(V)})})),function(){!T||!$||(J&&clearTimeout(J),J=setTimeout(()=>{if(!T.__hasClickListener){const S=G=>{const V=G.target;V&&(V.closest('[title="Show Unchanged Region"]')||V.closest('[title="Hide Unchanged Region"]'))&&Fe()};T.addEventListener("click",S),t(2,T.__hasClickListener=!0,T),I.push({dispose:()=>{T.removeEventListener("click",S)}})}$&&I.push($.onDidUpdateDiff(()=>{Fe()}))},300))}()}Ps(()=>{$==null||$.dispose(),D==null||D.dispose(),F==null||F.dispose(),E==null||E.dispose(),I.forEach(C=>C.dispose()),J&&clearTimeout(J),U==null||U()});let ne=null;function Fe(){ne&&clearTimeout(ne),ne=setTimeout(()=>{q.requestLayout(),ne=null},100),ne&&I.push({dispose:()=>{ne&&(clearTimeout(ne),ne=null)}})}function ye(C,R,S,G=[],V=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");F==null||F.dispose(),E==null||E.dispose(),R=R||"",S=S||"";const X=G.join(""),Z=V.join("");if(R=b?S.split(`
`).map(()=>" ").join(`
`):X+R+Z,S=X+S+Z,F=n.editor.createModel(R,void 0,C!==void 0?n.Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0),A&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,E=n.editor.createModel(S,void 0,C!==void 0?n.Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0)),$){$.setModel({original:F,modified:E});const Y=$.getOriginalEditor();Y&&Y.updateOptions({lineNumbers:"off"}),fe(),J&&clearTimeout(J),J=setTimeout(()=>{he(),J=null},300)}}tt(()=>{if(n)if(b){t(20,D=n.editor.create(T,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:w,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:G=>`${d-p.length+G}`}));const C=ue(0,u);t(21,E=n.editor.createModel(a,C,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(E),I.push(D.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const G=(E==null?void 0:E.getValue())||"";if(G===a)return;o("codeChange",{modifiedCode:G});const V=setTimeout(()=>{Ce=!1},500);I.push({dispose:()=>clearTimeout(V)})})),I.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const R=D.getContentHeight();t(3,K=Math.max(R,60));const S=setTimeout(()=>{D==null||D.layout()},0);I.push({dispose:()=>clearTimeout(S)})}else t(19,$=n.editor.createDiffEditor(T,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:w,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:C=>`${d-p.length+C}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),U&&U(),U=q.registerEditor({editor:$,updateHeight:be,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ye(u,l,a,p,k),fe(),he(),J&&clearTimeout(J),J=setTimeout(()=>{q.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,Re=0;function pe(C,R=!0){return $?(R?$.getModifiedEditor():$.getOriginalEditor()).getTopForLineNumber(C):18*C}function be(){if(!$)return;const C=$.getModel(),R=C==null?void 0:C.original,S=C==null?void 0:C.modified;if(!R||!S)return;const G=$.getOriginalEditor(),V=$.getModifiedEditor(),X=$.getLineChanges()||[];let Z;if(X.length===0){const Y=G.getContentHeight(),re=V.getContentHeight();Z=Math.max(100,Y,re)}else{let Y=0,re=0;for(const we of X)we.originalEndLineNumber>0&&(Y=Math.max(Y,we.originalEndLineNumber)),we.modifiedEndLineNumber>0&&(re=Math.max(re,we.modifiedEndLineNumber));Y=Math.min(Y+3,R.getLineCount()),re=Math.min(re+3,S.getLineCount());const ve=G.getTopForLineNumber(Y),Ne=V.getTopForLineNumber(re);Z=Math.max(ve,Ne)+60}t(3,K=Math.min(Z,2e4)),$.layout(),O()}function ke(C){if(!$)return;const R=$.getOriginalEditor(),S=$.getModifiedEditor();R.updateOptions({scrollbar:{handleMouseWheel:C}}),S.updateOptions({scrollbar:{handleMouseWheel:C}})}function P(C){if(!$)return 0;const R=$.getModel(),S=R==null?void 0:R.original,G=R==null?void 0:R.modified;if(!S||!G)return 0;const V=pe(C.range.start+1,!1),X=pe(C.range.start+1,!0);return V&&!X?V:!V&&X?X:Math.min(V,X)}function O(){if(!$||c.length===0)return;const C={};c.forEach((R,S)=>{C[S]=P(R)}),ae.set(C)}return r.$$set=C=>{"originalCode"in C&&t(10,l=C.originalCode),"modifiedCode"in C&&t(11,a=C.modifiedCode),"path"in C&&t(12,u=C.path),"descriptions"in C&&t(1,c=C.descriptions),"lineOffset"in C&&t(13,d=C.lineOffset),"extraPrefixLines"in C&&t(14,p=C.extraPrefixLines),"extraSuffixLines"in C&&t(15,k=C.extraSuffixLines),"theme"in C&&t(16,w=C.theme),"areDescriptionsVisible"in C&&t(0,v=C.areDescriptionsVisible),"isNewFile"in C&&t(17,b=C.isNewFile),"isDeletedFile"in C&&t(18,A=C.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(C=a,!(Ce||Date.now()-Re<1e3||E&&E.getValue()===p.join("")+C+k.join(""))))if(b&&D){if(E)E.setValue(a);else{const R=ue(0,u);n&&t(21,E=n.editor.createModel(a,R,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),E&&D.setModel(E)}t(3,K=20*a.split(`
`).length+40),D.layout()}else!b&&$&&(ye(u,l,a,p,k),q.requestLayout());var C;if(524290&r.$$.dirty[0]&&$&&c.length>0&&O(),1181696&r.$$.dirty[0]&&b&&a&&D){const R=D.getContentHeight();t(3,K=Math.max(R,60)),D.layout()}},[v,c,T,K,s,i,_,H,ae,P,l,a,u,d,p,k,w,b,A,$,D,E,n,function(C){Oe[C?"unshift":"push"](()=>{T=C,t(2,T)})},()=>t(0,v=!v)]}let nr=class extends se{constructor(r){super(),ie(this,r,tr,er,ee,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}};const sr=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],ir=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],si=1048576;function Vt(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function at(r){switch(Vt(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function an(r){const e=Vt(r);return sr.includes(e)}function un(r){return r>si}const ii=Symbol("focusedPath");function ri(){return Je(ii)}function Nt(r){return`file-diff-${je(r)}`}function rr(r){let e,t,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new nr({props:i}),Oe.push(()=>Ye(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[26]),{c(){z(e.$$.fragment)},m(o,l){L(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Xe(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){g(e.$$.fragment,o),n=!1},d(o){M(e,o)}}}function or(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[ur]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function lr(r){let e,t,n;return t=new $e({props:{$$slots:{default:[fr]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function ar(r){let e,t,n,s;const i=[$r,gr],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=y("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){h(a,e,u),~t&&o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(e),~t&&o[t].d()}}}function ur(r){let e,t,n,s,i,o,l,a=oe(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=N('File "'),t=N(a),n=N('" is too large to display a diff (size: '),s=N(u),i=N(" bytes, max: "),o=N(si),l=N(" bytes).")},m(c,d){h(c,e,d),h(c,t,d),h(c,n,d),h(c,s,d),h(c,i,d),h(c,o,d),h(c,l,d)},p(c,d){4096&d[0]&&a!==(a=oe(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(s,u)},d(c){c&&(m(e),m(t),m(n),m(s),m(i),m(o),m(l))}}}function cr(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file modified: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function dr(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file deleted: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function pr(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file added: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function fr(r){let e;function t(i,o){return i[21]||i[7]?pr:i[8]?dr:cr}let n=t(r),s=n(r);return{c(){s.c(),e=N(`
            No text preview available.`)},m(i,o){s.m(i,o),h(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&m(e),s.d(i)}}}function gr(r){let e,t,n,s,i,o,l,a;e=new $e({props:{class:"image-info-text",$$slots:{default:[Dr]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&cn(r);return{c(){z(e.$$.fragment),t=j(),n=y("img"),o=j(),u&&u.c(),l=xe(),Se(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",s),x(n,"alt",i="Current "+oe(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){L(e,c,d),h(c,t,d),h(c,n,d),h(c,o,d),u&&u.m(c,d),h(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Se(n.src,s="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+oe(c[12])))&&x(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=cn(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(W(),g(u,1,1,()=>{u=null}),Q())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){g(e.$$.fragment,c),g(u),a=!1},d(c){c&&(m(t),m(n),m(o),m(l)),M(e,c),u&&u.d(c)}}}function $r(r){let e,t,n,s;e=new $e({props:{class:"image-info-text",$$slots:{default:[xr]},$$scope:{ctx:r}}});let i=r[0].originalCode&&dn(r);return{c(){z(e.$$.fragment),t=j(),i&&i.c(),n=xe()},m(o,l){L(e,o,l),h(o,t,l),i&&i.m(o,l),h(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=dn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){s||(f(e.$$.fragment,o),f(i),s=!0)},o(o){g(e.$$.fragment,o),g(i),s=!1},d(o){o&&(m(t),m(n)),M(e,o),i&&i.d(o)}}}function hr(r){let e;return{c(){e=N("Image modified")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function mr(r){let e;return{c(){e=N("New image added")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Dr(r){let e,t,n=oe(r[12])+"";function s(l,a){return l[21]||l[7]?mr:hr}let i=s(r),o=i(r);return{c(){o.c(),e=N(": "),t=N(n)},m(l,a){o.m(l,a),h(l,e,a),h(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=oe(l[12])+"")&&le(t,n)},d(l){l&&(m(e),m(t)),o.d(l)}}}function cn(r){let e,t,n,s,i,o;return e=new $e({props:{class:"image-info-text",$$slots:{default:[Fr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),n=y("img"),Se(n.src,s="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+oe(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(e,l,a),h(l,t,a),h(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Se(n.src,s="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+oe(l[12])))&&x(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){g(e.$$.fragment,l),o=!1},d(l){l&&(m(t),m(n)),M(e,l)}}}function Fr(r){let e;return{c(){e=N("Previous version:")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function xr(r){let e,t,n=oe(r[12])+"";return{c(){e=N("Image deleted: "),t=N(n)},m(s,i){h(s,e,i),h(s,t,i)},p(s,i){4096&i[0]&&n!==(n=oe(s[12])+"")&&le(t,n)},d(s){s&&(m(e),m(t))}}}function dn(r){let e,t,n,s,i,o;return e=new $e({props:{class:"image-info-text",$$slots:{default:[kr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),n=y("img"),Se(n.src,s="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+oe(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){L(e,l,a),h(l,t,a),h(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Se(n.src,s="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+oe(l[12])))&&x(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){g(e.$$.fragment,l),o=!1},d(l){l&&(m(t),m(n)),M(e,l)}}}function kr(r){let e;return{c(){e=N("Previous version:")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Cr(r){let e,t,n,s;const i=[ar,lr,or,rr],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=y("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){h(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(e),o[t].d()}}}function vr(r){let e,t=oe(r[12])+"";return{c(){e=N(t)},m(n,s){h(n,e,s)},p(n,s){4096&s[0]&&t!==(t=oe(n[12])+"")&&le(e,t)},d(n){n&&m(e)}}}function wr(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[vr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function pn(r){let e,t,n=Qe(r[12])+"";return{c(){e=y("span"),t=N(n),x(e,"class","c-directory svelte-1536g7w")},m(s,i){h(s,e,i),B(e,t)},p(s,i){4096&i[0]&&n!==(n=Qe(s[12])+"")&&le(t,n)},d(s){s&&m(e)}}}function yr(r){let e,t,n,s=r[23]>0&&fn(r),i=r[22]>0&&gn(r);return{c(){e=y("div"),s&&s.c(),t=j(),i&&i.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){h(o,e,l),s&&s.m(e,null),B(e,t),i&&i.m(e,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=fn(o),s.c(),f(s,1),s.m(e,t)):s&&(W(),g(s,1,1,()=>{s=null}),Q()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=gn(o),i.c(),f(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){n||(f(s),f(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&m(e),s&&s.d(),i&&i.d()}}}function Ar(r){let e;return{c(){e=y("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){h(t,e,n)},p:te,i:te,o:te,d(t){t&&m(e)}}}function fn(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[br]},$$scope:{ctx:r}}}),{c(){e=y("span"),z(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function br(r){let e,t;return{c(){e=N("+"),t=N(r[23])},m(n,s){h(n,e,s),h(n,t,s)},p(n,s){8388608&s[0]&&le(t,n[23])},d(n){n&&(m(e),m(t))}}}function gn(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[Er]},$$scope:{ctx:r}}}),{c(){e=y("span"),z(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Er(r){let e,t;return{c(){e=N("-"),t=N(r[22])},m(n,s){h(n,e,s),h(n,t,s)},p(n,s){4194304&s[0]&&le(t,n[22])},d(n){n&&(m(e),m(t))}}}function _r(r){let e;return{c(){e=N("Apply")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Br(r){let e;return{c(){e=N("Applied")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function zr(r){let e,t,n;return t=new ct({}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Lr(r){let e,t,n;return t=new bt({props:{iconName:"check"}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Mr(r){let e,t,n,s,i;function o(p,k){return p[5]?Br:_r}let l=o(r),a=l(r);const u=[Lr,zr],c=[];function d(p,k){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=j(),n.c(),s=xe()},m(p,k){a.m(p,k),h(p,e,k),c[t].m(p,k),h(p,s,k),i=!0},p(p,k){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let w=t;t=d(p),t!==w&&(W(),g(c[w],1,1,()=>{c[w]=null}),Q(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){g(n),i=!1},d(p){p&&(m(e),m(s)),a.d(p),c[t].d(p)}}}function Rr(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Mr]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function $n(r){let e,t;return e=new qe({props:{content:r[11],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[Tr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Nr(r){let e,t;return e=new It({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Tr(r){let e,t;return e=new dt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Nr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Or(r){let e,t,n,s,i,o,l,a,u,c,d,p,k,w=Qe(r[12]);t=new Gs({}),i=new qe({props:{content:r[11],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[wr]},$$scope:{ctx:r}}});let v=w&&pn(r);const b=[Ar,yr],A=[];function _(D,F){return D[21]?0:1}a=_(r),u=A[a]=b[a](r),d=new qe({props:{content:r[13],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[Rr]},$$scope:{ctx:r}}});let $=r[5]&&$n(r);return{c(){e=y("div"),z(t.$$.fragment),n=j(),s=y("div"),z(i.$$.fragment),o=j(),v&&v.c(),l=j(),u.c(),c=j(),z(d.$$.fragment),p=j(),$&&$.c(),x(s,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,F){h(D,e,F),L(t,e,null),B(e,n),B(e,s),L(i,s,null),B(s,o),v&&v.m(s,null),B(e,l),A[a].m(e,null),B(e,c),L(d,e,null),B(e,p),$&&$.m(e,null),k=!0},p(D,F){const E={};2048&F[0]&&(E.content=D[11]),4096&F[0]|16384&F[1]&&(E.$$scope={dirty:F,ctx:D}),i.$set(E),4096&F[0]&&(w=Qe(D[12])),w?v?v.p(D,F):(v=pn(D),v.c(),v.m(s,null)):v&&(v.d(1),v=null);let T=a;a=_(D),a===T?A[a].p(D,F):(W(),g(A[T],1,1,()=>{A[T]=null}),Q(),u=A[a],u?u.p(D,F):(u=A[a]=b[a](D),u.c()),f(u,1),u.m(e,c));const I={};8192&F[0]&&(I.content=D[13]),16416&F[0]|16384&F[1]&&(I.$$scope={dirty:F,ctx:D}),d.$set(I),D[5]?$?($.p(D,F),32&F[0]&&f($,1)):($=$n(D),$.c(),f($,1),$.m(e,null)):$&&(W(),g($,1,1,()=>{$=null}),Q())},i(D){k||(f(t.$$.fragment,D),f(i.$$.fragment,D),f(u),f(d.$$.fragment,D),f($),k=!0)},o(D){g(t.$$.fragment,D),g(i.$$.fragment,D),g(u),g(d.$$.fragment,D),g($),k=!1},d(D){D&&m(e),M(t),M(i),v&&v.d(),A[a].d(),M(d),$&&$.d()}}}function Sr(r){let e,t,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Or],default:[Cr]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Qs({props:l}),Oe.push(()=>Ye(t,"collapsed",o)),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",s=Nt(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,u){h(a,e,u),L(t,e,null),i=!0},p(a,u){const c={};16777211&u[0]|16384&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Xe(()=>n=!1)),t.$set(c),(!i||8&u[0]&&s!==(s=Nt(a[3])))&&x(e,"id",s),(!i||16777224&u[0])&&Ae(e,"focused",a[24]===a[3])},i(a){i||(f(t.$$.fragment,a),i=!0)},o(a){g(t.$$.fragment,a),i=!1},d(a){a&&m(e),M(t)}}}function Pr(r,e,t){let n,s,i,o,l,a,u,c,d,p,k,w,v,b,A,_,$,D,F,E,T,I,q;Te(r,Is,P=>t(40,I=P));let{path:U}=e,{change:H}=e,{descriptions:K=[]}=e,{areDescriptionsVisible:ce=!0}=e,{isExpandedDefault:ue}=e,{isCollapsed:ae=!ue}=e,{isApplying:J}=e,{hasApplied:fe}=e,{onApplyChanges:he}=e,{onCodeChange:ne}=e,{onOpenFile:Fe}=e,{isAgentFromDifferentRepo:ye=!1}=e;const Ce=ri();Te(r,Ce,P=>t(24,q=P));const Re=Je(st.key);let pe=H.modifiedCode,be=F;function ke(){t(11,be=`Open ${F??"file"}`)}return tt(()=>{ke()}),r.$$set=P=>{"path"in P&&t(3,U=P.path),"change"in P&&t(0,H=P.change),"descriptions"in P&&t(4,K=P.descriptions),"areDescriptionsVisible"in P&&t(1,ce=P.areDescriptionsVisible),"isExpandedDefault"in P&&t(29,ue=P.isExpandedDefault),"isCollapsed"in P&&t(2,ae=P.isCollapsed),"isApplying"in P&&t(30,J=P.isApplying),"hasApplied"in P&&t(5,fe=P.hasApplied),"onApplyChanges"in P&&t(31,he=P.onApplyChanges),"onCodeChange"in P&&t(32,ne=P.onCodeChange),"onOpenFile"in P&&t(33,Fe=P.onOpenFile),"isAgentFromDifferentRepo"in P&&t(34,ye=P.isAgentFromDifferentRepo)},r.$$.update=()=>{var P;1&r.$$.dirty[0]&&t(6,pe=H.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Rt(H.diff)),256&r.$$.dirty[1]&&t(23,s=n.additions),256&r.$$.dirty[1]&&t(22,i=n.deletions),1&r.$$.dirty[0]&&t(21,o=ei(H)),1&r.$$.dirty[0]&&t(20,l=ti(H)),8&r.$$.dirty[0]&&t(38,a=an(U)),8&r.$$.dirty[0]&&t(19,u=at(U)),8&r.$$.dirty[0]&&t(37,c=function(O){if(an(O))return!1;const C=Vt(O);return ir.includes(C)}(U)),1&r.$$.dirty[0]&&t(10,d=((P=H.originalCode)==null?void 0:P.length)||0),64&r.$$.dirty[0]&&t(9,p=(pe==null?void 0:pe.length)||0),1024&r.$$.dirty[0]&&t(36,k=un(d)),512&r.$$.dirty[0]&&t(35,w=un(p)),65&r.$$.dirty[0]&&t(8,v=!pe&&!!H.originalCode),65&r.$$.dirty[0]&&t(7,b=!!pe&&!H.originalCode),128&r.$$.dirty[1]&&t(18,A=a),192&r.$$.dirty[1]&&t(17,_=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,$=!a&&!c&&(w||v&&k||b&&w)),512&r.$$.dirty[1]&&t(15,D=Ys(I==null?void 0:I.category,I==null?void 0:I.intensity)),8&r.$$.dirty[0]&&t(12,F=Xs(U)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,E=J||ye),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,T=J?"Applying changes...":fe?"Reapply changes to local file":ye?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[H,ce,ae,U,K,fe,pe,b,v,p,d,be,F,T,E,D,$,_,A,u,l,o,i,s,q,Ce,function(P){t(6,pe=P.detail.modifiedCode),ne==null||ne(pe)},function(){Re.reportApplyChangesEvent(),t(0,H.modifiedCode=pe,H),ne==null||ne(pe),he==null||he()},async function(){Fe&&(t(11,be="Opening file..."),await Fe()?ke():(t(11,be="Failed to open file. Does the file exist?"),setTimeout(()=>{ke()},2e3)))},ue,J,he,ne,Fe,ye,w,k,c,a,n,I,function(P){ce=P,t(1,ce)},function(P){ae=P,t(2,ae)}]}let Ir=class extends se{constructor(r){super(),ie(this,r,Pr,Sr,ee,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}};function hn(r,e,t){const n=r.slice();return n[6]=e[t],n}function qr(r){let e,t;return e=new Si({props:{filename:r[0].name}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function jr(r){let e,t;return e=new pt({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Vr(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=y("span"),t=N(n),x(e,"class","full-path-text svelte-qnxoj")},m(s,i){h(s,e,i),B(e,t)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&le(t,n)},d(s){s&&m(e)}}}function mn(r){let e,t,n=de(Array.from(r[0].children.values()).sort(Fn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=Dn(hn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){e=y("div");for(let o=0;o<s.length;o+=1)s[o].c();x(e,"class","tree-node__children svelte-qnxoj"),x(e,"role","group")},m(o,l){h(o,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(o,l){if(3&l){let a;for(n=de(Array.from(o[0].children.values()).sort(Fn)),a=0;a<n.length;a+=1){const u=hn(o,n,a);s[a]?(s[a].p(u,l),f(s[a],1)):(s[a]=Dn(u),s[a].c(),f(s[a],1),s[a].m(e,null))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&m(e),Me(s,o)}}}function Dn(r){let e,t;return e=new oi({props:{node:r[6],indentLevel:r[1]+1}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ur(r){let e,t,n,s,i,o,l,a,u,c,d,p,k,w,v,b,A;const _=[jr,qr],$=[];function D(E,T){return E[0].isFile?1:0}o=D(r),l=$[o]=_[o](r),c=new $e({props:{size:1,$$slots:{default:[Vr]},$$scope:{ctx:r}}});let F=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&mn(r);return{c(){e=y("div"),t=y("div"),n=y("div"),s=j(),i=y("div"),l.c(),a=j(),u=y("span"),z(c.$$.fragment),w=j(),F&&F.c(),x(n,"class","tree-node__indent svelte-qnxoj"),Ee(n,"width",6*r[1]+"px"),x(i,"class","tree-node__icon-container svelte-qnxoj"),x(u,"class","tree-node__label svelte-qnxoj"),x(u,"title",d=r[0].displayName||r[0].name),Ae(u,"full-path",r[0].displayName),x(t,"class","tree-node__content svelte-qnxoj"),x(t,"role","treeitem"),x(t,"tabindex","0"),x(t,"aria-selected",p=r[0].path===r[2]),x(t,"aria-expanded",k=r[0].isFile?void 0:r[0].isExpanded),Ae(t,"selected",r[0].path===r[2]),Ae(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),x(e,"class","tree-node svelte-qnxoj")},m(E,T){h(E,e,T),B(e,t),B(t,n),B(t,s),B(t,i),$[o].m(i,null),B(t,a),B(t,u),L(c,u,null),B(e,w),F&&F.m(e,null),v=!0,b||(A=[lt(t,"click",r[4]),lt(t,"keydown",r[5])],b=!0)},p(E,[T]){(!v||2&T)&&Ee(n,"width",6*E[1]+"px");let I=o;o=D(E),o===I?$[o].p(E,T):(W(),g($[I],1,1,()=>{$[I]=null}),Q(),l=$[o],l?l.p(E,T):(l=$[o]=_[o](E),l.c()),f(l,1),l.m(i,null));const q={};513&T&&(q.$$scope={dirty:T,ctx:E}),c.$set(q),(!v||1&T&&d!==(d=E[0].displayName||E[0].name))&&x(u,"title",d),(!v||1&T)&&Ae(u,"full-path",E[0].displayName),(!v||5&T&&p!==(p=E[0].path===E[2]))&&x(t,"aria-selected",p),(!v||1&T&&k!==(k=E[0].isFile?void 0:E[0].isExpanded))&&x(t,"aria-expanded",k),(!v||5&T)&&Ae(t,"selected",E[0].path===E[2]),(!v||1&T)&&Ae(t,"collapsed-folder",E[0].displayName&&!E[0].isFile),!E[0].isFile&&E[0].isExpanded&&E[0].children.size>0?F?(F.p(E,T),1&T&&f(F,1)):(F=mn(E),F.c(),f(F,1),F.m(e,null)):F&&(W(),g(F,1,1,()=>{F=null}),Q())},i(E){v||(f(l),f(c.$$.fragment,E),f(F),v=!0)},o(E){g(l),g(c.$$.fragment,E),g(F),v=!1},d(E){E&&m(e),$[o].d(),M(c),F&&F.d(),b=!1,qs(A)}}}const Fn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Zr(r,e,t){let n,{node:s}=e,{indentLevel:i=0}=e;const o=ri();function l(){s.isFile?o.set(s.path):t(0,s.isExpanded=!s.isExpanded,s)}return Te(r,o,a=>t(2,n=a)),r.$$set=a=>{"node"in a&&t(0,s=a.node),"indentLevel"in a&&t(1,i=a.indentLevel)},[s,i,n,o,l,a=>a.key==="Enter"&&l()]}class oi extends se{constructor(e){super(),ie(this,e,Zr,Ur,ee,{node:0,indentLevel:1})}}function xn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Hr(r){let e,t,n=de(Array.from(r[1].children.values()).sort(Cn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=kn(xn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=xe()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,e,l),t=!0},p(o,l){if(2&l){let a;for(n=de(Array.from(o[1].children.values()).sort(Cn)),a=0;a<n.length;a+=1){const u=xn(o,n,a);s[a]?(s[a].p(u,l),f(s[a],1)):(s[a]=kn(u),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&m(e),Me(s,o)}}}function Wr(r){let e,t,n;return t=new $e({props:{size:1,color:"neutral",$$slots:{default:[Gr]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Qr(r){let e;return{c(){e=y("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',x(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){h(t,e,n)},p:te,i:te,o:te,d(t){t&&m(e)}}}function kn(r){let e,t;return e=new oi({props:{node:r[4],indentLevel:0}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2&s&&(i.node=n[4]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Gr(r){let e;return{c(){e=N("No changed files")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Jr(r){let e,t,n,s,i;const o=[Qr,Wr,Hr],l=[];function a(u,c){return u[0]?0:u[1].children.size===0?1:2}return n=a(r),s=l[n]=o[n](r),{c(){e=y("div"),t=y("div"),s.c(),x(t,"class","tree-view__content svelte-1tnd9l7"),x(t,"role","tree"),x(t,"aria-label","Changed Files"),x(e,"class","tree-view svelte-1tnd9l7")},m(u,c){h(u,e,c),B(e,t),l[n].m(t,null),i=!0},p(u,[c]){let d=n;n=a(u),n===d?l[n].p(u,c):(W(),g(l[d],1,1,()=>{l[d]=null}),Q(),s=l[n],s?s.p(u,c):(s=l[n]=o[n](u),s.c()),f(s,1),s.m(t,null))},i(u){i||(f(s),i=!0)},o(u){g(s),i=!1},d(u){u&&m(e),l[n].d()}}}function Tt(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),a=o;for(;;){const u=Array.from(a.children.values()).filter(d=>!d.isFile),c=Array.from(a.children.values()).filter(d=>d.isFile);if(u.length!==1||c.length!==0)break;a=u[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Tt(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${a}`;r.children.set(c,u)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${l}/${a}`;r.children.set(c,u)}r.children.delete(l)}}}const Cn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Yr(r,e,t){let n,{changedFiles:s=[]}=e,{isLoading:i=!1}=e;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(u=>{const c=u.change_type===Oi.deleted?u.old_path:u.new_path;c&&function(d,p){const k=p.split("/");let w=d;for(let v=0;v<k.length;v++){const b=k[v],A=v===k.length-1,_=k.slice(0,v+1).join("/");w.children.has(b)||w.children.set(b,{name:b,path:_,isFile:A,children:new Map,isExpanded:!0}),w=w.children.get(b)}}(a,c)}),function(u){if(!u.isFile)if(u.path!=="")Tt(u);else{const c=Array.from(u.children.values()).filter(d=>!d.isFile);for(const d of c)Tt(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&t(2,s=l.changedFiles),"isLoading"in l&&t(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(s))},[i,n,s]}class li extends se{constructor(e){super(),ie(this,e,Yr,Jr,ee,{changedFiles:2,isLoading:0})}}function vn(r,e,t){const n=r.slice();return n[19]=e[t],n}function Xr(r){let e;return{c(){e=N("Changed files")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Kr(r){let e,t,n;return t=new $e({props:{size:1,color:"neutral",$$slots:{default:[to]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function eo(r){let e,t,n,s,i,o,l=[],a=new Map,u=r[9].length>0&&wn(r),c=de(r[9]);const d=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<c.length;p+=1){let k=vn(r,c,p),w=d(k);a.set(w,l[p]=yn(w,k))}return{c(){e=y("div"),t=y("div"),u&&u.c(),n=j(),s=y("div"),i=y("div");for(let p=0;p<l.length;p+=1)l[p].c();x(t,"class","c-edits-list-controls svelte-6iqvaj"),x(e,"class","c-edits-list-header svelte-6iqvaj"),x(i,"class","c-edits-section svelte-6iqvaj"),x(s,"class","c-edits-list svelte-6iqvaj")},m(p,k){h(p,e,k),B(e,t),u&&u.m(t,null),h(p,n,k),h(p,s,k),B(s,i);for(let w=0;w<l.length;w+=1)l[w]&&l[w].m(i,null);o=!0},p(p,k){p[9].length>0?u?(u.p(p,k),512&k&&f(u,1)):(u=wn(p),u.c(),f(u,1),u.m(t,null)):u&&(W(),g(u,1,1,()=>{u=null}),Q()),2654&k&&(c=de(p[9]),W(),l=Zs(l,k,d,1,p,c,a,i,Hs,yn,null,vn),Q())},i(p){if(!o){f(u);for(let k=0;k<c.length;k+=1)f(l[k]);o=!0}},o(p){g(u);for(let k=0;k<l.length;k+=1)g(l[k]);o=!1},d(p){p&&(m(e),m(n),m(s)),u&&u.d();for(let k=0;k<l.length;k+=1)l[k].d()}}}function to(r){let e;return{c(){e=N("No changes to show")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function wn(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[7]||r[8]||r[3].length>0||!r[10],$$slots:{default:[ro]},$$scope:{ctx:r}}}),e.$on("click",r[12]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1416&s&&(i.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function no(r){let e;return{c(){e=N("Apply all")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function so(r){let e;return{c(){e=N("All applied")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function io(r){let e;return{c(){e=N("Applying...")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function ro(r){let e,t,n,s;function i(a,u){return a[7]?io:a[8]?so:no}let o=i(r),l=o(r);return n=new ct({}),{c(){l.c(),e=j(),t=y("div"),z(n.$$.fragment),x(t,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,u){l.m(a,u),h(a,e,u),h(a,t,u),L(n,t,null),s=!0},p(a,u){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(e.parentNode,e)))},i(a){s||(f(n.$$.fragment,a),s=!0)},o(a){g(n.$$.fragment,a),s=!1},d(a){a&&(m(e),m(t)),l.d(a),M(n)}}}function yn(r,e){let t,n,s,i,o;function l(...c){return e[16](e[19],...c)}function a(){return e[17](e[19])}function u(){return e[18](e[19])}return n=new Ir({props:{path:e[19].qualifiedPathName.relPath,change:e[19].diff,isApplying:e[3].includes(e[19].qualifiedPathName.relPath),hasApplied:e[4].includes(e[19].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,onOpenFile:e[2]?u:void 0,isExpandedDefault:!0}}),{key:r,first:null,c(){t=y("div"),z(n.$$.fragment),s=j(),x(t,"class",""),this.first=t},m(c,d){h(c,t,d),L(n,t,null),B(t,s),o=!0},p(c,d){e=c;const p={};512&d&&(p.path=e[19].qualifiedPathName.relPath),512&d&&(p.change=e[19].diff),520&d&&(p.isApplying=e[3].includes(e[19].qualifiedPathName.relPath)),528&d&&(p.hasApplied=e[4].includes(e[19].qualifiedPathName.relPath)),512&d&&(p.onCodeChange=l),578&d&&(p.onApplyChanges=a),516&d&&(p.onOpenFile=e[2]?u:void 0),n.$set(p)},i(c){o||(f(n.$$.fragment,c),c&&ki(()=>{o&&(i||(i=en(t,nn,{},!0)),i.run(1))}),o=!0)},o(c){g(n.$$.fragment,c),c&&(i||(i=en(t,nn,{},!1)),i.run(0)),o=!1},d(c){c&&m(t),M(n),c&&i&&i.end()}}}function oo(r){let e,t,n,s,i,o,l,a,u,c,d,p;i=new $e({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Xr]},$$scope:{ctx:r}}}),l=new li({props:{changedFiles:r[0],isLoading:r[5]}});const k=[eo,Kr],w=[];function v(b,A){return b[9].length>0?0:1}return c=v(r),d=w[c]=k[c](r),{c(){e=y("div"),t=y("div"),n=y("div"),s=y("div"),z(i.$$.fragment),o=j(),z(l.$$.fragment),a=j(),u=y("div"),d.c(),x(s,"class","c-file-explorer__tree__header svelte-6iqvaj"),x(n,"class","c-file-explorer__tree svelte-6iqvaj"),x(u,"class","c-file-explorer__details svelte-6iqvaj"),x(t,"class","c-file-explorer__layout svelte-6iqvaj"),x(e,"class","c-edits-list-container svelte-6iqvaj")},m(b,A){h(b,e,A),B(e,t),B(t,n),B(n,s),L(i,s,null),B(s,o),L(l,s,null),B(t,a),B(t,u),w[c].m(u,null),p=!0},p(b,[A]){const _={};4194304&A&&(_.$$scope={dirty:A,ctx:b}),i.$set(_);const $={};1&A&&($.changedFiles=b[0]),32&A&&($.isLoading=b[5]),l.$set($);let D=c;c=v(b),c===D?w[c].p(b,A):(W(),g(w[D],1,1,()=>{w[D]=null}),Q(),d=w[c],d?d.p(b,A):(d=w[c]=k[c](b),d.c()),f(d,1),d.m(u,null))},i(b){p||(f(i.$$.fragment,b),f(l.$$.fragment,b),f(d),p=!0)},o(b){g(i.$$.fragment,b),g(l.$$.fragment,b),g(d),p=!1},d(b){b&&m(e),M(i),M(l),w[c].d()}}}function lo(r,e,t){let n,s,i,o,l,{changedFiles:a}=e,{onApplyChanges:u}=e,{onOpenFile:c}=e,{pendingFiles:d=[]}=e,{appliedFiles:p=[]}=e,{isLoadingTreeView:k=!1}=e,w={},v=!1,b=!1;function A(_,$){t(6,w[_]=$,w)}return r.$$set=_=>{"changedFiles"in _&&t(0,a=_.changedFiles),"onApplyChanges"in _&&t(1,u=_.onApplyChanges),"onOpenFile"in _&&t(2,c=_.onOpenFile),"pendingFiles"in _&&t(3,d=_.pendingFiles),"appliedFiles"in _&&t(4,p=_.appliedFiles),"isLoadingTreeView"in _&&t(5,k=_.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&t(15,n=JSON.stringify(a)),16&r.$$.dirty&&t(13,s=JSON.stringify(p)),8&r.$$.dirty&&t(14,i=JSON.stringify(d)),32768&r.$$.dirty&&n&&(t(6,w={}),t(7,v=!1),t(8,b=!1)),65&r.$$.dirty&&t(9,l=a.map(_=>{const $=_.new_path||_.old_path,D=_.old_contents||"",F=_.new_contents||"",E=Vi.generateDiff(_.old_path,_.new_path,D,F),T=function(I,q){const U=Ct("oldFile","newFile",I,q,"","",{context:3}),H=Ei(U);let K=0,ce=0,ue=[];for(const ae of H)for(const J of ae.hunks)for(const fe of J.lines){const he=fe.startsWith("+"),ne=fe.startsWith("-");he&&K++,ne&&ce++,ue.push({value:fe,added:he,removed:ne})}return{totalAddedLines:K,totalRemovedLines:ce,changes:ue,diff:U}}(D,F);return w[$]||t(6,w[$]=F,w),{qualifiedPathName:{rootPath:"",relPath:$},lineChanges:T,oldContents:D,newContents:F,diff:E}})),57880&r.$$.dirty&&t(10,o=(()=>{if(n&&s&&i){const _=l.map($=>$.qualifiedPathName.relPath);return _.length!==0&&_.some($=>!p.includes($)&&!d.includes($))}return!1})()),664&r.$$.dirty&&v){const _=l.map($=>$.qualifiedPathName.relPath);_.filter($=>!p.includes($)&&!d.includes($)).length===0&&_.every($=>p.includes($)||d.includes($))&&d.length===0&&p.length>0&&(t(7,v=!1),t(8,b=!0))}if(9104&r.$$.dirty&&l.length>0&&!v&&s){const _=l.map($=>$.qualifiedPathName.relPath);if(_.length>0){const $=_.every(D=>p.includes(D));$&&p.length>0?t(8,b=!0):!$&&b&&t(8,b=!1)}}},[a,u,c,d,p,k,w,v,b,l,o,A,function(){if(!u)return;const _=l.map(D=>D.qualifiedPathName.relPath);if(_.every(D=>p.includes(D)))return void t(8,b=!0);const $=_.filter(D=>!p.includes(D)&&!d.includes(D));$.length!==0&&(t(7,v=!0),t(8,b=!1),$.forEach(D=>{const F=l.find(E=>E.qualifiedPathName.relPath===D);if(F){const E=w[D]||F.newContents;u(D,F.oldContents,E)}}))},s,i,n,(_,$)=>{A(_.qualifiedPathName.relPath,$)},_=>{const $=w[_.qualifiedPathName.relPath]||_.newContents;u(_.qualifiedPathName.relPath,_.oldContents,$)},_=>c(_.qualifiedPathName.relPath)]}class ao extends se{constructor(e){super(),ie(this,e,lo,oo,ee,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function An(r,e,t){const n=r.slice();return n[3]=e[t],n}function bn(r){let e,t=de(r[1].paths),n=[];for(let s=0;s<t.length;s+=1)n[s]=En(An(r,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=xe()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);h(s,e,i)},p(s,i){if(2&i){let o;for(t=de(s[1].paths),o=0;o<t.length;o+=1){const l=An(s,t,o);n[o]?n[o].p(l,i):(n[o]=En(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(s){s&&m(e),Me(n,s)}}}function En(r){let e,t;return{c(){e=js("path"),x(e,"d",t=r[3]),x(e,"fill-rule","evenodd"),x(e,"clip-rule","evenodd")},m(n,s){h(n,e,s)},p(n,s){2&s&&t!==(t=n[3])&&x(e,"d",t)},d(n){n&&m(e)}}}function uo(r){let e,t=r[1]&&bn(r);return{c(){e=js("svg"),t&&t.c(),x(e,"width","14"),x(e,"viewBox","0 0 20 20"),x(e,"fill","currentColor"),x(e,"class","svelte-10h4f31")},m(n,s){h(n,e,s),t&&t.m(e,null)},p(n,s){n[1]?t?t.p(n,s):(t=bn(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&m(e),t&&t.d()}}}function co(r){let e,t;return e=new qe({props:{content:`This is a ${r[0]} change`,triggerOn:[Ke.Hover],$$slots:{default:[uo]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function po(r,e,t){let n,{type:s}=e;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=i[s]??i.other)},[s,n]}class fo extends se{constructor(e){super(),ie(this,e,po,co,ee,{type:0})}}function _n(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function Bn(r){let e,t,n,s,i;t=new dt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[ho]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=de(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=zn(_n(r,o,u));const a=u=>g(l[u],1,1,()=>{l[u]=null});return{c(){e=y("div"),z(t.$$.fragment),n=j(),s=y("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(s,"class","descriptions svelte-14s1ghg"),Ee(s,"transform","translateY("+-r[4]+"px)")},m(u,c){h(u,e,c),L(t,e,null),h(u,n,c),h(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=de(u[1]),p=0;p<o.length;p+=1){const k=_n(u,o,p);l[p]?(l[p].p(k,c),f(l[p],1)):(l[p]=zn(k),l[p].c(),f(l[p],1),l[p].m(s,null))}for(W(),p=o.length;p<l.length;p+=1)a(p);Q()}(!i||16&c[0])&&Ee(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);i=!0}},o(u){g(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)g(l[c]);i=!1},d(u){u&&(m(e),m(n),m(s)),M(t),Me(l,u)}}}function go(r){let e,t;return e=new pt({props:{icon:"book"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function $o(r){let e,t;return e=new pt({props:{icon:"x"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function ho(r){let e,t,n,s;const i=[$o,go],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function zn(r){let e,t,n,s;return t=new ni({props:{markdown:r[47].text}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),x(e,"class","description svelte-14s1ghg"),Ee(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ee(e,"--user-theme-sidebar-background","transparent")},m(i,o){h(i,e,o),L(t,e,null),B(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ee(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&m(e),M(t)}}}function mo(r){let e,t,n,s,i=r[1].length>0&&Bn(r);return{c(){e=y("div"),t=y("div"),n=j(),i&&i.c(),x(t,"class","editor-container svelte-14s1ghg"),Ee(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){h(o,e,l),B(e,t),r[23](t),B(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ee(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=Bn(o),i.c(),f(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){g(i),s=!1},d(o){o&&m(e),r[23](null),i&&i.d()}}}function Do(r,e,t){let n,s,i;const o=Ss();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:k=[]}=e,{theme:w}=e,{areDescriptionsVisible:v=!0}=e,{isNewFile:b=!1}=e,{isDeletedFile:A=!1}=e;const _=qt.getContext().monaco;let $,D,F,E;Te(r,_,C=>t(22,n=C));let T,I=[];const q=jt();let U,H=We(0);Te(r,H,C=>t(4,s=C));let K=b?20*a.split(`
`).length+40:100;const ce=n?n.languages.getLanguages().map(C=>C.id):[];function ue(C,R){var S,G;if(R){const V=(S=R.split(".").pop())==null?void 0:S.toLowerCase();if(V){const X=(G=n==null?void 0:n.languages.getLanguages().find(Z=>{var Y;return(Y=Z.extensions)==null?void 0:Y.includes("."+V)}))==null?void 0:G.id;if(X&&ce.includes(X))return X}}return"plaintext"}const ae=We({});Te(r,ae,C=>t(5,i=C));let J=null;function fe(){if(!$)return;I=I.filter(S=>(S.dispose(),!1));const C=$.getOriginalEditor(),R=$.getModifiedEditor();I.push(C.onDidScrollChange(()=>{xt(H,s=C.getScrollTop(),s)}),R.onDidScrollChange(()=>{xt(H,s=R.getScrollTop(),s)}))}function he(){if(!$||!T)return;const C=$.getOriginalEditor(),R=$.getModifiedEditor();I.push(R.onDidContentSizeChange(()=>q.requestLayout()),C.onDidContentSizeChange(()=>q.requestLayout()),$.onDidUpdateDiff(()=>q.requestLayout()),R.onDidChangeHiddenAreas(()=>q.requestLayout()),C.onDidChangeHiddenAreas(()=>q.requestLayout()),R.onDidLayoutChange(()=>q.requestLayout()),C.onDidLayoutChange(()=>q.requestLayout()),R.onDidFocusEditorWidget(()=>{ke(!0)}),C.onDidFocusEditorWidget(()=>{ke(!0)}),R.onDidBlurEditorWidget(()=>{ke(!1)}),C.onDidBlurEditorWidget(()=>{ke(!1)}),R.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const S=(E==null?void 0:E.getValue())||"";if(S===a)return;const G=S.replace(p.join(""),"").replace(k.join(""),"");o("codeChange",{modifiedCode:G});const V=setTimeout(()=>{Ce=!1},500);I.push({dispose:()=>clearTimeout(V)})})),function(){!T||!$||(J&&clearTimeout(J),J=setTimeout(()=>{if(!T.__hasClickListener){const S=G=>{const V=G.target;V&&(V.closest('[title="Show Unchanged Region"]')||V.closest('[title="Hide Unchanged Region"]'))&&Fe()};T.addEventListener("click",S),t(2,T.__hasClickListener=!0,T),I.push({dispose:()=>{T.removeEventListener("click",S)}})}$&&I.push($.onDidUpdateDiff(()=>{Fe()}))},300))}()}Ps(()=>{$==null||$.dispose(),D==null||D.dispose(),F==null||F.dispose(),E==null||E.dispose(),I.forEach(C=>C.dispose()),J&&clearTimeout(J),U==null||U()});let ne=null;function Fe(){ne&&clearTimeout(ne),ne=setTimeout(()=>{q.requestLayout(),ne=null},100),ne&&I.push({dispose:()=>{ne&&(clearTimeout(ne),ne=null)}})}function ye(C,R,S,G=[],V=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");F==null||F.dispose(),E==null||E.dispose(),R=R||"",S=S||"";const X=G.join(""),Z=V.join("");if(R=b?S.split(`
`).map(()=>" ").join(`
`):X+R+Z,S=X+S+Z,F=n.editor.createModel(R,void 0,C!==void 0?n.Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0),A&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,E=n.editor.createModel(S,void 0,C!==void 0?n.Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0)),$){$.setModel({original:F,modified:E});const Y=$.getOriginalEditor();Y&&Y.updateOptions({lineNumbers:"off"}),fe(),J&&clearTimeout(J),J=setTimeout(()=>{he(),J=null},300)}}tt(()=>{if(n)if(b){t(20,D=n.editor.create(T,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:w,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:G=>`${d-p.length+G}`}));const C=ue(0,u);t(21,E=n.editor.createModel(a,C,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(E),I.push(D.onDidChangeModelContent(()=>{Ce=!0,Re=Date.now();const G=(E==null?void 0:E.getValue())||"";if(G===a)return;o("codeChange",{modifiedCode:G});const V=setTimeout(()=>{Ce=!1},500);I.push({dispose:()=>clearTimeout(V)})})),I.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const R=D.getContentHeight();t(3,K=Math.max(R,60));const S=setTimeout(()=>{D==null||D.layout()},0);I.push({dispose:()=>clearTimeout(S)})}else t(19,$=n.editor.createDiffEditor(T,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:w,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:C=>`${d-p.length+C}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),U&&U(),U=q.registerEditor({editor:$,updateHeight:be,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ye(u,l,a,p,k),fe(),he(),J&&clearTimeout(J),J=setTimeout(()=>{q.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,Re=0;function pe(C,R=!0){return $?(R?$.getModifiedEditor():$.getOriginalEditor()).getTopForLineNumber(C):18*C}function be(){if(!$)return;const C=$.getModel(),R=C==null?void 0:C.original,S=C==null?void 0:C.modified;if(!R||!S)return;const G=$.getOriginalEditor(),V=$.getModifiedEditor(),X=$.getLineChanges()||[];let Z;if(X.length===0){const Y=G.getContentHeight(),re=V.getContentHeight();Z=Math.max(100,Y,re)}else{let Y=0,re=0;for(const we of X)we.originalEndLineNumber>0&&(Y=Math.max(Y,we.originalEndLineNumber)),we.modifiedEndLineNumber>0&&(re=Math.max(re,we.modifiedEndLineNumber));Y=Math.min(Y+3,R.getLineCount()),re=Math.min(re+3,S.getLineCount());const ve=G.getTopForLineNumber(Y),Ne=V.getTopForLineNumber(re);Z=Math.max(ve,Ne)+60}t(3,K=Math.min(Z,2e4)),$.layout(),O()}function ke(C){if(!$)return;const R=$.getOriginalEditor(),S=$.getModifiedEditor();R.updateOptions({scrollbar:{handleMouseWheel:C}}),S.updateOptions({scrollbar:{handleMouseWheel:C}})}function P(C){if(!$)return 0;const R=$.getModel(),S=R==null?void 0:R.original,G=R==null?void 0:R.modified;if(!S||!G)return 0;const V=pe(C.range.start+1,!1),X=pe(C.range.start+1,!0);return V&&!X?V:!V&&X?X:Math.min(V,X)}function O(){if(!$||c.length===0)return;const C={};c.forEach((R,S)=>{C[S]=P(R)}),ae.set(C)}return r.$$set=C=>{"originalCode"in C&&t(10,l=C.originalCode),"modifiedCode"in C&&t(11,a=C.modifiedCode),"path"in C&&t(12,u=C.path),"descriptions"in C&&t(1,c=C.descriptions),"lineOffset"in C&&t(13,d=C.lineOffset),"extraPrefixLines"in C&&t(14,p=C.extraPrefixLines),"extraSuffixLines"in C&&t(15,k=C.extraSuffixLines),"theme"in C&&t(16,w=C.theme),"areDescriptionsVisible"in C&&t(0,v=C.areDescriptionsVisible),"isNewFile"in C&&t(17,b=C.isNewFile),"isDeletedFile"in C&&t(18,A=C.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(C=a,!(Ce||Date.now()-Re<1e3||E&&E.getValue()===p.join("")+C+k.join(""))))if(b&&D){if(E)E.setValue(a);else{const R=ue(0,u);n&&t(21,E=n.editor.createModel(a,R,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),E&&D.setModel(E)}t(3,K=20*a.split(`
`).length+40),D.layout()}else!b&&$&&(ye(u,l,a,p,k),q.requestLayout());var C;if(524290&r.$$.dirty[0]&&$&&c.length>0&&O(),1181696&r.$$.dirty[0]&&b&&a&&D){const R=D.getContentHeight();t(3,K=Math.max(R,60)),D.layout()}},[v,c,T,K,s,i,_,H,ae,P,l,a,u,d,p,k,w,b,A,$,D,E,n,function(C){Oe[C?"unshift":"push"](()=>{T=C,t(2,T)})},()=>t(0,v=!v)]}class Fo extends se{constructor(e){super(),ie(this,e,Do,mo,ee,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}}const xo=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],ko=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],ai=1048576;function Ut(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function ut(r){switch(Ut(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function Ln(r){const e=Ut(r);return xo.includes(e)}function Mn(r){return r>ai}const Co=Symbol("focusedPath");function Rn(r){return`file-diff-${je(r)}`}function vo(r){let e,t,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new Fo({props:i}),Oe.push(()=>Ye(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[26]),{c(){z(e.$$.fragment)},m(o,l){L(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Xe(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){g(e.$$.fragment,o),n=!1},d(o){M(e,o)}}}function wo(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[bo]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function yo(r){let e,t,n;return t=new $e({props:{$$slots:{default:[zo]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Ao(r){let e,t,n,s;const i=[Mo,Lo],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=y("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){h(a,e,u),~t&&o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(e),~t&&o[t].d()}}}function bo(r){let e,t,n,s,i,o,l,a=oe(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=N('File "'),t=N(a),n=N('" is too large to display a diff (size: '),s=N(u),i=N(" bytes, max: "),o=N(ai),l=N(" bytes).")},m(c,d){h(c,e,d),h(c,t,d),h(c,n,d),h(c,s,d),h(c,i,d),h(c,o,d),h(c,l,d)},p(c,d){4096&d[0]&&a!==(a=oe(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(s,u)},d(c){c&&(m(e),m(t),m(n),m(s),m(i),m(o),m(l))}}}function Eo(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file modified: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function _o(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file deleted: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function Bo(r){let e,t,n,s=oe(r[12])+"";return{c(){e=N("Binary file added: "),t=N(s),n=N(".")},m(i,o){h(i,e,o),h(i,t,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=oe(i[12])+"")&&le(t,s)},d(i){i&&(m(e),m(t),m(n))}}}function zo(r){let e;function t(i,o){return i[21]||i[7]?Bo:i[8]?_o:Eo}let n=t(r),s=n(r);return{c(){s.c(),e=N(`
            No text preview available.`)},m(i,o){s.m(i,o),h(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&m(e),s.d(i)}}}function Lo(r){let e,t,n,s,i,o,l,a;e=new $e({props:{class:"image-info-text",$$slots:{default:[To]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&Nn(r);return{c(){z(e.$$.fragment),t=j(),n=y("img"),o=j(),u&&u.c(),l=xe(),Se(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",s),x(n,"alt",i="Current "+oe(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){L(e,c,d),h(c,t,d),h(c,n,d),h(c,o,d),u&&u.m(c,d),h(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Se(n.src,s="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+oe(c[12])))&&x(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=Nn(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(W(),g(u,1,1,()=>{u=null}),Q())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){g(e.$$.fragment,c),g(u),a=!1},d(c){c&&(m(t),m(n),m(o),m(l)),M(e,c),u&&u.d(c)}}}function Mo(r){let e,t,n,s;e=new $e({props:{class:"image-info-text",$$slots:{default:[So]},$$scope:{ctx:r}}});let i=r[0].originalCode&&Tn(r);return{c(){z(e.$$.fragment),t=j(),i&&i.c(),n=xe()},m(o,l){L(e,o,l),h(o,t,l),i&&i.m(o,l),h(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=Tn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){s||(f(e.$$.fragment,o),f(i),s=!0)},o(o){g(e.$$.fragment,o),g(i),s=!1},d(o){o&&(m(t),m(n)),M(e,o),i&&i.d(o)}}}function Ro(r){let e;return{c(){e=N("Image modified")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function No(r){let e;return{c(){e=N("New image added")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function To(r){let e,t,n=oe(r[12])+"";function s(l,a){return l[21]||l[7]?No:Ro}let i=s(r),o=i(r);return{c(){o.c(),e=N(": "),t=N(n)},m(l,a){o.m(l,a),h(l,e,a),h(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=oe(l[12])+"")&&le(t,n)},d(l){l&&(m(e),m(t)),o.d(l)}}}function Nn(r){let e,t,n,s,i,o;return e=new $e({props:{class:"image-info-text",$$slots:{default:[Oo]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),n=y("img"),Se(n.src,s="data:"+ut(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+oe(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(e,l,a),h(l,t,a),h(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Se(n.src,s="data:"+ut(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+oe(l[12])))&&x(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){g(e.$$.fragment,l),o=!1},d(l){l&&(m(t),m(n)),M(e,l)}}}function Oo(r){let e;return{c(){e=N("Previous version:")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function So(r){let e,t,n=oe(r[12])+"";return{c(){e=N("Image deleted: "),t=N(n)},m(s,i){h(s,e,i),h(s,t,i)},p(s,i){4096&i[0]&&n!==(n=oe(s[12])+"")&&le(t,n)},d(s){s&&(m(e),m(t))}}}function Tn(r){let e,t,n,s,i,o;return e=new $e({props:{class:"image-info-text",$$slots:{default:[Po]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),n=y("img"),Se(n.src,s="data:"+ut(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+oe(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){L(e,l,a),h(l,t,a),h(l,n,a),o=!0},p(l,a){const u={};16384&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Se(n.src,s="data:"+ut(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+oe(l[12])))&&x(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){g(e.$$.fragment,l),o=!1},d(l){l&&(m(t),m(n)),M(e,l)}}}function Po(r){let e;return{c(){e=N("Previous version:")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Io(r){let e,t,n,s;const i=[Ao,yo,wo,vo],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=y("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){h(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(e),o[t].d()}}}function qo(r){let e,t=oe(r[12])+"";return{c(){e=N(t)},m(n,s){h(n,e,s)},p(n,s){4096&s[0]&&t!==(t=oe(n[12])+"")&&le(e,t)},d(n){n&&m(e)}}}function jo(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[qo]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function On(r){let e,t,n=Qe(r[12])+"";return{c(){e=y("span"),t=N(n),x(e,"class","c-directory svelte-1536g7w")},m(s,i){h(s,e,i),B(e,t)},p(s,i){4096&i[0]&&n!==(n=Qe(s[12])+"")&&le(t,n)},d(s){s&&m(e)}}}function Vo(r){let e,t,n,s=r[23]>0&&Sn(r),i=r[22]>0&&Pn(r);return{c(){e=y("div"),s&&s.c(),t=j(),i&&i.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){h(o,e,l),s&&s.m(e,null),B(e,t),i&&i.m(e,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=Sn(o),s.c(),f(s,1),s.m(e,t)):s&&(W(),g(s,1,1,()=>{s=null}),Q()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=Pn(o),i.c(),f(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){n||(f(s),f(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&m(e),s&&s.d(),i&&i.d()}}}function Uo(r){let e;return{c(){e=y("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){h(t,e,n)},p:te,i:te,o:te,d(t){t&&m(e)}}}function Sn(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[Zo]},$$scope:{ctx:r}}}),{c(){e=y("span"),z(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Zo(r){let e,t;return{c(){e=N("+"),t=N(r[23])},m(n,s){h(n,e,s),h(n,t,s)},p(n,s){8388608&s[0]&&le(t,n[23])},d(n){n&&(m(e),m(t))}}}function Pn(r){let e,t,n;return t=new $e({props:{size:1,$$slots:{default:[Ho]},$$scope:{ctx:r}}}),{c(){e=y("span"),z(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Ho(r){let e,t;return{c(){e=N("-"),t=N(r[22])},m(n,s){h(n,e,s),h(n,t,s)},p(n,s){4194304&s[0]&&le(t,n[22])},d(n){n&&(m(e),m(t))}}}function Wo(r){let e;return{c(){e=N("Apply")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Qo(r){let e;return{c(){e=N("Applied")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Go(r){let e,t,n;return t=new ct({}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Jo(r){let e,t,n;return t=new bt({props:{iconName:"check"}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(s,i){h(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function Yo(r){let e,t,n,s,i;function o(p,k){return p[5]?Qo:Wo}let l=o(r),a=l(r);const u=[Jo,Go],c=[];function d(p,k){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=j(),n.c(),s=xe()},m(p,k){a.m(p,k),h(p,e,k),c[t].m(p,k),h(p,s,k),i=!0},p(p,k){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let w=t;t=d(p),t!==w&&(W(),g(c[w],1,1,()=>{c[w]=null}),Q(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){g(n),i=!1},d(p){p&&(m(e),m(s)),a.d(p),c[t].d(p)}}}function Xo(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Yo]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function In(r){let e,t;return e=new qe({props:{content:r[11],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[el]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ko(r){let e,t;return e=new It({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function el(r){let e,t;return e=new dt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Ko]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function tl(r){let e,t,n,s,i,o,l,a,u,c,d,p,k,w=Qe(r[12]);t=new Gs({}),i=new qe({props:{content:r[11],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[jo]},$$scope:{ctx:r}}});let v=w&&On(r);const b=[Uo,Vo],A=[];function _(D,F){return D[21]?0:1}a=_(r),u=A[a]=b[a](r),d=new qe({props:{content:r[13],triggerOn:[Ke.Hover],delayDurationMs:300,$$slots:{default:[Xo]},$$scope:{ctx:r}}});let $=r[5]&&In(r);return{c(){e=y("div"),z(t.$$.fragment),n=j(),s=y("div"),z(i.$$.fragment),o=j(),v&&v.c(),l=j(),u.c(),c=j(),z(d.$$.fragment),p=j(),$&&$.c(),x(s,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,F){h(D,e,F),L(t,e,null),B(e,n),B(e,s),L(i,s,null),B(s,o),v&&v.m(s,null),B(e,l),A[a].m(e,null),B(e,c),L(d,e,null),B(e,p),$&&$.m(e,null),k=!0},p(D,F){const E={};2048&F[0]&&(E.content=D[11]),4096&F[0]|16384&F[1]&&(E.$$scope={dirty:F,ctx:D}),i.$set(E),4096&F[0]&&(w=Qe(D[12])),w?v?v.p(D,F):(v=On(D),v.c(),v.m(s,null)):v&&(v.d(1),v=null);let T=a;a=_(D),a===T?A[a].p(D,F):(W(),g(A[T],1,1,()=>{A[T]=null}),Q(),u=A[a],u?u.p(D,F):(u=A[a]=b[a](D),u.c()),f(u,1),u.m(e,c));const I={};8192&F[0]&&(I.content=D[13]),16416&F[0]|16384&F[1]&&(I.$$scope={dirty:F,ctx:D}),d.$set(I),D[5]?$?($.p(D,F),32&F[0]&&f($,1)):($=In(D),$.c(),f($,1),$.m(e,null)):$&&(W(),g($,1,1,()=>{$=null}),Q())},i(D){k||(f(t.$$.fragment,D),f(i.$$.fragment,D),f(u),f(d.$$.fragment,D),f($),k=!0)},o(D){g(t.$$.fragment,D),g(i.$$.fragment,D),g(u),g(d.$$.fragment,D),g($),k=!1},d(D){D&&m(e),M(t),M(i),v&&v.d(),A[a].d(),M(d),$&&$.d()}}}function nl(r){let e,t,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[tl],default:[Io]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Qs({props:l}),Oe.push(()=>Ye(t,"collapsed",o)),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",s=Rn(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,u){h(a,e,u),L(t,e,null),i=!0},p(a,u){const c={};16777211&u[0]|16384&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Xe(()=>n=!1)),t.$set(c),(!i||8&u[0]&&s!==(s=Rn(a[3])))&&x(e,"id",s),(!i||16777224&u[0])&&Ae(e,"focused",a[24]===a[3])},i(a){i||(f(t.$$.fragment,a),i=!0)},o(a){g(t.$$.fragment,a),i=!1},d(a){a&&m(e),M(t)}}}function sl(r,e,t){let n,s,i,o,l,a,u,c,d,p,k,w,v,b,A,_,$,D,F,E,T,I,q;Te(r,Is,P=>t(40,I=P));let{path:U}=e,{change:H}=e,{descriptions:K=[]}=e,{areDescriptionsVisible:ce=!0}=e,{isExpandedDefault:ue}=e,{isCollapsed:ae=!ue}=e,{isApplying:J}=e,{hasApplied:fe}=e,{onApplyChanges:he}=e,{onCodeChange:ne}=e,{onOpenFile:Fe}=e,{isAgentFromDifferentRepo:ye=!1}=e;const Ce=Je(Co);Te(r,Ce,P=>t(24,q=P));const Re=Je(Ii.key);let pe=H.modifiedCode,be=F;function ke(){t(11,be=`Open ${F??"file"}`)}return tt(()=>{ke()}),r.$$set=P=>{"path"in P&&t(3,U=P.path),"change"in P&&t(0,H=P.change),"descriptions"in P&&t(4,K=P.descriptions),"areDescriptionsVisible"in P&&t(1,ce=P.areDescriptionsVisible),"isExpandedDefault"in P&&t(29,ue=P.isExpandedDefault),"isCollapsed"in P&&t(2,ae=P.isCollapsed),"isApplying"in P&&t(30,J=P.isApplying),"hasApplied"in P&&t(5,fe=P.hasApplied),"onApplyChanges"in P&&t(31,he=P.onApplyChanges),"onCodeChange"in P&&t(32,ne=P.onCodeChange),"onOpenFile"in P&&t(33,Fe=P.onOpenFile),"isAgentFromDifferentRepo"in P&&t(34,ye=P.isAgentFromDifferentRepo)},r.$$.update=()=>{var P;1&r.$$.dirty[0]&&t(6,pe=H.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Bi(H.diff)),256&r.$$.dirty[1]&&t(23,s=n.additions),256&r.$$.dirty[1]&&t(22,i=n.deletions),1&r.$$.dirty[0]&&t(21,o=zi(H)),1&r.$$.dirty[0]&&t(20,l=Li(H)),8&r.$$.dirty[0]&&t(38,a=Ln(U)),8&r.$$.dirty[0]&&t(19,u=ut(U)),8&r.$$.dirty[0]&&t(37,c=function(O){if(Ln(O))return!1;const C=Ut(O);return ko.includes(C)}(U)),1&r.$$.dirty[0]&&t(10,d=((P=H.originalCode)==null?void 0:P.length)||0),64&r.$$.dirty[0]&&t(9,p=(pe==null?void 0:pe.length)||0),1024&r.$$.dirty[0]&&t(36,k=Mn(d)),512&r.$$.dirty[0]&&t(35,w=Mn(p)),65&r.$$.dirty[0]&&t(8,v=!pe&&!!H.originalCode),65&r.$$.dirty[0]&&t(7,b=!!pe&&!H.originalCode),128&r.$$.dirty[1]&&t(18,A=a),192&r.$$.dirty[1]&&t(17,_=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,$=!a&&!c&&(w||v&&k||b&&w)),512&r.$$.dirty[1]&&t(15,D=Ys(I==null?void 0:I.category,I==null?void 0:I.intensity)),8&r.$$.dirty[0]&&t(12,F=Xs(U)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,E=J||ye),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,T=J?"Applying changes...":fe?"Reapply changes to local file":ye?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[H,ce,ae,U,K,fe,pe,b,v,p,d,be,F,T,E,D,$,_,A,u,l,o,i,s,q,Ce,function(P){t(6,pe=P.detail.modifiedCode),ne==null||ne(pe)},function(){Re.reportApplyChangesEvent(),t(0,H.modifiedCode=pe,H),ne==null||ne(pe),he==null||he()},async function(){Fe&&(t(11,be="Opening file..."),await Fe()?ke():(t(11,be="Failed to open file. Does the file exist?"),setTimeout(()=>{ke()},2e3)))},ue,J,he,ne,Fe,ye,w,k,c,a,n,I,function(P){ce=P,t(1,ce)},function(P){ae=P,t(2,ae)}]}class il extends se{constructor(e){super(),ie(this,e,sl,nl,ee,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function qn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function rl(r,e,t){const n=r.slice();return n[1]=e[t],n}function ol(r,e,t){const n=r.slice();return n[1]=e[t],n}function ll(r){let e;return{c(){e=y("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',x(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){h(t,e,n)},p:te,d(t){t&&m(e)}}}function al(r){let e,t,n,s,i=de(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=ll(ol(r,i,l));return{c(){e=y("div"),t=y("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=j(),s=y("div");for(let l=0;l<o.length;l+=1)o[l].c();x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),x(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){h(l,e,a),B(e,t),B(e,n),B(e,s);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(s,null)},p:te,d(l){l&&m(e),Me(o,l)}}}function jn(r){let e,t,n,s,i,o,l=r[3]===0&&function(c){let d;return{c(){d=y("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',x(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,k){h(p,d,k)},d(p){p&&m(d)}}}(),a=de(Array(2)),u=[];for(let c=0;c<a.length;c+=1)u[c]=al(rl(r,a,c));return{c(){e=y("div"),t=y("div"),n=y("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=j(),l&&l.c(),i=j();for(let c=0;c<u.length;c+=1)u[c].c();o=j(),x(n,"class","c-skeleton-diff__content svelte-1eiztmz"),x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){h(c,e,d),B(e,t),B(t,n),B(t,s),l&&l.m(t,null),B(e,i);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(e,null);B(e,o)},p(c,d){},d(c){c&&m(e),l&&l.d(),Me(u,c)}}}function ul(r){let e,t=de(Array(r[0])),n=[];for(let s=0;s<t.length;s+=1)n[s]=jn(qn(r,t,s));return{c(){e=y("div");for(let s=0;s<n.length;s+=1)n[s].c();x(e,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){h(s,e,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(s,[i]){if(1&i){let o;for(t=de(Array(s[0])),o=0;o<t.length;o+=1){const l=qn(s,t,o);n[o]?n[o].p(l,i):(n[o]=jn(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:te,o:te,d(s){s&&m(e),Me(n,s)}}}function cl(r,e,t){let{count:n=2}=e;return r.$$set=s=>{"count"in s&&t(0,n=s.count)},[n]}class dl extends se{constructor(e){super(),ie(this,e,cl,ul,ee,{count:0})}}function Vn(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Un(r){return r.startsWith("/")||r.startsWith("#")}function zt(r){let e,t;const n=r[5].default,s=_e(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=Vs(o,i[l]);return{c(){e=y(`h${r[0].depth}`),s&&s.c(),kt(`h${r[0].depth}`)(e,o)},m(l,a){h(l,e,a),s&&s.m(e,null),t=!0},p(l,a){s&&s.p&&(!t||16&a)&&Be(s,n,l,l[4],t?Le(n,l[4],a,null):ze(l[4]),null),kt(`h${l[0].depth}`)(e,o=Us(i,[(!t||2&a)&&{id:l[1]}]))},i(l){t||(f(s,l),t=!0)},o(l){g(s,l),t=!1},d(l){l&&m(e),s&&s.d(l)}}}function pl(r){let e,t,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&zt(r);return{c(){s&&s.c(),e=xe()},m(i,o){s&&s.m(i,o),h(i,e,o),t=!0},p(i,[o]){`h${i[0].depth}`?n?ee(n,`h${i[0].depth}`)?(s.d(1),s=zt(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):s.p(i,o):(s=zt(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function fl(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e,l;return r.$$set=a=>{"token"in a&&t(0,i=a.token),"options"in a&&t(2,o=a.options),"$$scope"in a&&t(4,s=a.$$scope)},r.$$.update=()=>{var a,u;5&r.$$.dirty&&t(1,(a=i.text,u=o.slugger,l=u.slug(a).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class gl extends se{constructor(e){super(),ie(this,e,fl,pl,ee,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function $l(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("blockquote"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function hl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ml extends se{constructor(e){super(),ie(this,e,hl,$l,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Zn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Hn(r){let e,t,n=de(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Wn(Zn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=xe()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,e,l),t=!0},p(o,l){if(7&l){let a;for(n=de(o[0]),a=0;a<n.length;a+=1){const u=Zn(o,n,a);s[a]?(s[a].p(u,l),f(s[a],1)):(s[a]=Wn(u),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&m(e),Me(s,o)}}}function Wn(r){let e,t;return e=new ui({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Dl(r){let e,t,n=r[0]&&Hn(r);return{c(){n&&n.c(),e=xe()},m(s,i){n&&n.m(s,i),h(s,e,i),t=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&f(n,1)):(n=Hn(s),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(W(),g(n,1,1,()=>{n=null}),Q())},i(s){t||(f(n),t=!0)},o(s){g(n),t=!1},d(s){s&&m(e),n&&n.d(s)}}}function Fl(r,e,t){let{tokens:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class Et extends se{constructor(e){super(),ie(this,e,Fl,Dl,ee,{tokens:0,renderers:1,options:2})}}function Qn(r){let e,t,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Cl]},$$scope:{ctx:o}}}}return s&&(e=tn(s,i(r))),{c(){e&&z(e.$$.fragment),t=xe()},m(o,l){e&&L(e,o,l),h(o,t,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(e){W();const a=e;g(a.$$.fragment,1,0,()=>{M(a,1)}),Q()}s?(e=tn(s,i(o)),z(e.$$.fragment),f(e.$$.fragment,1),L(e,t.parentNode,t)):e=null}else if(s){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)}},i(o){n||(e&&f(e.$$.fragment,o),n=!0)},o(o){e&&g(e.$$.fragment,o),n=!1},d(o){o&&m(t),e&&M(e,o)}}}function xl(r){let e,t=r[0].raw+"";return{c(){e=N(t)},m(n,s){h(n,e,s)},p(n,s){1&s&&t!==(t=n[0].raw+"")&&le(e,t)},i:te,o:te,d(n){n&&m(e)}}}function kl(r){let e,t;return e=new Et({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Cl(r){let e,t,n,s;const i=[kl,xl],o=[];function l(a,u){return"tokens"in a[0]&&a[0].tokens?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t?t.p(a,u):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function vl(r){let e,t,n=r[1][r[0].type]&&Qn(r);return{c(){n&&n.c(),e=xe()},m(s,i){n&&n.m(s,i),h(s,e,i),t=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&f(n,1)):(n=Qn(s),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(W(),g(n,1,1,()=>{n=null}),Q())},i(s){t||(f(n),t=!0)},o(s){g(n),t=!1},d(s){s&&m(e),n&&n.d(s)}}}function wl(r,e,t){let{token:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class ui extends se{constructor(e){super(),ie(this,e,wl,vl,ee,{token:0,renderers:1,options:2})}}function Gn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Jn(r){let e,t;return e=new ui({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Lt(r){let e,t,n,s=de(r[0].items),i=[];for(let u=0;u<s.length;u+=1)i[u]=Jn(Gn(r,s,u));const o=u=>g(i[u],1,1,()=>{i[u]=null});let l=[{start:t=r[0].start||1}],a={};for(let u=0;u<l.length;u+=1)a=Vs(a,l[u]);return{c(){e=y(r[3]);for(let u=0;u<i.length;u+=1)i[u].c();kt(r[3])(e,a)},m(u,c){h(u,e,c);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(e,null);n=!0},p(u,c){if(7&c){let d;for(s=de(u[0].items),d=0;d<s.length;d+=1){const p=Gn(u,s,d);i[d]?(i[d].p(p,c),f(i[d],1)):(i[d]=Jn(p),i[d].c(),f(i[d],1),i[d].m(e,null))}for(W(),d=s.length;d<i.length;d+=1)o(d);Q()}kt(u[3])(e,a=Us(l,[(!n||1&c&&t!==(t=u[0].start||1))&&{start:t}]))},i(u){if(!n){for(let c=0;c<s.length;c+=1)f(i[c]);n=!0}},o(u){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)g(i[c]);n=!1},d(u){u&&m(e),Me(i,u)}}}function yl(r){let e,t=r[3],n=r[3]&&Lt(r);return{c(){n&&n.c(),e=xe()},m(s,i){n&&n.m(s,i),h(s,e,i)},p(s,[i]){s[3]?t?ee(t,s[3])?(n.d(1),n=Lt(s),t=s[3],n.c(),n.m(e.parentNode,e)):n.p(s,i):(n=Lt(s),t=s[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=s[3])},i:te,o(s){g(n,s)},d(s){s&&m(e),n&&n.d(s)}}}function Al(r,e,t){let n,{token:s}=e,{options:i}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,i=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class bl extends se{constructor(e){super(),ie(this,e,Al,yl,ee,{token:0,options:1,renderers:2})}}function El(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("li"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function _l(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Bl extends se{constructor(e){super(),ie(this,e,_l,El,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function zl(r){let e;return{c(){e=y("br")},m(t,n){h(t,e,n)},p:te,i:te,o:te,d(t){t&&m(e)}}}function Ll(r,e,t){return[void 0,void 0,void 0]}class Ml extends se{constructor(e){super(),ie(this,e,Ll,zl,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Rl(r){let e,t,n,s,i=r[0].text+"";return{c(){e=y("pre"),t=y("code"),n=N(i),x(t,"class",s=`lang-${r[0].lang}`)},m(o,l){h(o,e,l),B(e,t),B(t,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&le(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&x(t,"class",s)},i:te,o:te,d(o){o&&m(e)}}}function Nl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Tl extends se{constructor(e){super(),ie(this,e,Nl,Rl,ee,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ol(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=y("code"),t=N(n)},m(s,i){h(s,e,i),B(e,t)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&le(t,n)},i:te,o:te,d(s){s&&m(e)}}}function Sl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Pl extends se{constructor(e){super(),ie(this,e,Sl,Ol,ee,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Yn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Xn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Kn(r,e,t){const n=r.slice();return n[9]=e[t],n}function es(r){let e,t,n,s;return t=new Et({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=y("th"),z(t.$$.fragment),n=j(),x(e,"scope","col")},m(i,o){h(i,e,o),L(t,e,null),B(e,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),t.$set(l)},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&m(e),M(t)}}}function ts(r){let e,t,n;return t=new Et({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=y("td"),z(t.$$.fragment)},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function ns(r){let e,t,n,s=de(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=ts(Xn(r,s,l));const o=l=>g(i[l],1,1,()=>{i[l]=null});return{c(){e=y("tr");for(let l=0;l<i.length;l+=1)i[l].c();t=j()},m(l,a){h(l,e,a);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);B(e,t),n=!0},p(l,a){if(7&a){let u;for(s=de(l[3]),u=0;u<s.length;u+=1){const c=Xn(l,s,u);i[u]?(i[u].p(c,a),f(i[u],1)):(i[u]=ts(c),i[u].c(),f(i[u],1),i[u].m(e,t))}for(W(),u=s.length;u<i.length;u+=1)o(u);Q()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)f(i[a]);n=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)g(i[a]);n=!1},d(l){l&&m(e),Me(i,l)}}}function Il(r){let e,t,n,s,i,o,l=de(r[0].header),a=[];for(let k=0;k<l.length;k+=1)a[k]=es(Kn(r,l,k));const u=k=>g(a[k],1,1,()=>{a[k]=null});let c=de(r[0].rows),d=[];for(let k=0;k<c.length;k+=1)d[k]=ns(Yn(r,c,k));const p=k=>g(d[k],1,1,()=>{d[k]=null});return{c(){e=y("table"),t=y("thead"),n=y("tr");for(let k=0;k<a.length;k+=1)a[k].c();s=j(),i=y("tbody");for(let k=0;k<d.length;k+=1)d[k].c()},m(k,w){h(k,e,w),B(e,t),B(t,n);for(let v=0;v<a.length;v+=1)a[v]&&a[v].m(n,null);B(e,s),B(e,i);for(let v=0;v<d.length;v+=1)d[v]&&d[v].m(i,null);o=!0},p(k,[w]){if(7&w){let v;for(l=de(k[0].header),v=0;v<l.length;v+=1){const b=Kn(k,l,v);a[v]?(a[v].p(b,w),f(a[v],1)):(a[v]=es(b),a[v].c(),f(a[v],1),a[v].m(n,null))}for(W(),v=l.length;v<a.length;v+=1)u(v);Q()}if(7&w){let v;for(c=de(k[0].rows),v=0;v<c.length;v+=1){const b=Yn(k,c,v);d[v]?(d[v].p(b,w),f(d[v],1)):(d[v]=ns(b),d[v].c(),f(d[v],1),d[v].m(i,null))}for(W(),v=c.length;v<d.length;v+=1)p(v);Q()}},i(k){if(!o){for(let w=0;w<l.length;w+=1)f(a[w]);for(let w=0;w<c.length;w+=1)f(d[w]);o=!0}},o(k){a=a.filter(Boolean);for(let w=0;w<a.length;w+=1)g(a[w]);d=d.filter(Boolean);for(let w=0;w<d.length;w+=1)g(d[w]);o=!1},d(k){k&&m(e),Me(a,k),Me(d,k)}}}function ql(r,e,t){let{token:n}=e,{options:s}=e,{renderers:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,s=o.options),"renderers"in o&&t(2,i=o.renderers)},[n,s,i]}class jl extends se{constructor(e){super(),ie(this,e,ql,Il,ee,{token:0,options:1,renderers:2})}}function Vl(r){let e,t,n=r[0].text+"";return{c(){e=new Ci(!1),t=xe(),e.a=t},m(s,i){e.m(n,s,i),h(s,t,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&e.p(n)},i:te,o:te,d(s){s&&(m(t),e.d())}}}function Ul(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Zl extends se{constructor(e){super(),ie(this,e,Ul,Vl,ee,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Hl(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("p"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function Wl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let Ql=class extends se{constructor(r){super(),ie(this,r,Wl,Hl,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function Gl(r){let e,t,n,s;const i=r[4].default,o=_e(i,r,r[3],null);return{c(){e=y("a"),o&&o.c(),x(e,"href",t=Un(r[0].href)?Vn(r[1].baseUrl,r[0].href):r[0].href),x(e,"title",n=r[0].title)},m(l,a){h(l,e,a),o&&o.m(e,null),s=!0},p(l,[a]){o&&o.p&&(!s||8&a)&&Be(o,i,l,l[3],s?Le(i,l[3],a,null):ze(l[3]),null),(!s||3&a&&t!==(t=Un(l[0].href)?Vn(l[1].baseUrl,l[0].href):l[0].href))&&x(e,"href",t),(!s||1&a&&n!==(n=l[0].title))&&x(e,"title",n)},i(l){s||(f(o,l),s=!0)},o(l){g(o,l),s=!1},d(l){l&&m(e),o&&o.d(l)}}}function Jl(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,s=l.$$scope)},[i,o,void 0,s,n]}class Yl extends se{constructor(e){super(),ie(this,e,Jl,Gl,ee,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Xl(r){let e;const t=r[4].default,n=_e(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Be(n,t,s,s[3],e?Le(t,s[3],i,null):ze(s[3]),null)},i(s){e||(f(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function Kl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ea extends se{constructor(e){super(),ie(this,e,Kl,Xl,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ta(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("dfn"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function na(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class sa extends se{constructor(e){super(),ie(this,e,na,ta,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ia(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("del"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function ra(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class oa extends se{constructor(e){super(),ie(this,e,ra,ia,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function la(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("em"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function aa(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ua extends se{constructor(e){super(),ie(this,e,aa,la,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ca(r){let e;return{c(){e=y("hr")},m(t,n){h(t,e,n)},p:te,i:te,o:te,d(t){t&&m(e)}}}function da(r,e,t){return[void 0,void 0,void 0]}class pa extends se{constructor(e){super(),ie(this,e,da,ca,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function fa(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=y("strong"),s&&s.c()},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function ga(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class $a extends se{constructor(e){super(),ie(this,e,ga,fa,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ha(r){let e,t,n,s;return{c(){e=y("img"),Se(e.src,t=r[0].href)||x(e,"src",t),x(e,"title",n=r[0].title),x(e,"alt",s=r[0].text),x(e,"class","markdown-image svelte-z38cge")},m(i,o){h(i,e,o)},p(i,[o]){1&o&&!Se(e.src,t=i[0].href)&&x(e,"src",t),1&o&&n!==(n=i[0].title)&&x(e,"title",n),1&o&&s!==(s=i[0].text)&&x(e,"alt",s)},i:te,o:te,d(i){i&&m(e)}}}function ma(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Da extends se{constructor(e){super(),ie(this,e,ma,ha,ee,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Fa(r){let e;const t=r[4].default,n=_e(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Be(n,t,s,s[3],e?Le(t,s[3],i,null):ze(s[3]),null)},i(s){e||(f(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function xa(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ss extends se{constructor(e){super(),ie(this,e,xa,Fa,ee,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ka(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let nt={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function is(r){nt=r}const ci=/[&<>"']/,Ca=new RegExp(ci.source,"g"),di=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,va=new RegExp(di.source,"g"),wa={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},rs=r=>wa[r];function Pe(r,e){if(e){if(ci.test(r))return r.replace(Ca,rs)}else if(di.test(r))return r.replace(va,rs);return r}const ya=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Aa(r){return r.replace(ya,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const ba=/(^|[^\[])\^/g;function De(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(ba,"$1"),t=t.replace(s,o),n},getRegex:()=>new RegExp(t,e)};return n}function os(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const rt={exec:()=>null};function ls(r,e){const t=r.replace(/\|/g,(s,i,o)=>{let l=!1,a=i;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function Dt(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==e||t){if(i===e||!t)break;s++}else s++}return r.slice(0,n-s)}function as(r,e,t,n){const s=e.href,i=e.title?Pe(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:s,title:i,text:Pe(o)}}class wt{constructor(e){ge(this,"options");ge(this,"rules");ge(this,"lexer");this.options=e||nt}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:Dt(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(u=>{const c=u.match(/^\s+/);if(c===null)return u;const[d]=c;return d.length>=a.length?u.slice(a.length):u}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const s=Dt(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=Dt(t[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:t[0],tokens:i,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",u=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,A=>" ".repeat(3*A.length)),p=e.split(`
`,1)[0],k=0;this.options.pedantic?(k=2,a=d.trimStart()):(k=t[2].search(/[^ ]/),k=k>4?1:k,a=d.slice(k),k+=t[1].length);let w=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,e=e.substring(p.length+1),c=!0),!c){const A=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),_=new RegExp(`^ {0,${Math.min(3,k-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),$=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:\`\`\`|~~~)`),D=new RegExp(`^ {0,${Math.min(3,k-1)}}#`);for(;e;){const F=e.split(`
`,1)[0];if(p=F,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),$.test(p)||D.test(p)||A.test(p)||_.test(e))break;if(p.search(/[^ ]/)>=k||!p.trim())a+=`
`+p.slice(k);else{if(w||d.search(/[^ ]/)>=4||$.test(d)||D.test(d)||_.test(d))break;a+=`
`+p}w||p.trim()||(w=!0),l+=F+`
`,e=e.substring(F.length+1),d=p.slice(k)}}i.loose||(u?i.loose=!0:/\n *\n *$/.test(l)&&(u=!0));let v,b=null;this.options.gfm&&(b=/^\[[ xX]\] /.exec(a),b&&(v=b[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!b,checked:v,loose:!1,text:a,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=a.trimEnd(),i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){const d=i.items[c].tokens.filter(k=>k.type==="space"),p=d.length>0&&d.some(k=>/\n.*\n/.test(k.raw));i.loose=p}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),s=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:i}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=ls(t[1]),s=t[2].replace(/^\||\| *$/g,"").split("|"),i=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(ls(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Pe(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=Dt(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let u=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===a[0])u++;else if(l[c]===a[1]&&(u--,u<0))return c;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let s=t[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=t[3]?t[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),as(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return as(n,s,n[0],this.lexer)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,a=i,u=0;const c=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+i);(s=c.exec(t))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){a+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){u+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+u);const d=[...s[0]][0].length,p=e.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const w=p.slice(1,-1);return{type:"em",raw:p,text:w,tokens:this.lexer.inlineTokens(w)}}const k=p.slice(2,-2);return{type:"strong",raw:p,text:k,tokens:this.lexer.inlineTokens(k)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=Pe(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=Pe(t[1]),s="mailto:"+n):(n=Pe(t[1]),s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let s,i;if(t[2]==="@")s=Pe(t[0]),i="mailto:"+s;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);s=Pe(t[0]),i=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:Pe(t[0]),{type:"text",raw:t[0],text:n}}}}const ft=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,pi=/(?:[*+-]|\d{1,9}[.)])/,fi=De(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,pi).getRegex(),Zt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ht=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ea=De(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Ht).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),_a=De(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,pi).getRegex(),_t="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Wt=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Ba=De("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Wt).replace("tag",_t).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),us=De(Zt).replace("hr",ft).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_t).getRegex(),Qt={blockquote:De(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",us).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Ea,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:ft,html:Ba,lheading:fi,list:_a,newline:/^(?: *(?:\n|$))+/,paragraph:us,table:rt,text:/^[^\n]+/},cs=De("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ft).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_t).getRegex(),za={...Qt,table:cs,paragraph:De(Zt).replace("hr",ft).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",cs).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_t).getRegex()},La={...Qt,html:De(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Wt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:rt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:De(Zt).replace("hr",ft).replace("heading",` *#{1,6} *[^
]`).replace("lheading",fi).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},gi=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,$i=/^( {2,}|\\)\n(?!\s*$)/,gt="\\p{P}$+<=>`^|~",Ma=De(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,gt).getRegex(),Ra=De(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,gt).getRegex(),Na=De("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,gt).getRegex(),Ta=De("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,gt).getRegex(),Oa=De(/\\([punct])/,"gu").replace(/punct/g,gt).getRegex(),Sa=De(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Pa=De(Wt).replace("(?:-->|$)","-->").getRegex(),Ia=De("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Pa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),yt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,qa=De(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",yt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ds=De(/^!?\[(label)\]\[(ref)\]/).replace("label",yt).replace("ref",Ht).getRegex(),ps=De(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ht).getRegex(),Gt={_backpedal:rt,anyPunctuation:Oa,autolink:Sa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:$i,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:rt,emStrongLDelim:Ra,emStrongRDelimAst:Na,emStrongRDelimUnd:Ta,escape:gi,link:qa,nolink:ps,punctuation:Ma,reflink:ds,reflinkSearch:De("reflink|nolink(?!\\()","g").replace("reflink",ds).replace("nolink",ps).getRegex(),tag:Ia,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:rt},ja={...Gt,link:De(/^!?\[(label)\]\((.*?)\)/).replace("label",yt).getRegex(),reflink:De(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",yt).getRegex()},Ot={...Gt,escape:De(gi).replace("])","~|])").getRegex(),url:De(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Va={...Ot,br:De($i).replace("{2,}","*").getRegex(),text:De(Ot.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Ft={normal:Qt,gfm:za,pedantic:La},it={normal:Gt,gfm:Ot,breaks:Va,pedantic:ja};class Ie{constructor(e){ge(this,"tokens");ge(this,"options");ge(this,"state");ge(this,"tokenizer");ge(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||nt,this.options.tokenizer=this.options.tokenizer||new wt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:Ft.normal,inline:it.normal};this.options.pedantic?(t.block=Ft.pedantic,t.inline=it.pedantic):this.options.gfm&&(t.block=Ft.gfm,this.options.breaks?t.inline=it.breaks:t.inline=it.gfm),this.tokenizer.rules=t}static get rules(){return{block:Ft,inline:it}}static lex(e,t){return new Ie(t).lex(e)}static lexInline(e,t){return new Ie(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,s,i,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,a,u)=>a+"    ".repeat(u.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?t.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=e.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},a),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(i=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=t[t.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n),o=i.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,s,i,o,l,a,u=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(u))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(u))!=null;)u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(u))!=null;)u=u.slice(0,o.index)+"++"+u.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,u,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(i=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(k=>{p=k.call({lexer:this},d),typeof p=="number"&&p>=0&&(c=Math.min(c,p))}),c<1/0&&c>=0&&(i=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(i))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,s=t[t.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class At{constructor(e){ge(this,"options");this.options=e||nt}code(e,t,n){var i;const s=(i=(t||"").match(/^\S*/))==null?void 0:i[0];return e=e.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+Pe(s)+'">'+(n?e:Pe(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:Pe(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const s=t?"ol":"ul";return"<"+s+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+s+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const s=os(e);if(s===null)return n;let i='<a href="'+(e=s)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image(e,t,n){const s=os(e);if(s===null)return n;let i=`<img src="${e=s}" alt="${n}"`;return t&&(i+=` title="${t}"`),i+=">",i}text(e){return e}}class Jt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class Ve{constructor(e){ge(this,"options");ge(this,"renderer");ge(this,"textRenderer");this.options=e||nt,this.options.renderer=this.options.renderer||new At,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Jt}static parse(e,t){return new Ve(t).parse(e)}static parseInline(e,t){return new Ve(t).parseInline(e)}parse(e,t=!0){let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Aa(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",a="";for(let c=0;c<o.header.length;c++)a+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(a);let u="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];a="";for(let p=0;p<d.length;p++)a+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});u+=this.renderer.tablerow(a)}n+=this.renderer.table(l,u);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,a=o.start,u=o.loose;let c="";for(let d=0;d<o.items.length;d++){const p=o.items[d],k=p.checked,w=p.task;let v="";if(p.task){const b=this.renderer.checkbox(!!k);u?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=b+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=b+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:b+" "}):v+=b+" "}v+=this.parse(p.tokens,u),c+=this.renderer.listitem(v,w,!!k)}n+=this.renderer.list(c,l,a);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<e.length&&e[s+1].type==="text";)o=e[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=t.text(o.text);break}case"html":{const o=i;n+=t.html(o.text);break}case"link":{const o=i;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=i;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=i;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=i;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=i;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=i;n+=t.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class ot{constructor(e){ge(this,"options");this.options=e||nt}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}ge(ot,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var et,St,hi,Os;const Ge=new(Os=class{constructor(...r){Kt(this,et);ge(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});ge(this,"options",this.setOptions);ge(this,"parse",ht(this,et,St).call(this,Ie.lex,Ve.parse));ge(this,"parseInline",ht(this,et,St).call(this,Ie.lexInline,Ve.parseInline));ge(this,"Parser",Ve);ge(this,"Renderer",At);ge(this,"TextRenderer",Jt);ge(this,"Lexer",Ie);ge(this,"Tokenizer",wt);ge(this,"Hooks",ot);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const i of r)switch(t=t.concat(e.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const a of l)t=t.concat(this.walkTokens(a.tokens,e));break}case"list":{const o=i;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);t=t.concat(this.walkTokens(a,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=e.renderers[s.name];e.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=e[s.level];i?i.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new At(this.defaults);for(const i in t.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.renderer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new wt(this.defaults);for(const i in t.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=t.tokenizer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new ot;for(const i in t.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.hooks[o],a=s[o];ot.passThroughHooks.has(i)?s[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(s,u)).then(d=>a.call(s,d));const c=l.call(s,u);return a.call(s,c)}:s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,i=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Ie.lex(r,e??this.defaults)}parser(r,e){return Ve.parse(r,e??this.defaults)}},et=new WeakSet,St=function(r,e){return(t,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=ht(this,et,hi).call(this,!!i.silent,!!i.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>e(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(t=i.hooks.preprocess(t));let l=r(t,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let a=e(l,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(l){return o(l)}}},hi=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Pe(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Os);function me(r,e){return Ge.parse(r,e)}me.options=me.setOptions=function(r){return Ge.setOptions(r),me.defaults=Ge.defaults,is(me.defaults),me},me.getDefaults=ka,me.defaults=nt,me.use=function(...r){return Ge.use(...r),me.defaults=Ge.defaults,is(me.defaults),me},me.walkTokens=function(r,e){return Ge.walkTokens(r,e)},me.parseInline=Ge.parseInline,me.Parser=Ve,me.parser=Ve.parse,me.Renderer=At,me.TextRenderer=Jt,me.Lexer=Ie,me.lexer=Ie.lex,me.Tokenizer=wt,me.Hooks=ot,me.parse=me,me.options,me.setOptions,me.use,me.walkTokens,me.parseInline,Ve.parse,Ie.lex;const Ua=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Za=Object.hasOwnProperty;class Ha{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(Ua,"").replace(/ /g,"-"))}(e,t===!0);const i=s;for(;Za.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function Wa(r){let e,t;return e=new Et({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Qa(r,e,t){(function(){const u=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||u(c)},tt(()=>{console.warn=u})})();let n,s,i,{source:o}=e,{options:l={}}=e,{renderers:a={}}=e;return r.$$set=u=>{"source"in u&&t(3,o=u.source),"options"in u&&t(4,l=u.options),"renderers"in u&&t(5,a=u.renderers)},r.$$.update=()=>{var u;56&r.$$.dirty&&(t(0,(u=o,n=new Ie().lex(u))),t(1,s={heading:gl,blockquote:ml,list:bl,list_item:Bl,br:Ml,code:Tl,codespan:Pl,table:jl,html:Zl,paragraph:Ql,link:Yl,text:ea,def:sa,del:oa,em:ua,hr:pa,strong:$a,image:Da,space:ss,escape:ss,...a}),t(2,i={baseUrl:"/",slugger:new Ha,...l}))},[n,s,i,o,l,a]}class Ga extends se{constructor(e){super(),ie(this,e,Qa,Wa,ee,{source:3,options:4,renderers:5})}}const Ja=r=>({}),fs=r=>({}),Ya=r=>({}),gs=r=>({}),Xa=r=>({}),$s=r=>({});function Ka(r){let e,t,n,s,i,o,l,a,u,c,d,p;const k=r[13].topBarLeft,w=_e(k,r,r[12],$s),v=r[13].topBarRight,b=_e(v,r,r[12],gs);function A(F){r[16](F)}let _={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(_.editorInstance=r[0]),o=new Ri({props:_}),Oe.push(()=>Ye(o,"editorInstance",A));const $=r[13].actionsBar,D=_e($,r,r[12],fs);return{c(){e=y("div"),t=y("div"),n=y("div"),w&&w.c(),s=j(),b&&b.c(),i=j(),z(o.$$.fragment),a=j(),u=y("div"),D&&D.c(),x(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),x(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),x(u,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),x(e,"class","c-codeblock svelte-mexfz1"),x(e,"role","button"),x(e,"tabindex","0")},m(F,E){h(F,e,E),B(e,t),B(t,n),w&&w.m(n,null),B(t,s),b&&b.m(t,null),B(e,i),L(o,e,null),B(e,a),B(e,u),D&&D.m(u,null),r[17](e),c=!0,d||(p=[lt(window,"focus",r[15]),lt(e,"mouseenter",r[14])],d=!0)},p(F,[E]){w&&w.p&&(!c||4096&E)&&Be(w,k,F,F[12],c?Le(k,F[12],E,Xa):ze(F[12]),$s),b&&b.p&&(!c||4096&E)&&Be(b,v,F,F[12],c?Le(v,F[12],E,Ya):ze(F[12]),gs);const T={};36&E&&(T.options={lineNumbers:"off",wrappingIndent:"same",padding:F[5],wordWrap:F[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&E&&(T.text=F[3].text),24&E&&(T.lang=F[4]||F[3].lang),64&E&&(T.height=F[6]),!l&&1&E&&(l=!0,T.editorInstance=F[0],Xe(()=>l=!1)),o.$set(T),D&&D.p&&(!c||4096&E)&&Be(D,$,F,F[12],c?Le($,F[12],E,Ja):ze(F[12]),fs)},i(F){c||(f(w,F),f(b,F),f(o.$$.fragment,F),f(D,F),c=!0)},o(F){g(w,F),g(b,F),g(o.$$.fragment,F),g(D,F),c=!1},d(F){F&&m(e),w&&w.d(F),b&&b.d(F),M(o),D&&D.d(F),r[17](null),d=!1,qs(p)}}}function eu(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{scroll:o=!1}=e,{token:l}=e,{language:a}=e,{padding:u={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:d}=e,{height:p}=e;const k=qt.getContext().monaco;Te(r,k,A=>t(18,n=A));const w=jt(),v=()=>{if(!c)return;const A=c.getSelections();if(!(A!=null&&A.length))return;const _=c.getModel();if(A.map($=>(_==null?void 0:_.getValueLengthInRange($))||0).reduce(($,D)=>$+D,0)!==0)return A.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map($=>(_==null?void 0:_.getValueInRange($))||"").join(`
`)},b=()=>{if(c)return c.getValue()||""};return r.$$set=A=>{"scroll"in A&&t(2,o=A.scroll),"token"in A&&t(3,l=A.token),"language"in A&&t(4,a=A.language),"padding"in A&&t(5,u=A.padding),"editorInstance"in A&&t(0,c=A.editorInstance),"element"in A&&t(1,d=A.element),"height"in A&&t(6,p=A.height),"$$scope"in A&&t(12,i=A.$$scope)},r.$$.update=()=>{var A;32&r.$$.dirty&&(A=u,c==null||c.updateOptions({padding:A})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[c,d,o,l,a,u,p,k,w,()=>c&&(v()||b())||"",v,b,i,s,function(A){vi.call(this,r,A)},()=>w.requestLayout(),function(A){c=A,t(0,c)},function(A){Oe[A?"unshift":"push"](()=>{d=A,t(1,d)})}]}class hs extends se{constructor(e){super(),ie(this,e,eu,Ka,ee,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const tu=r=>({codespanContents:2&r}),ms=r=>({codespanContents:r[1]});function nu(r){let e,t,n;const s=r[4].default,i=_e(s,r,r[3],ms),o=i||function(l){let a;return{c(){a=N(l[1])},m(u,c){h(u,a,c)},p(u,c){2&c&&le(a,u[1])},d(u){u&&m(a)}}}(r);return{c(){e=y("span"),t=y("code"),o&&o.c(),x(t,"class","markdown-codespan svelte-1dofrdh")},m(l,a){h(l,e,a),B(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[a]){i?i.p&&(!n||10&a)&&Be(i,s,l,l[3],n?Le(s,l[3],a,tu):ze(l[3]),ms):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(f(o,l),n=!0)},o(l){g(o,l),n=!1},d(l){l&&m(e),o&&o.d(l),r[5](null)}}}function su(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{token:o}=e,{element:l}=e;return r.$$set=a=>{"token"in a&&t(2,o=a.token),"element"in a&&t(0,l=a.element),"$$scope"in a&&t(3,i=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(a){Oe[a?"unshift":"push"](()=>{l=a,t(0,l)})}]}class Ds extends se{constructor(e){super(),ie(this,e,su,nu,ee,{token:2,element:0})}}function iu(r){let e,t,n,s,i=r[0].text+"";return{c(){e=y("span"),t=N("~"),n=N(i),s=N("~")},m(o,l){h(o,e,l),B(e,t),B(e,n),B(e,s)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&le(n,i)},i:te,o:te,d(o){o&&m(e)}}}function ru(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n]}class Fs extends se{constructor(e){super(),ie(this,e,ru,iu,ee,{token:0})}}function ou(r){let e,t;const n=r[1].default,s=_e(n,r,r[0],null);return{c(){e=y("p"),s&&s.c(),x(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){h(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||1&o)&&Be(s,n,i,i[0],t?Le(n,i[0],o,null):ze(i[0]),null)},i(i){t||(f(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&m(e),s&&s.d(i)}}}function lu(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(0,s=i.$$scope)},[s,n]}class xs extends se{constructor(e){super(),ie(this,e,lu,ou,ee,{})}}function au(r){let e,t,n;return t=new Ga({props:{source:r[0],renderers:{codespan:Ds,code:hs,paragraph:xs,del:Fs,...r[1]}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c-markdown svelte-n6ddeo")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:Ds,code:hs,paragraph:xs,del:Fs,...s[1]}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function uu(r,e,t){let{markdown:n}=e,{renderers:s={}}=e;return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown),"renderers"in i&&t(1,s=i.renderers)},[n,s]}class cu extends se{constructor(e){super(),ie(this,e,uu,au,ee,{markdown:0,renderers:1})}}function du(r){let e;return{c(){e=N(r[1])},m(t,n){h(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&m(e)}}}function pu(r){let e;return{c(){e=N(r[1])},m(t,n){h(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&m(e)}}}function fu(r){let e,t,n;function s(l,a){return l[2]?pu:du}let i=s(r),o=i(r);return{c(){e=y("span"),t=y("code"),o.c(),x(t,"class","markdown-codespan svelte-11ta4gi"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){h(l,e,a),B(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:te,o:te,d(l){l&&m(e),o.d(),r[6](null)}}}function gu(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(u){Oe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}class $u extends se{constructor(e){super(),ie(this,e,gu,fu,ee,{token:5,element:0})}}function hu(r){let e,t;return e=new cu({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function mu(r,e,t){let{markdown:n}=e;const s={codespan:$u};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class Du extends se{constructor(e){super(),ie(this,e,mu,hu,ee,{markdown:0})}}const{Boolean:Yt,Map:Fu}=Pi;function ks(r,e,t){const n=r.slice();return n[51]=e[t],n[52]=e,n[53]=t,n}function Cs(r,e,t){const n=r.slice();return n[54]=e[t],n[55]=e,n[56]=t,n}function vs(r,e,t){const n=r.slice();return n[57]=e[t],n[58]=e,n[59]=t,n}function ws(r,e,t){const n=r.slice();return n[60]=e[t],n}function ys(r){let e,t,n,s,i,o,l,a;t=new Ks({}),o=new Ue({props:{variant:"ghost",size:1,$$slots:{default:[xu]},$$scope:{ctx:r}}}),o.$on("click",r[31]);let u=r[4]&&As(r);return{c(){e=y("div"),z(t.$$.fragment),n=j(),s=N(r[20]),i=j(),z(o.$$.fragment),l=j(),u&&u.c(),x(e,"class","c-diff-view__error svelte-18bk2en")},m(c,d){h(c,e,d),L(t,e,null),B(e,n),B(e,s),B(e,i),L(o,e,null),B(e,l),u&&u.m(e,null),a=!0},p(c,d){(!a||1048576&d[0])&&le(s,c[20]);const p={};2&d[2]&&(p.$$scope={dirty:d,ctx:c}),o.$set(p),c[4]?u?(u.p(c,d),16&d[0]&&f(u,1)):(u=As(c),u.c(),f(u,1),u.m(e,null)):u&&(W(),g(u,1,1,()=>{u=null}),Q())},i(c){a||(f(t.$$.fragment,c),f(o.$$.fragment,c),f(u),a=!0)},o(c){g(t.$$.fragment,c),g(o.$$.fragment,c),g(u),a=!1},d(c){c&&m(e),M(t),M(o),u&&u.d()}}}function xu(r){let e;return{c(){e=N("Retry")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function As(r){let e,t;return e=new Ue({props:{variant:"ghost",size:1,$$slots:{default:[ku]},$$scope:{ctx:r}}}),e.$on("click",function(){wi(r[4])&&r[4].apply(this,arguments)}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){r=n;const i={};2&s[2]&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function ku(r){let e;return{c(){e=N("Render as list")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Cu(r){let e,t,n,s,i,o,l,a,u,c,d,p,k,w=r[1]&&r[2]!==r[1]&&bs(r),v=r[2]&&Es(r);o=new $e({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Eu]},$$scope:{ctx:r}}}),a=new li({props:{changedFiles:r[0]}});const b=[Bu,_u],A=[];function _($,D){return $[18]&&$[17].length===0?0:$[7]&&$[7].length>0?1:-1}return~(d=_(r))&&(p=A[d]=b[d](r)),{c(){e=y("div"),t=y("div"),n=y("div"),w&&w.c(),s=j(),v&&v.c(),i=j(),z(o.$$.fragment),l=j(),z(a.$$.fragment),u=j(),c=y("div"),p&&p.c(),x(n,"class","c-diff-view__tree__header svelte-18bk2en"),x(t,"class","c-diff-view__tree svelte-18bk2en"),x(c,"class","c-diff-view__explanation svelte-18bk2en"),x(e,"class","c-diff-view__layout svelte-18bk2en")},m($,D){h($,e,D),B(e,t),B(t,n),w&&w.m(n,null),B(n,s),v&&v.m(n,null),B(n,i),L(o,n,null),B(n,l),L(a,n,null),B(e,u),B(e,c),~d&&A[d].m(c,null),k=!0},p($,D){$[1]&&$[2]!==$[1]?w?(w.p($,D),6&D[0]&&f(w,1)):(w=bs($),w.c(),f(w,1),w.m(n,s)):w&&(W(),g(w,1,1,()=>{w=null}),Q()),$[2]?v?(v.p($,D),4&D[0]&&f(v,1)):(v=Es($),v.c(),f(v,1),v.m(n,i)):v&&(W(),g(v,1,1,()=>{v=null}),Q());const F={};2&D[2]&&(F.$$scope={dirty:D,ctx:$}),o.$set(F);const E={};1&D[0]&&(E.changedFiles=$[0]),a.$set(E);let T=d;d=_($),d===T?~d&&A[d].p($,D):(p&&(W(),g(A[T],1,1,()=>{A[T]=null}),Q()),~d?(p=A[d],p?p.p($,D):(p=A[d]=b[d]($),p.c()),f(p,1),p.m(c,null)):p=null)},i($){k||(f(w),f(v),f(o.$$.fragment,$),f(a.$$.fragment,$),f(p),k=!0)},o($){g(w),g(v),g(o.$$.fragment,$),g(a.$$.fragment,$),g(p),k=!1},d($){$&&m(e),w&&w.d(),v&&v.d(),M(o),M(a),~d&&A[d].d()}}}function vu(r){let e,t,n;return t=new $e({props:{size:2,color:"secondary",$$slots:{default:[rc]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c-diff-view__empty svelte-18bk2en")},m(s,i){h(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};2&i[2]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function bs(r){let e,t,n,s;return e=new $e({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[wu]},$$scope:{ctx:r}}}),n=new $e({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[yu]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),z(n.$$.fragment)},m(i,o){L(e,i,o),h(i,t,o),L(n,i,o),s=!0},p(i,o){const l={};2&o[2]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};2&o[0]|2&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(e.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&m(t),M(e,i),M(n,i)}}}function wu(r){let e;return{c(){e=N("Changes from agent")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function yu(r){let e;return{c(){e=N(r[1])},m(t,n){h(t,e,n)},p(t,n){2&n[0]&&le(e,t[1])},d(t){t&&m(e)}}}function Es(r){let e,t,n,s;return e=new $e({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Au]},$$scope:{ctx:r}}}),n=new $e({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[bu]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=j(),z(n.$$.fragment)},m(i,o){L(e,i,o),h(i,t,o),L(n,i,o),s=!0},p(i,o){const l={};2&o[2]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};4&o[0]|2&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(e.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&m(t),M(e,i),M(n,i)}}}function Au(r){let e;return{c(){e=N("Last user prompt")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function bu(r){let e;return{c(){e=N(r[2])},m(t,n){h(t,e,n)},p(t,n){4&n[0]&&le(e,t[2])},d(t){t&&m(e)}}}function Eu(r){let e;return{c(){e=N("Changed files")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function _u(r){let e,t,n=de(r[7]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Rs(ks(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=xe()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,e,l),t=!0},p(o,l){if(2062143464&l[0]){let a;for(n=de(o[7]),a=0;a<n.length;a+=1){const u=ks(o,n,a);s[a]?(s[a].p(u,l),f(s[a],1)):(s[a]=Rs(u),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Yt);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&m(e),Me(s,o)}}}function Bu(r){let e,t,n,s,i;return t=new qe({props:{content:r[10]?"Applying changes...":r[11]?"All changes applied":r[12]?"Apply all changes":"No changes to apply",$$slots:{default:[ic]},$$scope:{ctx:r}}}),s=new dl({props:{count:2}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),z(s.$$.fragment),x(e,"class","c-diff-view__controls svelte-18bk2en")},m(o,l){h(o,e,l),L(t,e,null),h(o,n,l),L(s,o,l),i=!0},p(o,l){const a={};7168&l[0]&&(a.content=o[10]?"Applying changes...":o[11]?"All changes applied":o[12]?"Apply all changes":"No changes to apply"),15360&l[0]|2&l[2]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&(m(e),m(n)),M(t),M(s,o)}}}function zu(r){let e,t=r[51].title+"";return{c(){e=N(t)},m(n,s){h(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[51].title+"")&&le(e,t)},d(n){n&&m(e)}}}function Lu(r){let e;return{c(){e=y("div"),x(e,"class","c-diff-view__skeleton-title svelte-18bk2en")},m(t,n){h(t,e,n)},p:te,d(t){t&&m(e)}}}function Mu(r){let e,t;return e=new Du({props:{markdown:r[51].description}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};128&s[0]&&(i.markdown=n[51].description),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ru(r){let e,t,n;return{c(){e=y("div"),t=j(),n=y("div"),x(e,"class","c-diff-view__skeleton-text svelte-18bk2en"),x(n,"class","c-diff-view__skeleton-text svelte-18bk2en")},m(s,i){h(s,e,i),h(s,t,i),h(s,n,i)},p:te,i:te,o:te,d(s){s&&(m(e),m(t),m(n))}}}function Nu(r){let e,t,n;return e=new qi({}),{c(){z(e.$$.fragment),t=N(`
                        Expand All`)},m(s,i){L(e,s,i),h(s,t,i),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e,s)}}}function Tu(r){let e,t,n;return e=new Mi({}),{c(){z(e.$$.fragment),t=N(`
                        Collapse All`)},m(s,i){L(e,s,i),h(s,t,i),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e,s)}}}function Ou(r){let e,t,n,s;const i=[Tu,Nu],o=[];function l(a,u){return a[25]?1:0}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function Su(r){let e,t,n,s;return n=new ct({}),{c(){e=N(`Apply all
                          `),t=y("div"),z(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-18bk2en")},m(i,o){h(i,e,o),h(i,t,o),L(n,t,null),s=!0},p:te,i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(m(e),m(t)),M(n)}}}function Pu(r){let e,t,n,s,i,o;t=new $e({props:{size:2,$$slots:{default:[qu]},$$scope:{ctx:r}}});const l=[Vu,ju],a=[];function u(c,d){return c[14]?0:1}return s=u(r),i=a[s]=l[s](r),{c(){e=y("div"),z(t.$$.fragment),n=j(),i.c(),x(e,"class","c-diff-view__applied svelte-18bk2en")},m(c,d){h(c,e,d),L(t,e,null),B(e,n),a[s].m(e,null),o=!0},p(c,d){const p={};2&d[2]&&(p.$$scope={dirty:d,ctx:c}),t.$set(p);let k=s;s=u(c),s!==k&&(W(),g(a[k],1,1,()=>{a[k]=null}),Q(),i=a[s],i||(i=a[s]=l[s](c),i.c()),f(i,1),i.m(e,null))},i(c){o||(f(t.$$.fragment,c),f(i),o=!0)},o(c){g(t.$$.fragment,c),g(i),o=!1},d(c){c&&m(e),M(t),a[s].d()}}}function Iu(r){let e,t,n,s,i;return t=new Pt({props:{size:1,useCurrentColor:!0}}),s=new $e({props:{size:2,$$slots:{default:[Uu]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),z(s.$$.fragment),x(e,"class","c-diff-view__applying svelte-18bk2en")},m(o,l){h(o,e,l),L(t,e,null),B(e,n),L(s,e,null),i=!0},p(o,l){const a={};2&l[2]&&(a.$$scope={dirty:l,ctx:o}),s.$set(a)},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&m(e),M(t),M(s)}}}function qu(r){let e;return{c(){e=N("Applied")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function ju(r){let e,t;return e=new bt({props:{iconName:"check"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Vu(r){let e,t;return e=new Js({props:{slot:"rightIcon"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Uu(r){let e;return{c(){e=N("Applying...")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function Zu(r){let e,t,n,s;const i=[Iu,Pu,Su],o=[];function l(a,u){return a[10]?0:a[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t?t.p(a,u):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function Hu(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[15],$$slots:{default:[Zu]},$$scope:{ctx:r}}}),e.$on("click",r[30]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};32768&s[0]&&(i.disabled=n[15]),19456&s[0]|2&s[2]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function _s(r){let e,t,n,s,i,o,l,a,u,c,d,p,k,w,v=r[6].size+"";i=new Js({});let b=de(r[6]),A=[];for(let $=0;$<b.length;$+=1)A[$]=Bs(ws(r,b,$));const _=$=>g(A[$],1,1,()=>{A[$]=null});return{c(){e=y("div"),t=y("div"),n=y("div"),s=y("div"),z(i.$$.fragment),o=j(),l=y("h5"),a=N("Conflicts ("),u=N(v),c=N(")"),d=j(),p=y("div"),p.textContent="The following files have merge conflicts that need to be resolved manually.",k=j();for(let $=0;$<A.length;$+=1)A[$].c();x(s,"class","c-diff-view__icon svelte-18bk2en"),x(l,"class","c-diff-view__title svelte-18bk2en"),x(n,"class","c-diff-view__content svelte-18bk2en"),x(p,"class","c-diff-view__description svelte-18bk2en"),x(t,"class","c-diff-view__header svelte-18bk2en"),x(e,"class","c-diff-view__subsection c-diff-view__conflicts svelte-18bk2en")},m($,D){h($,e,D),B(e,t),B(t,n),B(n,s),L(i,s,null),B(n,o),B(n,l),B(l,a),B(l,u),B(l,c),B(t,d),B(t,p),B(e,k);for(let F=0;F<A.length;F+=1)A[F]&&A[F].m(e,null);w=!0},p($,D){if((!w||64&D[0])&&v!==(v=$[6].size+"")&&le(u,v),72&D[0]){let F;for(b=de($[6]),F=0;F<b.length;F+=1){const E=ws($,b,F);A[F]?(A[F].p(E,D),f(A[F],1)):(A[F]=Bs(E),A[F].c(),f(A[F],1),A[F].m(e,null))}for(W(),F=b.length;F<A.length;F+=1)_(F);Q()}},i($){if(!w){f(i.$$.fragment,$);for(let D=0;D<b.length;D+=1)f(A[D]);w=!0}},o($){g(i.$$.fragment,$),A=A.filter(Yt);for(let D=0;D<A.length;D+=1)g(A[D]);w=!1},d($){$&&m(e),M(i),Me(A,$)}}}function Wu(r){let e,t;return e=new ji({props:{filepath:r[60]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};64&s[0]&&(i.filepath=n[60]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Qu(r){let e,t;return e=new It({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Gu(r){let e,t;return e=new dt({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Qu]},$$scope:{ctx:r}}}),e.$on("click",function(){return r[39](r[60])}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){r=n;const i={};2&s[2]&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Bs(r){let e,t,n,s,i,o;return t=new qe({props:{content:r[60],$$slots:{default:[Wu]},$$scope:{ctx:r}}}),s=new qe({props:{content:"Open file",$$slots:{default:[Gu]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),z(s.$$.fragment),i=j(),x(e,"class","c-diff-view__conflicts--file svelte-18bk2en")},m(l,a){h(l,e,a),L(t,e,null),B(e,n),L(s,e,null),B(e,i),o=!0},p(l,a){const u={};64&a[0]&&(u.content=l[60]),64&a[0]|2&a[2]&&(u.$$scope={dirty:a,ctx:l}),t.$set(u);const c={};72&a[0]|2&a[2]&&(c.$$scope={dirty:a,ctx:l}),s.$set(c)},i(l){o||(f(t.$$.fragment,l),f(s.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),g(s.$$.fragment,l),o=!1},d(l){l&&m(e),M(t),M(s)}}}function Ju(r){let e,t=r[54].title+"";return{c(){e=N(t)},m(n,s){h(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[54].title+"")&&le(e,t)},d(n){n&&m(e)}}}function Yu(r){let e;return{c(){e=y("div"),x(e,"class","c-diff-view__skeleton-text svelte-18bk2en")},m(t,n){h(t,e,n)},p:te,d(t){t&&m(e)}}}function zs(r){let e,t,n,s,i,o=r[54].warning+"";return t=new Ks({}),{c(){e=y("div"),z(t.$$.fragment),n=j(),s=N(o),x(e,"class","c-diff-view__warning svelte-18bk2en")},m(l,a){h(l,e,a),L(t,e,null),B(e,n),B(e,s),i=!0},p(l,a){(!i||128&a[0])&&o!==(o=l[54].warning+"")&&le(s,o)},i(l){i||(f(t.$$.fragment,l),i=!0)},o(l){g(t.$$.fragment,l),i=!1},d(l){l&&m(e),M(t)}}}function Ls(r,e){let t,n,s,i,o,l=e[53],a=e[56],u=e[57];function c(..._){return e[40](e[57],..._)}function d(){return e[41](e[57])}function p(){return e[42](e[57])}function k(_){e[43](_,e[57])}const w=()=>e[44](n,l,a,u),v=()=>e[44](null,l,a,u);function b(_){e[45](_)}let A={path:e[57].path,change:e[57],descriptions:e[54].descriptions,isExpandedDefault:e[9][e[57].path]!==void 0?!e[9][e[57].path]:e[8],isApplying:e[16][e[57].path]==="pending",hasApplied:e[16][e[57].path]==="applied",onCodeChange:c,onApplyChanges:d,onOpenFile:e[3]?p:void 0,isAgentFromDifferentRepo:e[5]};return e[9][e[57].path]!==void 0&&(A.isCollapsed=e[9][e[57].path]),e[22]!==void 0&&(A.areDescriptionsVisible=e[22]),n=new il({props:A}),Oe.push(()=>Ye(n,"isCollapsed",k)),w(),Oe.push(()=>Ye(n,"areDescriptionsVisible",b)),{key:r,first:null,c(){t=y("div"),z(n.$$.fragment),x(t,"class","c-diff-view__changes-item svelte-18bk2en"),this.first=t},m(_,$){h(_,t,$),L(n,t,null),o=!0},p(_,$){l===(e=_)[53]&&a===e[56]&&u===e[57]||(v(),l=e[53],a=e[56],u=e[57],w());const D={};128&$[0]&&(D.path=e[57].path),128&$[0]&&(D.change=e[57]),128&$[0]&&(D.descriptions=e[54].descriptions),896&$[0]&&(D.isExpandedDefault=e[9][e[57].path]!==void 0?!e[9][e[57].path]:e[8]),65664&$[0]&&(D.isApplying=e[16][e[57].path]==="pending"),65664&$[0]&&(D.hasApplied=e[16][e[57].path]==="applied"),128&$[0]&&(D.onCodeChange=c),128&$[0]&&(D.onApplyChanges=d),136&$[0]&&(D.onOpenFile=e[3]?p:void 0),32&$[0]&&(D.isAgentFromDifferentRepo=e[5]),!s&&640&$[0]&&(s=!0,D.isCollapsed=e[9][e[57].path],Xe(()=>s=!1)),!i&&4194304&$[0]&&(i=!0,D.areDescriptionsVisible=e[22],Xe(()=>i=!1)),n.$set(D)},i(_){o||(f(n.$$.fragment,_),o=!0)},o(_){g(n.$$.fragment,_),o=!1},d(_){_&&m(t),v(),M(n)}}}function Ms(r){let e,t,n,s,i,o,l,a,u,c,d,p=[],k=new Fu;function w(D,F){return D[19]&&D[54].descriptions.length===0?Yu:Ju}i=new fo({props:{type:r[54].type}});let v=w(r),b=v(r),A=!r[19]&&r[54].warning&&zs(r),_=de(r[54].changes);const $=D=>D[57].id;for(let D=0;D<_.length;D+=1){let F=vs(r,_,D),E=$(F);k.set(E,p[D]=Ls(E,F))}return{c(){e=y("div"),t=y("div"),n=y("div"),s=y("div"),z(i.$$.fragment),o=j(),l=y("h5"),b.c(),a=j(),A&&A.c(),u=j(),c=y("div");for(let D=0;D<p.length;D+=1)p[D].c();x(s,"class","c-diff-view__icon svelte-18bk2en"),x(l,"class","c-diff-view__title svelte-18bk2en"),x(n,"class","c-diff-view__content svelte-18bk2en"),x(t,"class","c-diff-view__header svelte-18bk2en"),x(c,"class","c-diff-view__changes svelte-18bk2en"),x(e,"class","c-diff-view__subsection svelte-18bk2en"),x(e,"id",`subsection-${r[53]}-${r[56]}`)},m(D,F){h(D,e,F),B(e,t),B(t,n),B(n,s),L(i,s,null),B(n,o),B(n,l),b.m(l,null),B(n,a),A&&A.m(n,null),B(e,u),B(e,c);for(let E=0;E<p.length;E+=1)p[E]&&p[E].m(c,null);d=!0},p(D,F){const E={};128&F[0]&&(E.type=D[54].type),i.$set(E),v===(v=w(D))&&b?b.p(D,F):(b.d(1),b=v(D),b&&(b.c(),b.m(l,null))),!D[19]&&D[54].warning?A?(A.p(D,F),524416&F[0]&&f(A,1)):(A=zs(D),A.c(),f(A,1),A.m(n,null)):A&&(W(),g(A,1,1,()=>{A=null}),Q()),811664296&F[0]&&(_=de(D[54].changes),W(),p=Zs(p,F,$,1,D,_,k,c,Hs,Ls,null,vs),Q())},i(D){if(!d){f(i.$$.fragment,D),f(A);for(let F=0;F<_.length;F+=1)f(p[F]);d=!0}},o(D){g(i.$$.fragment,D),g(A);for(let F=0;F<p.length;F+=1)g(p[F]);d=!1},d(D){D&&m(e),M(i),b.d(),A&&A.d();for(let F=0;F<p.length;F+=1)p[F].d()}}}function Rs(r){let e,t,n,s,i,o,l,a,u,c,d,p,k;function w(q,U){return q[19]&&q[51].title==="Loading..."?Lu:zu}let v=w(r),b=v(r);const A=[Ru,Mu],_=[];function $(q,U){return q[19]&&q[51].description===""?0:1}l=$(r),a=_[l]=A[l](r);let D=r[53]===0&&function(q){let U,H,K,ce,ue;return H=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[Ou]},$$scope:{ctx:q}}}),H.$on("click",q[27]),ce=new qe({props:{content:q[23],$$slots:{default:[Hu]},$$scope:{ctx:q}}}),{c(){U=y("div"),z(H.$$.fragment),K=j(),z(ce.$$.fragment),x(U,"class","c-diff-view__controls svelte-18bk2en")},m(ae,J){h(ae,U,J),L(H,U,null),B(U,K),L(ce,U,null),ue=!0},p(ae,J){const fe={};33554432&J[0]|2&J[2]&&(fe.$$scope={dirty:J,ctx:ae}),H.$set(fe);const he={};8388608&J[0]&&(he.content=ae[23]),52224&J[0]|2&J[2]&&(he.$$scope={dirty:J,ctx:ae}),ce.$set(he)},i(ae){ue||(f(H.$$.fragment,ae),f(ce.$$.fragment,ae),ue=!0)},o(ae){g(H.$$.fragment,ae),g(ce.$$.fragment,ae),ue=!1},d(ae){ae&&m(U),M(H),M(ce)}}}(r),F=r[6].size>0&&_s(r),E=de(r[51].sections||[]),T=[];for(let q=0;q<E.length;q+=1)T[q]=Ms(Cs(r,E,q));const I=q=>g(T[q],1,1,()=>{T[q]=null});return{c(){e=y("div"),t=y("div"),n=y("div"),s=y("h5"),b.c(),i=j(),o=y("div"),a.c(),u=j(),D&&D.c(),c=j(),F&&F.c(),d=j();for(let q=0;q<T.length;q+=1)T[q].c();p=j(),x(s,"class","c-diff-view__title svelte-18bk2en"),x(o,"class","c-diff-view__description svelte-18bk2en"),x(n,"class","c-diff-view__content svelte-18bk2en"),x(t,"class","c-diff-view__header svelte-18bk2en"),x(e,"class","c-diff-view__section svelte-18bk2en"),x(e,"id",`section-${r[53]}`)},m(q,U){h(q,e,U),B(e,t),B(t,n),B(n,s),b.m(s,null),B(n,i),B(n,o),_[l].m(o,null),B(t,u),D&&D.m(t,null),B(e,c),F&&F.m(e,null),B(e,d);for(let H=0;H<T.length;H+=1)T[H]&&T[H].m(e,null);B(e,p),k=!0},p(q,U){v===(v=w(q))&&b?b.p(q,U):(b.d(1),b=v(q),b&&(b.c(),b.m(s,null)));let H=l;if(l=$(q),l===H?_[l].p(q,U):(W(),g(_[H],1,1,()=>{_[H]=null}),Q(),a=_[l],a?a.p(q,U):(a=_[l]=A[l](q),a.c()),f(a,1),a.m(o,null)),q[53]===0&&D.p(q,U),q[6].size>0?F?(F.p(q,U),64&U[0]&&f(F,1)):(F=_s(q),F.c(),f(F,1),F.m(e,d)):F&&(W(),g(F,1,1,()=>{F=null}),Q()),812188584&U[0]){let K;for(E=de(q[51].sections||[]),K=0;K<E.length;K+=1){const ce=Cs(q,E,K);T[K]?(T[K].p(ce,U),f(T[K],1)):(T[K]=Ms(ce),T[K].c(),f(T[K],1),T[K].m(e,p))}for(W(),K=E.length;K<T.length;K+=1)I(K);Q()}},i(q){if(!k){f(a),f(D),f(F);for(let U=0;U<E.length;U+=1)f(T[U]);k=!0}},o(q){g(a),g(D),g(F),T=T.filter(Yt);for(let U=0;U<T.length;U+=1)g(T[U]);k=!1},d(q){q&&m(e),b.d(),_[l].d(),D&&D.d(),F&&F.d(),Me(T,q)}}}function Xu(r){let e,t,n,s;return n=new ct({}),{c(){e=N(`Apply all
                  `),t=y("div"),z(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-18bk2en")},m(i,o){h(i,e,o),h(i,t,o),L(n,t,null),s=!0},i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(m(e),m(t)),M(n)}}}function Ku(r){let e,t,n;return t=new $e({props:{size:2,$$slots:{default:[tc]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),x(e,"class","c-diff-view__applied svelte-18bk2en")},m(s,i){h(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t)}}}function ec(r){let e,t,n,s,i;return t=new Pt({props:{size:1,useCurrentColor:!0}}),s=new $e({props:{size:2,$$slots:{default:[nc]},$$scope:{ctx:r}}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),z(s.$$.fragment),x(e,"class","c-diff-view__applying svelte-18bk2en")},m(o,l){h(o,e,l),L(t,e,null),B(e,n),L(s,e,null),i=!0},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&m(e),M(t),M(s)}}}function tc(r){let e,t,n;return t=new bt({props:{iconName:"check"}}),{c(){e=N(`Applied
                      `),z(t.$$.fragment)},m(s,i){h(s,e,i),L(t,s,i),n=!0},p:te,i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t,s)}}}function nc(r){let e;return{c(){e=N("Applying...")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function sc(r){let e,t,n,s;const i=[ec,Ku,Xu],o=[];function l(a,u){return a[10]?0:a[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function ic(r){let e,t;return e=new Ue({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[10]||r[11]||r[13].length>0||!r[12],$$slots:{default:[sc]},$$scope:{ctx:r}}}),e.$on("click",r[30]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};15360&s[0]&&(i.disabled=n[10]||n[11]||n[13].length>0||!n[12]),3072&s[0]|2&s[2]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function rc(r){let e;return{c(){e=N("No files changed")},m(t,n){h(t,e,n)},d(t){t&&m(e)}}}function oc(r){let e,t,n,s,i,o=r[20]&&ys(r);const l=[vu,Cu],a=[];function u(c,d){return c[24]?0:1}return n=u(r),s=a[n]=l[n](r),{c(){e=y("div"),o&&o.c(),t=j(),s.c(),x(e,"class","c-diff-view svelte-18bk2en")},m(c,d){h(c,e,d),o&&o.m(e,null),B(e,t),a[n].m(e,null),i=!0},p(c,d){c[20]?o?(o.p(c,d),1048576&d[0]&&f(o,1)):(o=ys(c),o.c(),f(o,1),o.m(e,t)):o&&(W(),g(o,1,1,()=>{o=null}),Q());let p=n;n=u(c),n===p?a[n].p(c,d):(W(),g(a[p],1,1,()=>{a[p]=null}),Q(),s=a[n],s?s.p(c,d):(s=a[n]=l[n](c),s.c()),f(s,1),s.m(e,null))},i(c){i||(f(o),f(s),i=!0)},o(c){g(o),g(s),i=!1},d(c){c&&m(e),o&&o.d(),a[n].d()}}}function lc(r,e,t){let n,s,i,o,l,a,u,c,d,{changedFiles:p}=e,{agentLabel:k}=e,{latestUserPrompt:w}=e,{onApplyChanges:v}=e,{onOpenFile:b}=e,{onRenderBackup:A}=e,{preloadedExplanation:_}=e,{isAgentFromDifferentRepo:$=!1}=e,{conflictFiles:D=new Set}=e;const F=Je(st.key);let E="",T=!1,I=[],q=[],U=!1,H=!1,K=null,ce=!0,ue={},ae=[],J=!1,fe=!1,he=!0;const ne=We({});Te(r,ne,O=>t(16,d=O));let Fe={};function ye(O,C){t(35,Fe[O]=C,Fe)}async function Ce(O,C,R){if(v)return ne.update(S=>(S[O]="pending",S)),new Promise(S=>{v==null||v(O,C,R).then(()=>{ne.update(G=>(G[O]="applied",G)),S()})})}function Re(O){const C={title:"Changed Files",description:`${O.length} files were changed`,sections:[]},R=[],S=[],G=[];return O.forEach(V=>{V.old_path?V.new_path?S.push(V):G.push(V):R.push(V)}),R.length>0&&C.sections.push(pe("Added files","feature",R)),S.length>0&&C.sections.push(pe("Modified files","fix",S)),G.length>0&&C.sections.push(pe("Deleted files","chore",G)),[C]}function pe(O,C,R){const S=[];return R.forEach(G=>{const V=G.new_path||G.old_path,X=G.old_contents||"",Z=G.new_contents||"",Y=G.old_path?G.old_path:"",re=Ct(Y,G.new_path||"/dev/null",X,Z,"","",{context:3}),ve=`${je(V)}-${je(X+Z)}`;S.push({id:ve,path:V,diff:re,originalCode:X,modifiedCode:Z})}),{title:O,descriptions:[],type:C,changes:S}}async function be(){if(!T)return;if(t(18,U=!0),t(19,H=!1),t(20,K=null),t(17,q=[]),t(7,I=[]),l)return void t(18,U=!1);const O=102400;let C=0;if(p.forEach(R=>{var S,G;C+=(((S=R.old_contents)==null?void 0:S.length)||0)+(((G=R.new_contents)==null?void 0:G.length)||0)}),p.length>12||C>512e3){try{t(7,I=Re(p))}catch(R){console.error("Failed to create simple explanation:",R),t(20,K="Failed to create explanation for large changes.")}t(18,U=!1)}else try{const R=new Ti(V=>Ws.postMessage(V)),S=new Map,G=p.map(V=>{const X=V.new_path||V.old_path,Z=V.old_contents||"",Y=V.new_contents||"",re=`${je(X)}-${je(Z+Y)}`;return S.set(re,{old_path:V.old_path,new_path:V.new_path,old_contents:Z,new_contents:Y,change_type:V.change_type}),{id:re,old_path:V.old_path,new_path:V.new_path,change_type:V.change_type}});try{const V=G.length===1;let X=[];V?X=G.map(Z=>({path:Z.new_path||Z.old_path,changes:[{id:Z.id,path:Z.new_path||Z.old_path,diff:`File: ${Z.new_path||Z.old_path}
Change type: ${Z.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):X=(await R.send({type:"get-diff-group-changes-request",data:{changedFiles:G,changesById:!0,apikey:E}},3e4)).data.groupedChanges,t(17,q=X.map(Z=>({path:Z.path,changes:Z.changes.map(Y=>{if(Y.id&&S.has(Y.id)){const re=S.get(Y.id);let ve=Y.diff;return ve&&!ve.startsWith("File:")||(ve=Ct(re.old_path||"",re.new_path||"",re.old_contents||"",re.new_contents||"")),{...Y,diff:ve,old_path:re.old_path,new_path:re.new_path,old_contents:re.old_contents,new_contents:re.new_contents,change_type:re.change_type,originalCode:re.old_contents||"",modifiedCode:re.new_contents||""}}return Y})})))}catch(V){console.error("Failed to group changes with LLM, falling back to simple grouping:",V);try{const X=G.map(Z=>{if(Z.id&&S.has(Z.id)){const Y=S.get(Z.id);return{...Z,old_path:Y.old_path,new_path:Y.new_path,old_contents:Y.old_contents||"",new_contents:Y.new_contents||"",change_type:Y.change_type}}return Z});t(7,I=Re(X)),t(17,q=I[0].sections.map(Z=>({path:Z.title,changes:Z.changes}))),t(19,H=!1)}catch(X){console.error("Failed to create simple explanation:",X),t(20,K="Failed to group changes. Please try again.")}}if(t(18,U=!1),!q||q.length===0)throw new Error("Failed to group changes");if(!I||I.length===0){t(7,I=function(X){const Z={title:"Loading...",description:"",sections:[]};return X.forEach(Y=>{const re=Y.changes.map(Ne=>{if(Ne.id)return Ne;const we=je(Ne.path),Ze=je(Ne.originalCode+Ne.modifiedCode);return{...Ne,id:`${we}-${Ze}`}}),ve={title:Y.path,descriptions:[],type:"other",changes:re};Z.sections.push(ve)}),[Z]}(q));const V=I[0].sections.map(X=>({path:X.title,changes:X.changes.map(Z=>{var Ne,we,Ze;const Y=((Ne=Z.originalCode)==null?void 0:Ne.length)||0,re=((we=Z.modifiedCode)==null?void 0:we.length)||0,ve=((Ze=Z.diff)==null?void 0:Ze.length)||0;return Y>O||re>O||ve>O?{id:Z.id,path:Z.path,diff:`File: ${Z.path}
Content too large to include in explanation request (${Math.max(Y,re,ve)} bytes)`,originalCode:Y>O?`[File content too large: ${Y} bytes]`:Z.originalCode,modifiedCode:re>O?`[File content too large: ${re} bytes]`:Z.modifiedCode}:{id:Z.id,path:Z.path,diff:Z.diff,originalCode:Z.originalCode,modifiedCode:Z.modifiedCode}})}));t(19,H=!0);try{const{explanation:X,error:Z}=await F.getDescriptions(V,E);if(Z==="Token limit exceeded")return t(7,I=Re(p)),t(18,U=!1),void t(19,H=!1);X&&X.length>0&&X.forEach((Y,re)=>{Y.sections&&Y.sections.forEach((ve,Ne)=>{ve.changes&&ve.changes.forEach(we=>{const Ze=I[re];if(Ze&&Ze.sections){const Bt=Ze.sections[Ne];if(Bt&&Bt.changes){const $t=Bt.changes.find(mi=>mi.id===we.id);$t&&(we.originalCode=$t.originalCode,we.modifiedCode=$t.modifiedCode,we.diff=$t.diff)}}})})}),t(7,I=X)}catch(X){console.error("Failed to get descriptions, using skeleton explanation:",X)}}I.length===0&&t(20,K="Failed to generate explanation.")}catch(R){console.error("Failed to get explanation:",R),t(20,K=R instanceof Error?R.message:"An error occurred while generating the explanation.")}finally{t(18,U=!1),t(19,H=!1)}}tt(()=>{const O=localStorage.getItem("anthropic_apikey");O&&(E=O),t(34,T=!0)});let ke="",P="Apply all changes locally";return r.$$set=O=>{"changedFiles"in O&&t(0,p=O.changedFiles),"agentLabel"in O&&t(1,k=O.agentLabel),"latestUserPrompt"in O&&t(2,w=O.latestUserPrompt),"onApplyChanges"in O&&t(32,v=O.onApplyChanges),"onOpenFile"in O&&t(3,b=O.onOpenFile),"onRenderBackup"in O&&t(4,A=O.onRenderBackup),"preloadedExplanation"in O&&t(33,_=O.preloadedExplanation),"isAgentFromDifferentRepo"in O&&t(5,$=O.isAgentFromDifferentRepo),"conflictFiles"in O&&t(6,D=O.conflictFiles)},r.$$.update=()=>{if(65537&r.$$.dirty[0]&&p&&ne.set(p.reduce((O,C)=>{const R=C.new_path||C.old_path;return O[R]=d[R]??"none",O},{})),1&r.$$.dirty[0]&&t(38,a=JSON.stringify(p)),172&r.$$.dirty[1]&&T&&a&&a!==ke&&(t(36,ke=a),_&&_.length>0?(t(7,I=_),t(18,U=!1),t(19,H=!1)):be(),t(10,J=!1),t(11,fe=!1),t(35,Fe={})),896&r.$$.dirty[0]&&I&&I.length>0){const O=mt(I);Array.from(O).forEach(S=>{ue[S]===void 0&&t(9,ue[S]=!ce,ue)});const C=Object.keys(ue).filter(S=>ue[S]),R=Array.from(O);R.length>0&&t(8,ce=!R.some(S=>C.includes(S)))}if(512&r.$$.dirty[0]&&t(25,n=Object.values(ue).some(Boolean)),128&r.$$.dirty[0]|16&r.$$.dirty[1]&&I&&I.length>0&&I.flatMap(O=>O.sections||[]).flatMap(O=>O.changes).forEach(O=>{Fe[O.path]||t(35,Fe[O.path]=O.modifiedCode,Fe)}),128&r.$$.dirty[0]&&t(37,s=JSON.stringify(I)),65664&r.$$.dirty[0]|64&r.$$.dirty[1]&&t(12,i=(()=>{if(s&&d){const O=mt(I);return O.size!==0&&Array.from(O).some(C=>d[C]!=="applied")}return!1})()),65536&r.$$.dirty[0]&&t(11,fe=Object.keys(d).every(O=>d[O]==="applied")),65536&r.$$.dirty[0]&&t(13,o=Object.keys(d).filter(O=>d[O]==="pending")),1&r.$$.dirty[0]&&t(24,l=p.length===0),67712&r.$$.dirty[0]|64&r.$$.dirty[1]&&s&&fe){const O=mt(I);Array.from(O).every(C=>d[C]==="applied")||t(11,fe=!1)}2112&r.$$.dirty[0]&&t(14,u=fe&&D.size>0),15392&r.$$.dirty[0]&&t(15,c=$||J||fe||o.length>0||!i),64544&r.$$.dirty[0]&&(c?$?t(23,P="Cannot apply changes from a different repository locally"):J?t(23,P="Applying changes..."):u?t(23,P="All changes applied, but conflicts need to be resolved manually"):fe?t(23,P="All changes applied"):o.length>0?t(23,P="Waiting for changes to apply"):i||t(23,P="No changes to apply"):t(23,P="Apply all changes locally"))},[p,k,w,b,A,$,D,I,ce,ue,J,fe,i,o,u,c,d,q,U,H,K,ae,he,P,l,n,ne,function(){const O=mt(I),C=Object.values(ue).some(Boolean);t(8,ce=C),Array.from(O).forEach(R=>{t(9,ue[R]=!ce,ue)})},ye,Ce,function(){if(!v)return;F.reportApplyChangesEvent(),t(10,J=!0),t(11,fe=!1);const{filesToApply:O,areAllPathsApplied:C}=yi(I,p,Fe);C||O.length===0?t(11,fe=C):Ai(O,Ce).then(()=>{t(10,J=!1),t(11,fe=!0)})},be,v,_,T,Fe,ke,s,a,O=>b==null?void 0:b(O),(O,C)=>{ye(O.path,C)},O=>{Ce(O.path,O.originalCode,O.modifiedCode)},O=>b(O.path),function(O,C){r.$$.not_equal(ue[C.path],O)&&(ue[C.path]=O,t(9,ue),t(7,I),t(8,ce),t(34,T),t(38,a),t(36,ke),t(33,_),t(0,p))},function(O,C,R,S){Oe[O?"unshift":"push"](()=>{ae[100*C+10*R+S.path.length%10]=O,t(21,ae)})},function(O){he=O,t(22,he)}]}class ac extends se{constructor(e){super(),ie(this,e,lc,oc,ee,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:32,onOpenFile:3,onRenderBackup:4,preloadedExplanation:33,isAgentFromDifferentRepo:5,conflictFiles:6},null,[-1,-1,-1])}}function Ns(r){let e,t,n=r[8].opts,s=Ts(r);return{c(){e=y("div"),s.c(),x(e,"class","file-explorer-contents svelte-5tfpo4")},m(i,o){h(i,e,o),s.m(e,null),t=!0},p(i,o){256&o&&ee(n,n=i[8].opts)?(W(),g(s,1,1,te),Q(),s=Ts(i),s.c(),f(s,1),s.m(e,null)):s.p(i,o)},i(i){t||(f(s),t=!0)},o(i){g(s),t=!1},d(i){i&&m(e),s.d(i)}}}function uc(r){var n,s;let e,t;return e=new ac({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[12],preloadedExplanation:(s=(n=r[8])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation,isAgentFromDifferentRepo:r[5],conflictFiles:r[6]}}),{c(){z(e.$$.fragment)},m(i,o){L(e,i,o),t=!0},p(i,o){var a,u;const l={};1&o&&(l.changedFiles=i[0]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),128&o&&(l.onRenderBackup=i[12]),256&o&&(l.preloadedExplanation=(u=(a=i[8])==null?void 0:a.opts)==null?void 0:u.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=i[5]),64&o&&(l.conflictFiles=i[6]),e.$set(l)},i(i){t||(f(e.$$.fragment,i),t=!0)},o(i){g(e.$$.fragment,i),t=!1},d(i){M(e,i)}}}function cc(r){let e,t;return e=new ao({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ts(r){let e,t,n,s;const i=[cc,uc],o=[];function l(a,u){return a[7]==="changedFiles"?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),h(a,n,u),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t?t.p(a,u):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){g(t),s=!1},d(a){a&&m(n),o[e].d(a)}}}function dc(r){let e,t,n,s=r[0]&&Ns(r);return{c(){e=y("div"),t=y("div"),s&&s.c(),x(t,"class","file-explorer-main svelte-5tfpo4"),x(e,"class","diff-page svelte-5tfpo4")},m(i,o){h(i,e,o),B(e,t),s&&s.m(t,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&f(s,1)):(s=Ns(i),s.c(),f(s,1),s.m(t,null)):s&&(W(),g(s,1,1,()=>{s=null}),Q())},i(i){n||(f(s),n=!0)},o(i){g(s),n=!1},d(i){i&&m(e),s&&s.d()}}}function pc(r,e,t){let n,{changedFiles:s=[]}=e,{pendingFiles:i=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:a}=e,{isAgentFromDifferentRepo:u=!1}=e,c=new Set;const d=Je(st.key),p=Je(vt.key);Te(r,p,w=>t(8,n=w));let k="summary";return function(w){w.subscribe(v=>{if(v){const b=document.getElementById(Nt(v));b&&b.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(w=null){const v=We(w);return Mt(ii,v),v}(null)),r.$$set=w=>{"changedFiles"in w&&t(0,s=w.changedFiles),"pendingFiles"in w&&t(1,i=w.pendingFiles),"appliedFiles"in w&&t(2,o=w.appliedFiles),"agentLabel"in w&&t(3,l=w.agentLabel),"latestUserPrompt"in w&&t(4,a=w.latestUserPrompt),"isAgentFromDifferentRepo"in w&&t(5,u=w.isAgentFromDifferentRepo)},[s,i,o,l,a,u,c,k,n,p,async(w,v,b)=>{const{success:A,hasConflicts:_}=await d.applyChanges(w,v,b);A&&_&&t(6,c=new Set([...c,w]))},w=>d.openFile(w),()=>{t(7,k="changedFiles")}]}class fc extends se{constructor(e){super(),ie(this,e,pc,dc,ee,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function gc(r){let e,t,n,s,i;return t=new Pt({props:{size:1}}),{c(){e=y("div"),z(t.$$.fragment),n=j(),s=y("p"),s.textContent="Loading diff view...",x(e,"class","l-center svelte-ccste2")},m(o,l){h(o,e,l),L(t,e,null),B(e,n),B(e,s),i=!0},p:te,i(o){i||(f(t.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),i=!1},d(o){o&&m(e),M(t)}}}function $c(r){let e,t;return e=new fc({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),1&s&&(i.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function hc(r){let e,t,n,s;const i=[$c,gc],o=[];function l(a,u){return a[0]?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=y("div"),n.c(),x(e,"class","l-main svelte-ccste2")},m(a,u){h(a,e,u),o[t].m(e,null),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(a,u):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(e),o[t].d()}}}function mc(r){let e,t,n,s;return e=new Ni.Root({props:{$$slots:{default:[hc]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(i,o){L(e,i,o),t=!0,n||(s=lt(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(f(e.$$.fragment,i),t=!0)},o(i){g(e.$$.fragment,i),t=!1},d(i){M(e,i),n=!1,s()}}}function Dc(r,e,t){let n,s,i=new bi(Ws),o=new vt(i);Te(r,o,a=>t(4,s=a)),i.registerConsumer(o);let l=new st(i);return Mt(st.key,l),Mt(vt.key,o),tt(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=s.opts)},[n,i,o,l,s]}new class extends se{constructor(r){super(),ie(this,r,Dc,mc,ee,{})}}({target:document.getElementById("app")});
