import{S as h,i as p,s as y,W as b,K as v,c as o,a3 as f,ac as g,e as w,f as L,M as N,n as u,h as j}from"./SpinnerAugment-CQKp6jSN.js";function k(a){let t,i,s;return{c(){t=b("span"),i=v(a[1]),o(t,"class",s=f(`material-symbols-outlined ${a[0]}`)+" svelte-htlsjs"),g(t,"font-variation-settings","'FILL' "+a[3]+", 'wght' "+a[4]+", 'GRAD' "+a[5]),o(t,"title",a[2])},m(e,l){w(e,t,l),L(t,i)},p(e,[l]){2&l&&N(i,e[1]),1&l&&s!==(s=f(`material-symbols-outlined ${e[0]}`)+" svelte-htlsjs")&&o(t,"class",s),56&l&&g(t,"font-variation-settings","'FILL' "+e[3]+", 'wght' "+e[4]+", 'GRAD' "+e[5]),4&l&&o(t,"title",e[2])},i:u,o:u,d(e){e&&j(t)}}}function x(a,t,i){let s,e,l,{class:m=""}=t,{iconName:$=""}=t,{fill:r=!1}=t,{grade:c="normal"}=t,{title:d}=t;return a.$$set=n=>{"class"in n&&i(0,m=n.class),"iconName"in n&&i(1,$=n.iconName),"fill"in n&&i(6,r=n.fill),"grade"in n&&i(7,c=n.grade),"title"in n&&i(2,d=n.title)},a.$$.update=()=>{if(64&a.$$.dirty&&i(3,s=r?"1":"0"),64&a.$$.dirty&&i(4,e=r?"700":"400"),128&a.$$.dirty)switch(c){case"low":i(5,l="-25");break;case"normal":i(5,l="0");break;case"high":i(5,l="200")}},[m,$,d,s,e,l,r,c]}class D extends h{constructor(t){super(),p(this,t,x,k,y,{class:0,iconName:1,fill:6,grade:7,title:2})}}export{D as M};
