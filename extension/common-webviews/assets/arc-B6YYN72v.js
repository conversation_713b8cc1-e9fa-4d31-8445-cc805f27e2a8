import{L as dt,M as lt,N as f,O as Tt,P as $,Q as T,R as rt,S as ft,T as xt,V as W,W as o,X as N,Y as gt,Z as At,$ as Rt}from"./AugmentMessage-DZ4G-ao3.js";function Ot(i){return i.innerRadius}function Pt(i){return i.outerRadius}function Lt(i){return i.startAngle}function Mt(i){return i.endAngle}function Nt(i){return i&&i.padAngle}function E(i,j,b,q,O,P,_){var e=i-b,G=j-q,c=(_?P:-P)/W(e*e+G*G),n=c*G,A=-c*e,u=i+n,a=j+A,y=b+n,l=q+A,Z=(u+y)/2,s=(a+l)/2,t=y-u,r=l-a,m=t*t+r*r,h=O-P,x=u*l-y*a,v=(r<0?-1:1)*W(Rt(0,h*h*m-x*x)),L=(x*r-t*v)/m,M=(-x*t-r*v)/m,k=(x*r+t*v)/m,C=(-x*t+r*v)/m,d=L-Z,g=M-s,p=k-Z,D=C-s;return d*d+g*g>p*p+D*D&&(L=k,M=C),{cx:L,cy:M,x01:-n,y01:-A,x11:L*(O/h-1),y11:M*(O/h-1)}}function qt(){var i=Ot,j=Pt,b=N(0),q=null,O=Lt,P=Mt,_=Nt,e=null,G=dt(c);function c(){var n,A,u=+i.apply(this,arguments),a=+j.apply(this,arguments),y=O.apply(this,arguments)-lt,l=P.apply(this,arguments)-lt,Z=ft(l-y),s=l>y;if(e||(e=n=G()),a<u&&(A=a,a=u,u=A),a>f)if(Z>Tt-f)e.moveTo(a*$(y),a*T(y)),e.arc(0,0,a,y,l,!s),u>f&&(e.moveTo(u*$(l),u*T(l)),e.arc(0,0,u,l,y,s));else{var t,r,m=y,h=l,x=y,v=l,L=Z,M=Z,k=_.apply(this,arguments)/2,C=k>f&&(q?+q.apply(this,arguments):W(u*u+a*a)),d=rt(ft(a-u)/2,+b.apply(this,arguments)),g=d,p=d;if(C>f){var D=gt(C/u*T(k)),F=gt(C/a*T(k));(L-=2*D)>f?(x+=D*=s?1:-1,v-=D):(L=0,x=v=(y+l)/2),(M-=2*F)>f?(m+=F*=s?1:-1,h-=F):(M=0,m=h=(y+l)/2)}var U=a*$(m),V=a*T(m),J=u*$(v),K=u*T(v);if(d>f){var R,X=a*$(h),Y=a*T(h),w=u*$(x),z=u*T(x);if(Z<xt)if(R=function(tt,nt,pt,mt,at,ct,ht,vt){var st=pt-tt,it=mt-nt,ot=ht-at,yt=vt-ct,H=yt*st-ot*it;if(!(H*H<f))return[tt+(H=(ot*(nt-ct)-yt*(tt-at))/H)*st,nt+H*it]}(U,V,w,z,X,Y,J,K)){var B=U-R[0],I=V-R[1],S=X-R[0],Q=Y-R[1],et=1/T(At((B*S+I*Q)/(W(B*B+I*I)*W(S*S+Q*Q)))/2),ut=W(R[0]*R[0]+R[1]*R[1]);g=rt(d,(u-ut)/(et-1)),p=rt(d,(a-ut)/(et+1))}else g=p=0}M>f?p>f?(t=E(w,z,U,V,a,p,s),r=E(X,Y,J,K,a,p,s),e.moveTo(t.cx+t.x01,t.cy+t.y01),p<d?e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,a,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),!s),e.arc(r.cx,r.cy,p,o(r.y11,r.x11),o(r.y01,r.x01),!s))):(e.moveTo(U,V),e.arc(0,0,a,m,h,!s)):e.moveTo(U,V),u>f&&L>f?g>f?(t=E(J,K,X,Y,u,-g,s),r=E(U,V,w,z,u,-g,s),e.lineTo(t.cx+t.x01,t.cy+t.y01),g<d?e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,u,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),s),e.arc(r.cx,r.cy,g,o(r.y11,r.x11),o(r.y01,r.x01),!s))):e.arc(0,0,u,v,x,s):e.lineTo(J,K)}else e.moveTo(0,0);if(e.closePath(),n)return e=null,n+""||null}return c.centroid=function(){var n=(+i.apply(this,arguments)+ +j.apply(this,arguments))/2,A=(+O.apply(this,arguments)+ +P.apply(this,arguments))/2-xt/2;return[$(A)*n,T(A)*n]},c.innerRadius=function(n){return arguments.length?(i=typeof n=="function"?n:N(+n),c):i},c.outerRadius=function(n){return arguments.length?(j=typeof n=="function"?n:N(+n),c):j},c.cornerRadius=function(n){return arguments.length?(b=typeof n=="function"?n:N(+n),c):b},c.padRadius=function(n){return arguments.length?(q=n==null?null:typeof n=="function"?n:N(+n),c):q},c.startAngle=function(n){return arguments.length?(O=typeof n=="function"?n:N(+n),c):O},c.endAngle=function(n){return arguments.length?(P=typeof n=="function"?n:N(+n),c):P},c.padAngle=function(n){return arguments.length?(_=typeof n=="function"?n:N(+n),c):_},c.context=function(n){return arguments.length?(e=n??null,c):e},c}export{qt as d};
