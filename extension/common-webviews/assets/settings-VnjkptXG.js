var no=Object.defineProperty;var so=(s,e,t)=>e in s?no(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ye=(s,e,t)=>so(s,typeof e!="symbol"?e+"":e,t);import{N as Ve,ak as ro,al as _n,S as $e,i as ve,s as he,W as T,J as E,E as w,K as L,c as _,e as y,f as k,F as x,a8 as Le,ad as Ut,q as B,r as J,u as p,t as m,h as v,I as b,aa as Ss,Y as be,M as fe,T as ie,aC as hr,A as Se,n as H,B as ks,b as et,$ as Ie,a as Fe,X as ss,a0 as Re,a1 as Oe,a2 as Pe,g as Vt,Z as rs,_ as Qs,j as Mt,ab as as,aA as At,ah as lt,L as Ht,a9 as ao,a4 as gr,H as er,w as tr,x as nr,y as sr,d as nn,z as rr,aj as li,C as Ae,D as Ne,G as Ee,aE as io,ao as Cs,a6 as di,ap as $r,a5 as ui,ac as vr,ax as yr}from"./SpinnerAugment-CQKp6jSN.js";import{P as un,B as pi,T as mi,a as oo,D as fi,F as co,L as lo}from"./trash-can-YBGqp3xH.js";import"./design-system-init-D-hN7xfd.js";import{W as ge,a as nt,e as _e,u as jt,o as Lt,h as je,g as uo,H as _r}from"./BaseButton-ESlFPUk1.js";import{T as yn,M as hi}from"./TextTooltipAugment--NM_J2iY.js";import{G as po,S as mo,a as fo,C as ho,N as go,J as $o,L as vo,F as St,b as De,D as yo,c as _o,M as wo}from"./mcp-logo-DrfFqzDb.js";import{a as Os,b as gt,G as xo,L as Ze,F as bo,c as So,R as ko,d as Co,M as To,C as Mo,e as Ao,T as No,U as Eo,f as Zo}from"./github-CwfQWdpa.js";import{M as Yt,C as Io}from"./magnifying-glass-CXSTSDWT.js";import{V as gi}from"./VSCodeCodicon-CONIBlZ6.js";import{n as zs,R as Ro,a as Ps,q as Qn,A as wr,b as $i,d as vi}from"./types-DwxhLPcD.js";import{I as Ts,A as Oo}from"./IconButtonAugment-D-fvrWAT.js";import{o as Po}from"./keypress-DD1aQVr0.js";import{D as jo}from"./Drawer-DUeUh8Wh.js";import{B as Me}from"./ButtonAugment-CAn8LxGl.js";import{T as Lo}from"./Content-D7Q35t53.js";import{T as Nt,D as Te}from"./index-DvKVcjj3.js";import{C as Ms}from"./CalloutAugment-ZPisEIAt.js";import{E as Fo}from"./ellipsis-DA5Ek1es.js";import{P as zo}from"./pen-to-square-CIi2Dx1U.js";import{T as yi}from"./TextAreaAugment-b9B2aQlO.js";import{a as Do,C as Uo,T as _i}from"./CollapseButtonAugment--cD032dy.js";import{M as Vo}from"./index-csTQmXPq.js";import{M as qo}from"./MarkdownEditor-B_cAiW03.js";import{A as Bo,m as Jo}from"./arrow-up-right-from-square-ChzPb9WB.js";import{C as Go,E as Wo}from"./chat-flags-model-BhsWla-l.js";import{R as xr}from"./chat-types-DOHETl9Q.js";import{R as Ho}from"./RulesDropdown-Yzl2DW0K.js";import{C as wi}from"./lodash-8faY21Ia.js";import{C as Ko}from"./CardAugment-KjDsYzQv.js";import"./index-CMtlLYew.js";import"./resize-observer-DdAtcrRr.js";import"./globals-D0QH3NT1.js";import"./index-C4SxYn1J.js";import"./file-paths-BcSg4gks.js";const mn={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class Yo{constructor(e,t=mn){ye(this,"timerId",null);ye(this,"currentMS");ye(this,"step",0);ye(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=mn.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=mn.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=mn.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=mn.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class Xo{constructor(e){ye(this,"configs",Ve([]));ye(this,"pollingManager");ye(this,"_enableDebugFeatures",Ve(!1));ye(this,"_settingsComponentSupported",Ve({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));ye(this,"_enableAgentMode",Ve(!1));ye(this,"_enableInitialOrientation",Ve(!1));ye(this,"_userTier",Ve("unknown"));ye(this,"_guidelines",Ve({}));this._host=e,this.pollingManager=new Yo(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===Os.remoteToolHost){let r=e.identifier.toolId;switch(typeof r=="string"&&/^\d+$/.test(r)&&(r=Number(r)),r){case gt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:xo,requiresAuthentication:t,authUrl:n};case gt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:vo,requiresAuthentication:t,authUrl:n};case gt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:$o,requiresAuthentication:t,authUrl:n};case gt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:go,requiresAuthentication:t,authUrl:n};case gt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:ho,requiresAuthentication:t,authUrl:n};case gt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:fo,requiresAuthentication:t,authUrl:n};case gt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:mo,requiresAuthentication:t,authUrl:n};case gt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:po,requiresAuthentication:t,authUrl:n};case gt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${r}`)}}else if(e.identifier.hostName===Os.localToolHost){const r=e.identifier.toolId;switch(r){case Ze.readFile:case Ze.editFile:case Ze.saveFile:case Ze.launchProcess:case Ze.killProcess:case Ze.readProcess:case Ze.writeProcess:case Ze.listProcesses:case Ze.waitProcess:case Ze.openBrowser:case Ze.clarify:case Ze.onboardingSubAgent:case Ze.strReplaceEditor:case Ze.remember:case Ze.diagnostics:case Ze.setupScript:case Ze.readTerminal:case Ze.gitCommitRetrieval:case Ze.spawnSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:St,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${r}`)}}else if(e.identifier.hostName===Os.sidecarToolHost){const r=e.identifier.toolId;switch(r){case De.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Yt,requiresAuthentication:t,authUrl:n};case De.shell:return{displayName:"Shell",description:"Shell",icon:Yt,requiresAuthentication:t,authUrl:n};case De.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Yt,requiresAuthentication:t,authUrl:n};case De.view:return{displayName:"File View",description:"File Viewer",icon:Yt,requiresAuthentication:t,authUrl:n};case De.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Yt,requiresAuthentication:t,authUrl:n};case De.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:_o,requiresAuthentication:t,authUrl:n};case De.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:St,requiresAuthentication:t,authUrl:n};case De.saveFile:return{displayName:"Save File",description:"Save a new file",icon:bo,requiresAuthentication:t,authUrl:n};case De.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:St,requiresAuthentication:t,authUrl:n};case De.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:St,requiresAuthentication:t,authUrl:n};case De.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:St,requiresAuthentication:t,authUrl:n};case De.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:St,requiresAuthentication:t,authUrl:n};case De.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:St,requiresAuthentication:t,authUrl:n};case De.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:St,requiresAuthentication:t,authUrl:n};case De.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:yo,requiresAuthentication:t,authUrl:n};case De.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Yt,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${r}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case ge.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),!0;case ge.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(r=>{const a=n.find(i=>i.name===r.name);return a?{...a,displayName:r.displayName,description:r.description,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,isConfigured:r.isConfigured}:r})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const r=this.transformToolDisplay(n),a=t.find(o=>o.name===n.definition.name),i=(a==null?void 0:a.isConfigured)??!r.requiresAuthentication;return{config:(a==null?void 0:a.config)??{},configString:JSON.stringify((a==null?void 0:a.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:r.displayName,description:r.description,identifier:n.identifier,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,showStatus:!1,statusMessage:"",statusType:"info"}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return ro(this.configs,e=>{const t=e.filter(r=>this.isDisplayableTool(r)),n=new Map;for(const r of t)n.set(r.displayName,r);return Array.from(n.values()).sort((r,a)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[r.displayName]||o,l=i[a.displayName]||o;return c<o&&l<o||c===o&&l===o?c!==l?c-l:r.displayName.localeCompare(a.displayName):c-l})})}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ge.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:ge.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}getSettingsComponentSupported(){return this._settingsComponentSupported}}var pe,Ds;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(pe||(pe={})),(Ds||(Ds={})).mergeShapes=(s,e)=>({...s,...e});const V=pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),vt=s=>{switch(typeof s){case"undefined":return V.undefined;case"string":return V.string;case"number":return isNaN(s)?V.nan:V.number;case"boolean":return V.boolean;case"function":return V.function;case"bigint":return V.bigint;case"symbol":return V.symbol;case"object":return Array.isArray(s)?V.array:s===null?V.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?V.promise:typeof Map<"u"&&s instanceof Map?V.map:typeof Set<"u"&&s instanceof Set?V.set:typeof Date<"u"&&s instanceof Date?V.date:V.object;default:return V.unknown}},O=pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);let it=class xi extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof xi))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};it.create=s=>new it(s);const sn=(s,e)=>{let t;switch(s.code){case O.invalid_type:t=s.received===V.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case O.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,pe.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:t=`Unrecognized key(s) in object: ${pe.joinValues(s.keys,", ")}`;break;case O.invalid_union:t="Invalid input";break;case O.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${pe.joinValues(s.options)}`;break;case O.invalid_enum_value:t=`Invalid enum value. Expected ${pe.joinValues(s.options)}, received '${s.received}'`;break;case O.invalid_arguments:t="Invalid function arguments";break;case O.invalid_return_type:t="Invalid function return type";break;case O.invalid_date:t="Invalid date";break;case O.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:pe.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case O.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case O.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case O.custom:t="Invalid input";break;case O.invalid_intersection_types:t="Intersection results could not be merged";break;case O.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case O.not_finite:t="Number must be finite";break;default:t=e.defaultError,pe.assertNever(s)}return{message:t}};let bi=sn;function is(){return bi}const os=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function z(s,e){const t=is(),n=os({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===sn?void 0:sn].filter(r=>!!r)});s.common.issues.push(n)}let Ge=class Si{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return ne;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return Si.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return ne;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}};const ne=Object.freeze({status:"aborted"}),cs=s=>({status:"dirty",value:s}),Be=s=>({status:"valid",value:s}),Us=s=>s.status==="aborted",Vs=s=>s.status==="dirty",qt=s=>s.status==="valid",wn=s=>typeof Promise<"u"&&s instanceof Promise;function ls(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function ki(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var K,fn,hn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(K||(K={}));let mt=class{constructor(s,e,t,n){this._cachedPath=[],this.parent=s,this.data=e,this._path=t,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const br=(s,e)=>{if(qt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new it(s.common.issues);return this._error=t,this._error}}};function re(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}let oe=class{get description(){return this._def.description}_getType(s){return vt(s.data)}_getOrReturnCtx(s,e){return e||{common:s.parent.common,data:s.data,parsedType:vt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}_processInputParams(s){return{status:new Ge,ctx:{common:s.parent.common,data:s.data,parsedType:vt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}}_parseSync(s){const e=this._parse(s);if(wn(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(s){const e=this._parse(s);return Promise.resolve(e)}parse(s,e){const t=this.safeParse(s,e);if(t.success)return t.data;throw t.error}safeParse(s,e){var t;const n={common:{issues:[],async:(t=e==null?void 0:e.async)!==null&&t!==void 0&&t,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:vt(s)},r=this._parseSync({data:s,path:n.path,parent:n});return br(n,r)}"~validate"(s){var e,t;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:vt(s)};if(!this["~standard"].async)try{const r=this._parseSync({data:s,path:[],parent:n});return qt(r)?{value:r.value}:{issues:n.common.issues}}catch(r){!((t=(e=r==null?void 0:r.message)===null||e===void 0?void 0:e.toLowerCase())===null||t===void 0)&&t.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:s,path:[],parent:n}).then(r=>qt(r)?{value:r.value}:{issues:n.common.issues})}async parseAsync(s,e){const t=await this.safeParseAsync(s,e);if(t.success)return t.data;throw t.error}async safeParseAsync(s,e){const t={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:vt(s)},n=this._parse({data:s,path:t.path,parent:t}),r=await(wn(n)?n:Promise.resolve(n));return br(t,r)}refine(s,e){const t=n=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(n):e;return this._refinement((n,r)=>{const a=s(n),i=()=>r.addIssue({code:O.custom,...t(n)});return typeof Promise<"u"&&a instanceof Promise?a.then(o=>!!o||(i(),!1)):!!a||(i(),!1)})}refinement(s,e){return this._refinement((t,n)=>!!s(t)||(n.addIssue(typeof e=="function"?e(t,n):e),!1))}_refinement(s){return new st({schema:this,typeName:ee.ZodEffects,effect:{type:"refinement",refinement:s}})}superRefine(s){return this._refinement(s)}constructor(s){this.spa=this.safeParseAsync,this._def=s,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ut.create(this,this._def)}nullable(){return Zt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Bt.create(this)}promise(){return on.create(this,this._def)}or(s){return Mn.create([this,s],this._def)}and(s){return An.create(this,s,this._def)}transform(s){return new st({...re(this._def),schema:this,typeName:ee.ZodEffects,effect:{type:"transform",transform:s}})}default(s){const e=typeof s=="function"?s:()=>s;return new In({...re(this._def),innerType:this,defaultValue:e,typeName:ee.ZodDefault})}brand(){return new ar({typeName:ee.ZodBranded,type:this,...re(this._def)})}catch(s){const e=typeof s=="function"?s:()=>s;return new Rn({...re(this._def),innerType:this,catchValue:e,typeName:ee.ZodCatch})}describe(s){return new this.constructor({...this._def,description:s})}pipe(s){return ir.create(this,s)}readonly(){return On.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const Qo=/^c[^\s-]{8,}$/i,ec=/^[0-9a-z]+$/,tc=/^[0-9A-HJKMNP-TV-Z]{26}$/i,nc=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,sc=/^[a-z0-9_-]{21}$/i,rc=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,ac=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ic=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let js;const oc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,cc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,lc=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,dc=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,uc=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,pc=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ci="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",mc=new RegExp(`^${Ci}$`);function Ti(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Mi(s){let e=`${Ci}T${Ti(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function fc(s,e){if(!rc.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function hc(s,e){return!(e!=="v4"&&e||!cc.test(s))||!(e!=="v6"&&e||!dc.test(s))}let rn=class gn extends oe{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==V.string){const i=this._getOrReturnCtx(e);return z(i,{code:O.invalid_type,expected:V.string,received:i.parsedType}),ne}const t=new Ge;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?z(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&z(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")ic.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"email",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")js||(js=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),js.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"emoji",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")nc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"uuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")sc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"nanoid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Qo.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")ec.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cuid2",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")tc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"ulid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),z(n,{validation:"url",code:O.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"regex",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Mi(i).test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?mc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Ti(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?ac.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"duration",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!oc.test(r))&&(a!=="v6"&&a||!lc.test(r))&&(n=this._getOrReturnCtx(e,n),z(n,{validation:"ip",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?fc(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"jwt",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?hc(e.data,i.version)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cidr",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?uc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"base64",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?pc.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"base64url",code:O.invalid_string,message:i.message}),t.dirty()):pe.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:O.invalid_string,...K.errToObj(n)})}_addCheck(e){return new gn({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...K.errToObj(e)})}url(e){return this._addCheck({kind:"url",...K.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...K.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...K.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...K.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...K.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...K.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...K.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...K.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...K.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...K.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...K.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...K.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...K.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...K.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...K.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...K.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...K.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...K.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...K.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...K.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...K.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...K.errToObj(t)})}nonempty(e){return this.min(1,K.errToObj(e))}trim(){return new gn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new gn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new gn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};function gc(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}rn.create=s=>{var e;return new rn({checks:[],typeName:ee.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...re(s)})};let xn=class qs extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==V.number){const r=this._getOrReturnCtx(e);return z(r,{code:O.invalid_type,expected:V.number,received:r.parsedType}),ne}let t;const n=new Ge;for(const r of this._def.checks)r.kind==="int"?pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),z(t,{code:O.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?gc(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_finite,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,r){return new qs({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(r)}]})}_addCheck(e){return new qs({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:K.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:K.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:K.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:K.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};xn.create=s=>new xn({checks:[],typeName:ee.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...re(s)});let bn=class Bs extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==V.bigint)return this._getInvalidInput(e);let t;const n=new Ge;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:V.bigint,received:t.parsedType}),ne}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,r){return new Bs({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(r)}]})}_addCheck(e){return new Bs({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};bn.create=s=>{var e;return new bn({checks:[],typeName:ee.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...re(s)})};let Sn=class extends oe{_parse(s){if(this._def.coerce&&(s.data=!!s.data),this._getType(s)!==V.boolean){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.boolean,received:e.parsedType}),ne}return Be(s.data)}};Sn.create=s=>new Sn({typeName:ee.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...re(s)});let kn=class Ai extends oe{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==V.date){const r=this._getOrReturnCtx(e);return z(r,{code:O.invalid_type,expected:V.date,received:r.parsedType}),ne}if(isNaN(e.data.getTime()))return z(this._getOrReturnCtx(e),{code:O.invalid_date}),ne;const t=new Ge;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):pe.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ai({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:K.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:K.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};kn.create=s=>new kn({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:ee.ZodDate,...re(s)});let ds=class extends oe{_parse(s){if(this._getType(s)!==V.symbol){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.symbol,received:e.parsedType}),ne}return Be(s.data)}};ds.create=s=>new ds({typeName:ee.ZodSymbol,...re(s)});let Cn=class extends oe{_parse(s){if(this._getType(s)!==V.undefined){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.undefined,received:e.parsedType}),ne}return Be(s.data)}};Cn.create=s=>new Cn({typeName:ee.ZodUndefined,...re(s)});let Tn=class extends oe{_parse(s){if(this._getType(s)!==V.null){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.null,received:e.parsedType}),ne}return Be(s.data)}};Tn.create=s=>new Tn({typeName:ee.ZodNull,...re(s)});let an=class extends oe{constructor(){super(...arguments),this._any=!0}_parse(s){return Be(s.data)}};an.create=s=>new an({typeName:ee.ZodAny,...re(s)});let zt=class extends oe{constructor(){super(...arguments),this._unknown=!0}_parse(s){return Be(s.data)}};zt.create=s=>new zt({typeName:ee.ZodUnknown,...re(s)});let wt=class extends oe{_parse(s){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.never,received:e.parsedType}),ne}};wt.create=s=>new wt({typeName:ee.ZodNever,...re(s)});let us=class extends oe{_parse(s){if(this._getType(s)!==V.undefined){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.void,received:e.parsedType}),ne}return Be(s.data)}};us.create=s=>new us({typeName:ee.ZodVoid,...re(s)});let Bt=class ts extends oe{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==V.array)return z(t,{code:O.invalid_type,expected:V.array,received:t.parsedType}),ne;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(z(t,{code:i?O.too_big:O.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(z(t,{code:O.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(z(t,{code:O.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new mt(t,i,t.path,o)))).then(i=>Ge.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new mt(t,i,t.path,o)));return Ge.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new ts({...this._def,minLength:{value:e,message:K.toString(t)}})}max(e,t){return new ts({...this._def,maxLength:{value:e,message:K.toString(t)}})}length(e,t){return new ts({...this._def,exactLength:{value:e,message:K.toString(t)}})}nonempty(e){return this.min(1,e)}};function Xt(s){if(s instanceof Ye){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ut.create(Xt(n))}return new Ye({...s._def,shape:()=>e})}return s instanceof Bt?new Bt({...s._def,type:Xt(s.element)}):s instanceof ut?ut.create(Xt(s.unwrap())):s instanceof Zt?Zt.create(Xt(s.unwrap())):s instanceof Et?Et.create(s.items.map(e=>Xt(e))):s}Bt.create=(s,e)=>new Bt({type:s,minLength:null,maxLength:null,exactLength:null,typeName:ee.ZodArray,...re(e)});let Ye=class tt extends oe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==V.object){const c=this._getOrReturnCtx(e);return z(c,{code:O.invalid_type,expected:V.object,received:c.parsedType}),ne}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof wt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new mt(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof wt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(z(n,{code:O.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new mt(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Ge.mergeObjectSync(t,c)):Ge.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return K.errToObj,new tt({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=K.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new tt({...this._def,unknownKeys:"strip"})}passthrough(){return new tt({...this._def,unknownKeys:"passthrough"})}extend(e){return new tt({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new tt({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ee.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new tt({...this._def,catchall:e})}pick(e){const t={};return pe.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new tt({...this._def,shape:()=>t})}omit(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new tt({...this._def,shape:()=>t})}deepPartial(){return Xt(this)}partial(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new tt({...this._def,shape:()=>t})}required(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ut;)r=r._def.innerType;t[n]=r}}),new tt({...this._def,shape:()=>t})}keyof(){return Pi(pe.objectKeys(this.shape))}};Ye.create=(s,e)=>new Ye({shape:()=>s,unknownKeys:"strip",catchall:wt.create(),typeName:ee.ZodObject,...re(e)}),Ye.strictCreate=(s,e)=>new Ye({shape:()=>s,unknownKeys:"strict",catchall:wt.create(),typeName:ee.ZodObject,...re(e)}),Ye.lazycreate=(s,e)=>new Ye({shape:s,unknownKeys:"strip",catchall:wt.create(),typeName:ee.ZodObject,...re(e)});let Mn=class extends oe{_parse(s){const{ctx:e}=this._processInputParams(s),t=this._def.options;if(e.common.async)return Promise.all(t.map(async n=>{const r={...e,common:{...e.common,issues:[]},parent:null};return{result:await n._parseAsync({data:e.data,path:e.path,parent:r}),ctx:r}})).then(function(n){for(const a of n)if(a.result.status==="valid")return a.result;for(const a of n)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const r=n.map(a=>new it(a.ctx.common.issues));return z(e,{code:O.invalid_union,unionErrors:r}),ne});{let n;const r=[];for(const i of t){const o={...e,common:{...e.common,issues:[]},parent:null},c=i._parseSync({data:e.data,path:e.path,parent:o});if(c.status==="valid")return c;c.status!=="dirty"||n||(n={result:c,ctx:o}),o.common.issues.length&&r.push(o.common.issues)}if(n)return e.common.issues.push(...n.ctx.common.issues),n.result;const a=r.map(i=>new it(i));return z(e,{code:O.invalid_union,unionErrors:a}),ne}}get options(){return this._def.options}};Mn.create=(s,e)=>new Mn({options:s,typeName:ee.ZodUnion,...re(e)});const kt=s=>s instanceof Nn?kt(s.schema):s instanceof st?kt(s.innerType()):s instanceof En?[s.value]:s instanceof Hn?s.options:s instanceof Zn?pe.objectValues(s.enum):s instanceof In?kt(s._def.innerType):s instanceof Cn?[void 0]:s instanceof Tn?[null]:s instanceof ut?[void 0,...kt(s.unwrap())]:s instanceof Zt?[null,...kt(s.unwrap())]:s instanceof ar||s instanceof On?kt(s.unwrap()):s instanceof Rn?kt(s._def.innerType):[];let Ni=class Ei extends oe{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==V.object)return z(t,{code:O.invalid_type,expected:V.object,received:t.parsedType}),ne;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(z(t,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),ne)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=kt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Ei({typeName:ee.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...re(n)})}};function Js(s,e){const t=vt(s),n=vt(e);if(s===e)return{valid:!0,data:s};if(t===V.object&&n===V.object){const r=pe.objectKeys(e),a=pe.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Js(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===V.array&&n===V.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Js(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===V.date&&n===V.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}let An=class extends oe{_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=(r,a)=>{if(Us(r)||Us(a))return ne;const i=Js(r.value,a.value);return i.valid?((Vs(r)||Vs(a))&&e.dirty(),{status:e.value,value:i.data}):(z(t,{code:O.invalid_intersection_types}),ne)};return t.common.async?Promise.all([this._def.left._parseAsync({data:t.data,path:t.path,parent:t}),this._def.right._parseAsync({data:t.data,path:t.path,parent:t})]).then(([r,a])=>n(r,a)):n(this._def.left._parseSync({data:t.data,path:t.path,parent:t}),this._def.right._parseSync({data:t.data,path:t.path,parent:t}))}};An.create=(s,e,t)=>new An({left:s,right:e,typeName:ee.ZodIntersection,...re(t)});let Et=class Zi extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.array)return z(n,{code:O.invalid_type,expected:V.array,received:n.parsedType}),ne;if(n.data.length<this._def.items.length)return z(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ne;!this._def.rest&&n.data.length>this._def.items.length&&(z(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new mt(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Ge.mergeArray(t,a)):Ge.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Zi({...this._def,rest:e})}};Et.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Et({items:s,typeName:ee.ZodTuple,rest:null,...re(e)})};let Ii=class Ri extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.object)return z(n,{code:O.invalid_type,expected:V.object,received:n.parsedType}),ne;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new mt(n,o,n.path,o)),value:i._parse(new mt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Ge.mergeObjectAsync(t,r):Ge.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Ri(t instanceof oe?{keyType:e,valueType:t,typeName:ee.ZodRecord,...re(n)}:{keyType:rn.create(),valueType:e,typeName:ee.ZodRecord,...re(t)})}},ps=class extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(s){const{status:e,ctx:t}=this._processInputParams(s);if(t.parsedType!==V.map)return z(t,{code:O.invalid_type,expected:V.map,received:t.parsedType}),ne;const n=this._def.keyType,r=this._def.valueType,a=[...t.data.entries()].map(([i,o],c)=>({key:n._parse(new mt(t,i,t.path,[c,"key"])),value:r._parse(new mt(t,o,t.path,[c,"value"]))}));if(t.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of a){const c=await o.key,l=await o.value;if(c.status==="aborted"||l.status==="aborted")return ne;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}})}{const i=new Map;for(const o of a){const c=o.key,l=o.value;if(c.status==="aborted"||l.status==="aborted")return ne;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}}}};ps.create=(s,e,t)=>new ps({valueType:e,keyType:s,typeName:ee.ZodMap,...re(t)});let ms=class Gs extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.set)return z(n,{code:O.invalid_type,expected:V.set,received:n.parsedType}),ne;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(z(n,{code:O.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(z(n,{code:O.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return ne;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new mt(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Gs({...this._def,minSize:{value:e,message:K.toString(t)}})}max(e,t){return new Gs({...this._def,maxSize:{value:e,message:K.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};ms.create=(s,e)=>new ms({valueType:s,minSize:null,maxSize:null,typeName:ee.ZodSet,...re(e)});let Oi=class ns extends oe{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==V.function)return z(t,{code:O.invalid_type,expected:V.function,received:t.parsedType}),ne;function n(o,c){return os({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,is(),sn].filter(l=>!!l),issueData:{code:O.invalid_arguments,argumentsError:c}})}function r(o,c){return os({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,is(),sn].filter(l=>!!l),issueData:{code:O.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof on){const o=this;return Be(async function(...c){const l=new it([]),d=await o._def.args.parseAsync(c,a).catch(g=>{throw l.addIssue(n(c,g)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(g=>{throw l.addIssue(r(u,g)),l})})}{const o=this;return Be(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new it([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new it([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ns({...this._def,args:Et.create(e).rest(zt.create())})}returns(e){return new ns({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new ns({args:e||Et.create([]).rest(zt.create()),returns:t||zt.create(),typeName:ee.ZodFunction,...re(n)})}},Nn=class extends oe{get schema(){return this._def.getter()}_parse(s){const{ctx:e}=this._processInputParams(s);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};Nn.create=(s,e)=>new Nn({getter:s,typeName:ee.ZodLazy,...re(e)});let En=class extends oe{_parse(s){if(s.data!==this._def.value){const e=this._getOrReturnCtx(s);return z(e,{received:e.data,code:O.invalid_literal,expected:this._def.value}),ne}return{status:"valid",value:s.data}}get value(){return this._def.value}};function Pi(s,e){return new Hn({values:s,typeName:ee.ZodEnum,...re(e)})}En.create=(s,e)=>new En({value:s,typeName:ee.ZodLiteral,...re(e)});let Hn=class Ws extends oe{constructor(){super(...arguments),fn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return z(t,{expected:pe.joinValues(n),received:t.parsedType,code:O.invalid_type}),ne}if(ls(this,fn)||ki(this,fn,new Set(this._def.values)),!ls(this,fn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return z(t,{received:t.data,code:O.invalid_enum_value,options:n}),ne}return Be(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Ws.create(e,{...this._def,...t})}exclude(e,t=this._def){return Ws.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};fn=new WeakMap,Hn.create=Pi;let Zn=class extends oe{constructor(){super(...arguments),hn.set(this,void 0)}_parse(s){const e=pe.getValidEnumValues(this._def.values),t=this._getOrReturnCtx(s);if(t.parsedType!==V.string&&t.parsedType!==V.number){const n=pe.objectValues(e);return z(t,{expected:pe.joinValues(n),received:t.parsedType,code:O.invalid_type}),ne}if(ls(this,hn)||ki(this,hn,new Set(pe.getValidEnumValues(this._def.values))),!ls(this,hn).has(s.data)){const n=pe.objectValues(e);return z(t,{received:t.data,code:O.invalid_enum_value,options:n}),ne}return Be(s.data)}get enum(){return this._def.values}};hn=new WeakMap,Zn.create=(s,e)=>new Zn({values:s,typeName:ee.ZodNativeEnum,...re(e)});let on=class extends oe{unwrap(){return this._def.type}_parse(s){const{ctx:e}=this._processInputParams(s);if(e.parsedType!==V.promise&&e.common.async===!1)return z(e,{code:O.invalid_type,expected:V.promise,received:e.parsedType}),ne;const t=e.parsedType===V.promise?e.data:Promise.resolve(e.data);return Be(t.then(n=>this._def.type.parseAsync(n,{path:e.path,errorMap:e.common.contextualErrorMap})))}};on.create=(s,e)=>new on({type:s,typeName:ee.ZodPromise,...re(e)});let st=class extends oe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ee.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=this._def.effect||null,r={addIssue:a=>{z(t,a),a.fatal?e.abort():e.dirty()},get path(){return t.path}};if(r.addIssue=r.addIssue.bind(r),n.type==="preprocess"){const a=n.transform(t.data,r);if(t.common.async)return Promise.resolve(a).then(async i=>{if(e.value==="aborted")return ne;const o=await this._def.schema._parseAsync({data:i,path:t.path,parent:t});return o.status==="aborted"?ne:o.status==="dirty"||e.value==="dirty"?cs(o.value):o});{if(e.value==="aborted")return ne;const i=this._def.schema._parseSync({data:a,path:t.path,parent:t});return i.status==="aborted"?ne:i.status==="dirty"||e.value==="dirty"?cs(i.value):i}}if(n.type==="refinement"){const a=i=>{const o=n.refinement(i,r);if(t.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(t.common.async===!1){const i=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});return i.status==="aborted"?ne:(i.status==="dirty"&&e.dirty(),a(i.value),{status:e.value,value:i.value})}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(i=>i.status==="aborted"?ne:(i.status==="dirty"&&e.dirty(),a(i.value).then(()=>({status:e.value,value:i.value}))))}if(n.type==="transform"){if(t.common.async===!1){const a=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});if(!qt(a))return a;const i=n.transform(a.value,r);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(a=>qt(a)?Promise.resolve(n.transform(a.value,r)).then(i=>({status:e.value,value:i})):a)}pe.assertNever(n)}};st.create=(s,e,t)=>new st({schema:s,typeName:ee.ZodEffects,effect:e,...re(t)}),st.createWithPreprocess=(s,e,t)=>new st({schema:e,effect:{type:"preprocess",transform:s},typeName:ee.ZodEffects,...re(t)});let ut=class extends oe{_parse(s){return this._getType(s)===V.undefined?Be(void 0):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};ut.create=(s,e)=>new ut({innerType:s,typeName:ee.ZodOptional,...re(e)});let Zt=class extends oe{_parse(s){return this._getType(s)===V.null?Be(null):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};Zt.create=(s,e)=>new Zt({innerType:s,typeName:ee.ZodNullable,...re(e)});let In=class extends oe{_parse(s){const{ctx:e}=this._processInputParams(s);let t=e.data;return e.parsedType===V.undefined&&(t=this._def.defaultValue()),this._def.innerType._parse({data:t,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};In.create=(s,e)=>new In({innerType:s,typeName:ee.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...re(e)});let Rn=class extends oe{_parse(s){const{ctx:e}=this._processInputParams(s),t={...e,common:{...e.common,issues:[]}},n=this._def.innerType._parse({data:t.data,path:t.path,parent:{...t}});return wn(n)?n.then(r=>({status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new it(t.common.issues)},input:t.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new it(t.common.issues)},input:t.data})}}removeCatch(){return this._def.innerType}};Rn.create=(s,e)=>new Rn({innerType:s,typeName:ee.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...re(e)});let fs=class extends oe{_parse(s){if(this._getType(s)!==V.nan){const e=this._getOrReturnCtx(s);return z(e,{code:O.invalid_type,expected:V.nan,received:e.parsedType}),ne}return{status:"valid",value:s.data}}};fs.create=s=>new fs({typeName:ee.ZodNaN,...re(s)});const $c=Symbol("zod_brand");let ar=class extends oe{_parse(s){const{ctx:e}=this._processInputParams(s),t=e.data;return this._def.type._parse({data:t,path:e.path,parent:e})}unwrap(){return this._def.type}},ir=class ji extends oe{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ne:r.status==="dirty"?(t.dirty(),cs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ne:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new ji({in:e,out:t,typeName:ee.ZodPipeline})}},On=class extends oe{_parse(s){const e=this._def.innerType._parse(s),t=n=>(qt(n)&&(n.value=Object.freeze(n.value)),n);return wn(e)?e.then(n=>t(n)):t(e)}unwrap(){return this._def.innerType}};function Sr(s,e={},t){return s?an.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):an.create()}On.create=(s,e)=>new On({innerType:s,typeName:ee.ZodReadonly,...re(e)});const vc={object:Ye.lazycreate};var ee;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(ee||(ee={}));const kr=rn.create,Cr=xn.create,yc=fs.create,_c=bn.create,Tr=Sn.create,wc=kn.create,xc=ds.create,bc=Cn.create,Sc=Tn.create,kc=an.create,Cc=zt.create,Tc=wt.create,Mc=us.create,Ac=Bt.create,Nc=Ye.create,Ec=Ye.strictCreate,Zc=Mn.create,Ic=Ni.create,Rc=An.create,Oc=Et.create,Pc=Ii.create,jc=ps.create,Lc=ms.create,Fc=Oi.create,zc=Nn.create,Dc=En.create,Uc=Hn.create,Vc=Zn.create,qc=on.create,Mr=st.create,Bc=ut.create,Jc=Zt.create,Gc=st.createWithPreprocess,Wc=ir.create,Hc={string:s=>rn.create({...s,coerce:!0}),number:s=>xn.create({...s,coerce:!0}),boolean:s=>Sn.create({...s,coerce:!0}),bigint:s=>bn.create({...s,coerce:!0}),date:s=>kn.create({...s,coerce:!0})},Kc=ne;var we=Object.freeze({__proto__:null,defaultErrorMap:sn,setErrorMap:function(s){bi=s},getErrorMap:is,makeIssue:os,EMPTY_PATH:[],addIssueToContext:z,ParseStatus:Ge,INVALID:ne,DIRTY:cs,OK:Be,isAborted:Us,isDirty:Vs,isValid:qt,isAsync:wn,get util(){return pe},get objectUtil(){return Ds},ZodParsedType:V,getParsedType:vt,ZodType:oe,datetimeRegex:Mi,ZodString:rn,ZodNumber:xn,ZodBigInt:bn,ZodBoolean:Sn,ZodDate:kn,ZodSymbol:ds,ZodUndefined:Cn,ZodNull:Tn,ZodAny:an,ZodUnknown:zt,ZodNever:wt,ZodVoid:us,ZodArray:Bt,ZodObject:Ye,ZodUnion:Mn,ZodDiscriminatedUnion:Ni,ZodIntersection:An,ZodTuple:Et,ZodRecord:Ii,ZodMap:ps,ZodSet:ms,ZodFunction:Oi,ZodLazy:Nn,ZodLiteral:En,ZodEnum:Hn,ZodNativeEnum:Zn,ZodPromise:on,ZodEffects:st,ZodTransformer:st,ZodOptional:ut,ZodNullable:Zt,ZodDefault:In,ZodCatch:Rn,ZodNaN:fs,BRAND:$c,ZodBranded:ar,ZodPipeline:ir,ZodReadonly:On,custom:Sr,Schema:oe,ZodSchema:oe,late:vc,get ZodFirstPartyTypeKind(){return ee},coerce:Hc,any:kc,array:Ac,bigint:_c,boolean:Tr,date:wc,discriminatedUnion:Ic,effect:Mr,enum:Uc,function:Fc,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>Sr(t=>t instanceof s,e),intersection:Rc,lazy:zc,literal:Dc,map:jc,nan:yc,nativeEnum:Vc,never:Tc,null:Sc,nullable:Jc,number:Cr,object:Nc,oboolean:()=>Tr().optional(),onumber:()=>Cr().optional(),optional:Bc,ostring:()=>kr().optional(),pipeline:Wc,preprocess:Gc,promise:qc,record:Pc,set:Lc,strictObject:Ec,string:kr,symbol:xc,transformer:Mr,tuple:Oc,undefined:bc,union:Zc,unknown:Cc,void:Mc,NEVER:Kc,ZodIssueCode:O,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:it});function dt(s){return(s==null?void 0:s.type)==="http"||(s==null?void 0:s.type)==="sse"}function Qt(s){return(s==null?void 0:s.type)==="stdio"}function hs(s){return dt(s)?s.url:Qt(s)?s.command:""}const Xe=we.object({name:we.string().optional(),title:we.string().optional(),type:we.enum(["stdio","http","sse"]).optional(),command:we.string().optional(),args:we.array(we.union([we.string(),we.number(),we.boolean()])).optional(),env:we.record(we.union([we.string(),we.number(),we.boolean(),we.null(),we.undefined()])).optional(),url:we.string().optional()}).passthrough(),Yc=we.array(Xe),Xc=we.object({servers:we.array(Xe)}).passthrough(),Qc=we.object({mcpServers:we.array(Xe)}).passthrough(),el=we.object({servers:we.record(Xe)}).passthrough(),tl=we.object({mcpServers:we.record(Xe)}).passthrough(),nl=we.record(Xe),sl=Xe.refine(s=>s.command!==void 0||s.url!==void 0,{message:"Server must have either a 'command' or 'url' property"}),rl=Symbol("MCPServerError");let Ke=class Li extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Li.prototype)}};var oi,Fs;let al=(oi=rl,Fs=class{constructor(s){ye(this,"servers",Ve([]));this.host=s,this.loadServersFromStorage()}handleMessageFromExtension(s){const e=s.data;if(e.type===ge.getStoredMCPServersResponse){const t=e.data;return Array.isArray(t)&&this.servers.set(t),!0}return!1}async importServersFromJSON(s){return this.importFromJSON(s)}loadServersFromStorage(){try{this.host.postMessage({type:ge.getStoredMCPServers})}catch(s){console.error("Failed to load MCP servers:",s),this.servers.set([])}}saveServers(s){try{this.host.postMessage({type:ge.setStoredMCPServers,data:s})}catch(e){throw console.error("Failed to save MCP servers:",e),new Ke("Failed to save MCP servers")}}getServers(){return this.servers}addServer(s){this.checkExistingServerName(s.name),this.servers.update(e=>{const t=[...e,{...s,id:crypto.randomUUID()}];return this.saveServers(t),t})}addServers(s){for(const e of s)this.checkExistingServerName(e.name);this.servers.update(e=>{const t=[...e,...s.map(n=>({...n,id:crypto.randomUUID()}))];return this.saveServers(t),t})}checkExistingServerName(s,e){const t=_n(this.servers).find(n=>n.name===s);if(t&&(t==null?void 0:t.id)!==e)throw new Ke(`Server name '${s}' already exists`)}updateServer(s){this.checkExistingServerName(s.name,s.id),this.servers.update(e=>{const t=e.map(n=>n.id===s.id?s:n);return this.saveServers(t),t})}deleteServer(s){this.servers.update(e=>{const t=e.filter(n=>n.id!==s);return this.saveServers(t),t})}toggleDisabledServer(s){this.servers.update(e=>{const t=e.map(n=>n.id===s?{...n,disabled:!n.disabled}:n);return this.saveServers(t),t})}static convertServerToJSON(s){if(dt(s))return JSON.stringify({mcpServers:{[s.name]:{url:s.url,type:s.type}}},null,2);{const e=s;return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}}static parseServerValidationMessages(s){const e=new Map,t=new Map;s.forEach(r=>{var a;r.disabled?e.set(r.id,"MCP server has been manually disabled"):r.tools&&r.tools.length===0?e.set(r.id,"No tools are available for this MCP server"):r.disabledTools&&r.disabledTools.length===((a=r.tools)==null?void 0:a.length)?e.set(r.id,"All tools for this MCP server have validation errors: "+r.disabledTools.join(", ")):r.disabledTools&&r.disabledTools.length>0&&t.set(r.id,"MCP server has validation errors in the following tools which have been disabled: "+r.disabledTools.join(", "))});const n=this.parseDuplicateServerIds(s);return{errors:new Map([...e,...n]),warnings:t}}static parseDuplicateServerIds(s){const e=new Map;for(const n of s)e.has(n.name)||e.set(n.name,[]),e.get(n.name).push(n.id);const t=new Map;for(const[,n]of e)if(n.length>1)for(let r=1;r<n.length;r++)t.set(n[r],"MCP server is disabled due to duplicate server names");return t}parseServerConfigFromJSON(s){try{const e=JSON.parse(s),t=we.union([Yc.transform(n=>n.map(r=>this.normalizeServerConfig(r))),Xc.transform(n=>n.servers.map(r=>this.normalizeServerConfig(r))),Qc.transform(n=>n.mcpServers.map(r=>this.normalizeServerConfig(r))),el.transform(n=>Object.entries(n.servers).map(([r,a])=>{const i=Xe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),tl.transform(n=>Object.entries(n.mcpServers).map(([r,a])=>{const i=Xe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),nl.transform(n=>{if(!Object.values(n).some(r=>{const a=Xe.safeParse(r);return a.success&&(a.data.command!==void 0||a.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(n).map(([r,a])=>{const i=Xe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})}),sl.transform(n=>[this.normalizeServerConfig(n)])]).safeParse(e);if(t.success)return t.data;throw new Ke("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof Ke?e:new Ke("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(s){try{const e=this.parseServerConfigFromJSON(s),t=_n(this.servers),n=new Set(t.map(r=>r.name));for(const r of e){if(!r.name)throw new Ke("All servers must have a name.");if(n.has(r.name))throw new Ke(`A server with the name '${r.name}' already exists.`);n.add(r.name)}return this.servers.update(r=>{const a=[...r,...e.map(i=>({...i,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof Ke?e:new Ke("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(s){try{const e=Xe.transform(t=>{let n;if(t.type)n=t.type;else if(t.url)n="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");n="stdio"}if(n==="http"||n==="sse"){if(!t.url)throw new Error(`${n.toUpperCase()} server must have a 'url' property`);return{type:n,name:t.name||t.title||t.url,url:t.url}}{const r=t.command||"",a=t.args?t.args.map(l=>String(l)):[];if(!r)throw new Error("Stdio server must have a 'command' property");const i=a.length>0?`${r} ${a.join(" ")}`:r,o=t.name||t.title||(r?r.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([l,d])=>d!=null).map(([l,d])=>[l,String(d)])):void 0;return{type:"stdio",name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(s);if(!e.success)throw new Ke(e.error.message);return e.data}catch(e){throw e instanceof Error?new Ke(`Invalid server configuration: ${e.message}`):new Ke("Invalid server configuration")}}},ye(Fs,oi,"MCPServerError"),Fs);class il{constructor(e){ye(this,"_terminalSettings",Ve({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===ge.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ge.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:ge.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:ge.updateTerminalSettings,data:{startupScript:e}})}}function Pn(s,e){return t=>!t.shiftKey&&t.key===s&&(e(t),!0)}var _t=(s=>(s.file="file",s.folder="folder",s))(_t||{});class Tt{constructor(e,t){ye(this,"subscribe");ye(this,"set");ye(this,"update");ye(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case ge.wsContextSourceFoldersChanged:case ge.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ge.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});ye(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ge.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ye(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:ge.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:r,update:a}=Ve({sourceFolders:[],sourceTree:[],syncStatus:zs.done});this.subscribe=n,this.set=r,this.update=a,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:Tt.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==nt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:ge.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:ge.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:ge.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=_n(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(r=>({...r,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=Tt.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(r=>[JSON.stringify([r.fileId.folderRoot,r.fileId.relPath]),r]));for(let r of t){const a=Tt.fileIdToString(r.fileId);if(r.type==="folder"){const i=n.get(a);i&&(r.expanded=i.type==="folder"&&i.expanded,r.expanded&&(r.children=await this.getChildren(r.fileId),r.children=await this.getRefreshedSourceTreeRecurse(i.children,r.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function Ar(s,e,t){const n=s.slice();return n[6]=e[t],n}function Nr(s){let e,t;function n(){return s[5](s[6])}return e=new Ts({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[ol]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[4](s[6])}),e.$on("keyup",function(){Ut(Pn("Enter",n))&&Pn("Enter",n).apply(this,arguments)}),{c(){w(e.$$.fragment)},m(r,a){x(e,r,a),t=!0},p(r,a){s=r;const i={};512&a&&(i.$$scope={dirty:a,ctx:s}),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){b(e,r)}}}function ol(s){let e,t;return e=new So({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Er(s){let e,t;return e=new ie({props:{size:1,class:"file-count",$$slots:{default:[cl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};513&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function cl(s){let e,t=s[6].trackedFileCount.toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1&r&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&fe(e,t)},d(n){n&&v(e)}}}function Zr(s,e){let t,n,r,a,i,o,c,l,d,u,g,f=e[6].name+"",$=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",h=!e[6].isWorkspaceFolder&&Nr(e);r=new gi({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let S=e[6].trackedFileCount&&Er(e);return{key:s,first:null,c(){t=T("div"),h&&h.c(),n=E(),w(r.$$.fragment),a=E(),i=T("span"),o=L(f),c=E(),l=T("span"),d=L($),u=E(),S&&S.c(),_(l,"class","folderRoot svelte-1skknri"),_(i,"class","name svelte-1skknri"),_(t,"class","item svelte-1skknri"),be(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(M,I){y(M,t,I),h&&h.m(t,null),k(t,n),x(r,t,null),k(t,a),k(t,i),k(i,o),k(i,c),k(i,l),k(l,d),k(t,u),S&&S.m(t,null),g=!0},p(M,I){(e=M)[6].isWorkspaceFolder?h&&(B(),m(h,1,1,()=>{h=null}),J()):h?(h.p(e,I),1&I&&p(h,1)):(h=Nr(e),h.c(),p(h,1),h.m(t,n));const Z={};1&I&&(Z.icon=e[3](e[6])),r.$set(Z),(!g||1&I)&&f!==(f=e[6].name+"")&&fe(o,f),(!g||1&I)&&$!==($=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&fe(d,$),e[6].trackedFileCount?S?(S.p(e,I),1&I&&p(S,1)):(S=Er(e),S.c(),p(S,1),S.m(t,null)):S&&(B(),m(S,1,1,()=>{S=null}),J()),(!g||1&I)&&be(t,"workspace-folder",e[6].isWorkspaceFolder)},i(M){g||(p(h),p(r.$$.fragment,M),p(S),g=!0)},o(M){m(h),m(r.$$.fragment,M),m(S),g=!1},d(M){M&&v(t),h&&h.d(),b(r),S&&S.d()}}}function ll(s){let e,t,n,r,a,i,o,c,l=[],d=new Map,u=_e(s[0]);const g=f=>Tt.fileIdToString(f[6].fileId);for(let f=0;f<u.length;f+=1){let $=Ar(s,u,f),h=g($);d.set(h,l[f]=Zr(h,$))}return r=new un({}),{c(){e=T("div");for(let f=0;f<l.length;f+=1)l[f].c();t=E(),n=T("div"),w(r.$$.fragment),a=L(" Add more..."),_(n,"role","button"),_(n,"tabindex","0"),_(n,"class","add-more svelte-1skknri"),_(e,"class","source-folder svelte-1skknri")},m(f,$){y(f,e,$);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(e,null);k(e,t),k(e,n),x(r,n,null),k(n,a),i=!0,o||(c=[Le(n,"keyup",function(){Ut(Pn("Enter",s[1]))&&Pn("Enter",s[1]).apply(this,arguments)}),Le(n,"click",function(){Ut(s[1])&&s[1].apply(this,arguments)})],o=!0)},p(f,[$]){s=f,13&$&&(u=_e(s[0]),B(),l=jt(l,$,g,1,s,u,d,e,Lt,Zr,t,Ar),J())},i(f){if(!i){for(let $=0;$<u.length;$+=1)p(l[$]);p(r.$$.fragment,f),i=!0}},o(f){for(let $=0;$<l.length;$+=1)m(l[$]);m(r.$$.fragment,f),i=!1},d(f){f&&v(e);for(let $=0;$<l.length;$+=1)l[$].d();b(r),o=!1,Ss(c)}}}function dl(s,e,t){let{folders:n=[]}=e,{onAddMore:r}=e,{onRemove:a}=e;return s.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,r=i.onAddMore),"onRemove"in i&&t(2,a=i.onRemove)},[n,r,a,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>a(i.fileId.folderRoot),i=>a(i.fileId.folderRoot)]}class ul extends $e{constructor(e){super(),ve(this,e,dl,ll,he,{folders:0,onAddMore:1,onRemove:2})}}function Ir(s,e,t){const n=s.slice();return n[10]=e[t],n}function Rr(s){let e,t;return e=new ie({props:{size:1,class:"file-count",$$slots:{default:[pl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8193&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function pl(s){let e,t=s[0].trackedFileCount.toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1&r&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&fe(e,t)},d(n){n&&v(e)}}}function Or(s){let e,t,n=[],r=new Map,a=_e(s[5].children);const i=o=>Tt.fileIdToString(o[10].fileId);for(let o=0;o<a.length;o+=1){let c=Ir(s,a,o),l=i(c);r.set(l,n[o]=Pr(l,c))}return{c(){e=T("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","children-container")},m(o,c){y(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,c){38&c&&(a=_e(o[5].children),B(),n=jt(n,c,i,1,o,a,r,e,Lt,Pr,null,Ir),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function Pr(s,e){let t,n,r;return n=new Fi({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};32&i&&(o.data=e[10]),2&i&&(o.wsContextModel=e[1]),4&i&&(o.indentLevel=e[2]+1),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function ml(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,S,M,I,Z=s[0].name+"";n=new gi({props:{icon:s[4]}});let C=s[0].type===_t.folder&&s[0].inclusionState!==nt.excluded&&typeof s[0].trackedFileCount=="number"&&Rr(s),A=s[5]&&Or(s);return{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),a=T("span"),i=L(Z),o=E(),C&&C.c(),c=E(),l=T("img"),h=E(),A&&A.c(),_(a,"class","name svelte-sympus"),hr(l.src,d=s[7][s[0].inclusionState])||_(l,"src",d),_(l,"alt",u=s[8][s[0].inclusionState]),_(t,"class","tree-item svelte-sympus"),_(t,"role","treeitem"),_(t,"aria-selected","false"),_(t,"tabindex","0"),_(t,"title",g=s[0].reason),_(t,"aria-expanded",f=s[0].type===_t.folder&&s[0].expanded),_(t,"aria-level",s[2]),_(t,"style",$=`padding-left: ${10*s[2]+20}px;`),be(t,"included-folder",s[3])},m(R,G){y(R,e,G),k(e,t),x(n,t,null),k(t,r),k(t,a),k(a,i),k(t,o),C&&C.m(t,null),k(t,c),k(t,l),k(e,h),A&&A.m(e,null),S=!0,M||(I=[Le(t,"click",s[6]),Le(t,"keyup",Pn("Enter",s[6]))],M=!0)},p(R,[G]){const le={};16&G&&(le.icon=R[4]),n.$set(le),(!S||1&G)&&Z!==(Z=R[0].name+"")&&fe(i,Z),R[0].type===_t.folder&&R[0].inclusionState!==nt.excluded&&typeof R[0].trackedFileCount=="number"?C?(C.p(R,G),1&G&&p(C,1)):(C=Rr(R),C.c(),p(C,1),C.m(t,c)):C&&(B(),m(C,1,1,()=>{C=null}),J()),(!S||1&G&&!hr(l.src,d=R[7][R[0].inclusionState]))&&_(l,"src",d),(!S||1&G&&u!==(u=R[8][R[0].inclusionState]))&&_(l,"alt",u),(!S||1&G&&g!==(g=R[0].reason))&&_(t,"title",g),(!S||1&G&&f!==(f=R[0].type===_t.folder&&R[0].expanded))&&_(t,"aria-expanded",f),(!S||4&G)&&_(t,"aria-level",R[2]),(!S||4&G&&$!==($=`padding-left: ${10*R[2]+20}px;`))&&_(t,"style",$),(!S||8&G)&&be(t,"included-folder",R[3]),R[5]?A?(A.p(R,G),32&G&&p(A,1)):(A=Or(R),A.c(),p(A,1),A.m(e,null)):A&&(B(),m(A,1,1,()=>{A=null}),J())},i(R){S||(p(n.$$.fragment,R),p(C),p(A),S=!0)},o(R){m(n.$$.fragment,R),m(C),m(A),S=!1},d(R){R&&v(e),b(n),C&&C.d(),A&&A.d(),M=!1,Ss(I)}}}function fl(s,e,t){let{data:n}=e,{wsContextModel:r}=e,{indentLevel:a}=e;const i={[nt.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[nt.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[nt.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},o={[nt.included]:"included",[nt.excluded]:"excluded",[nt.partial]:"partially included"};let c,l,d;return s.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,r=u.wsContextModel),"indentLevel"in u&&t(2,a=u.indentLevel)},s.$$.update=()=>{var u;1&s.$$.dirty&&t(4,l=(u=n).type===_t.folder&&u.inclusionState!==nt.excluded?u.expanded?"chevron-down":"chevron-right":u.type===_t.folder?"folder":"file"),1&s.$$.dirty&&t(3,c=n.type===_t.folder&&n.inclusionState!==nt.excluded),1&s.$$.dirty&&t(5,d=n.type===_t.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,r,a,c,l,d,()=>{r.toggleNode(n)},i,o]}class Fi extends $e{constructor(e){super(),ve(this,e,fl,ml,he,{data:0,wsContextModel:1,indentLevel:2})}}function jr(s,e,t){const n=s.slice();return n[3]=e[t],n}function Lr(s,e){let t,n,r;return n=new Fi({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1&i&&(o.wsContextModel=e[0]),2&i&&(o.data=e[3]),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function hl(s){let e,t,n=[],r=new Map,a=_e(s[1]);const i=o=>Tt.fileIdToString(o[3].fileId);for(let o=0;o<a.length;o+=1){let c=jr(s,a,o),l=i(c);r.set(l,n[o]=Lr(l,c))}return{c(){e=T("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","files-container svelte-8hfqhl")},m(o,c){y(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,[c]){3&c&&(a=_e(o[1]),B(),n=jt(n,c,i,1,o,a,r,e,Lt,Lr,null,jr),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function gl(s,e,t){let n,r=H,a=()=>(r(),r=ks(o,c=>t(2,n=c)),o);s.$$.on_destroy.push(()=>r());let i,{wsContextModel:o}=e;return a(),s.$$set=c=>{"wsContextModel"in c&&a(t(0,o=c.wsContextModel))},s.$$.update=()=>{4&s.$$.dirty&&t(1,i=n.sourceTree)},[o,i,n]}class $l extends $e{constructor(e){super(),ve(this,e,gl,hl,he,{wsContextModel:0})}}function vl(s){let e,t,n;return{c(){e=et("svg"),t=et("rect"),n=et("path"),_(t,"width","16"),_(t,"height","16"),_(t,"transform","matrix(-1 0 0 -1 16 16)"),_(t,"fill","currentColor"),_(t,"fill-opacity","0.01"),_(n,"fill-rule","evenodd"),_(n,"clip-rule","evenodd"),_(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),_(n,"fill","currentColor"),_(e,"width","15"),_(e,"height","15"),_(e,"viewBox","0 0 16 16"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){y(r,e,a),k(e,t),k(e,n)},p:H,i:H,o:H,d(r){r&&v(e)}}}class yl extends $e{constructor(e){super(),ve(this,e,null,vl,he,{})}}const _l=s=>({}),Fr=s=>({}),wl=s=>({}),zr=s=>({});function xl(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],zr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Re(n,t,r,r[10],e?Pe(t,r[10],a,wl):Oe(r[10]),zr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function bl(s){let e,t,n,r=s[0]&&Dr(s),a=s[1]&&Ur(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Se()},m(i,o){r&&r.m(i,o),y(i,e,o),a&&a.m(i,o),y(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Dr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(B(),m(r,1,1,()=>{r=null}),J()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=Ur(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(v(e),v(t)),r&&r.d(i),a&&a.d(i)}}}function Dr(s){let e,t,n;var r=s[0];return r&&(t=At(r,{})),{c(){e=T("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){y(a,e,i),t&&x(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){B();const o=t;m(o.$$.fragment,1,0,()=>{b(o,1)}),J()}r?(t=At(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&v(e),t&&b(t)}}}function Ur(s){let e,t;return e=new ie({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Sl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Sl(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&fe(e,t[1])},d(t){t&&v(e)}}}function Vr(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=T("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Re(r,n,a,a[10],t?Pe(n,a[10],i,null):Oe(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function kl(s){let e,t,n,r,a,i,o,c,l,d,u;const g=[bl,xl],f=[];function $(C,A){return C[0]||C[1]?0:1}r=$(s),a=f[r]=g[r](s);const h=s[8]["header-right"],S=Ie(h,s,s[10],Fr);let M=s[5].default&&Vr(s),I=[{role:"button"},{class:s[3]},s[4]],Z={};for(let C=0;C<I.length;C+=1)Z=Fe(Z,I[C]);return{c(){e=T("div"),t=T("div"),n=T("div"),a.c(),i=E(),o=T("div"),S&&S.c(),c=E(),M&&M.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),ss(e,Z),be(e,"clickable",s[2]),be(e,"svelte-13uht7n",!0)},m(C,A){y(C,e,A),k(e,t),k(t,n),f[r].m(n,null),k(t,i),k(t,o),S&&S.m(o,null),k(e,c),M&&M.m(e,null),l=!0,d||(u=Le(e,"click",s[9]),d=!0)},p(C,[A]){let R=r;r=$(C),r===R?f[r].p(C,A):(B(),m(f[R],1,1,()=>{f[R]=null}),J(),a=f[r],a?a.p(C,A):(a=f[r]=g[r](C),a.c()),p(a,1),a.m(n,null)),S&&S.p&&(!l||1024&A)&&Re(S,h,C,C[10],l?Pe(h,C[10],A,_l):Oe(C[10]),Fr),C[5].default?M?(M.p(C,A),32&A&&p(M,1)):(M=Vr(C),M.c(),p(M,1),M.m(e,null)):M&&(B(),m(M,1,1,()=>{M=null}),J()),ss(e,Z=Vt(I,[{role:"button"},(!l||8&A)&&{class:C[3]},16&A&&C[4]])),be(e,"clickable",C[2]),be(e,"svelte-13uht7n",!0)},i(C){l||(p(a),p(S,C),p(M),l=!0)},o(C){m(a),m(S,C),m(M),l=!1},d(C){C&&v(e),f[r].d(),S&&S.d(C),M&&M.d(),d=!1,u()}}}function Cl(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=rs(e,i),{$$slots:c={},$$scope:l}=e;const d=Qs(c);let{class:u=""}=e,{icon:g}=e,{title:f}=e,{isClickable:$=!1}=e;return s.$$set=h=>{e=Fe(Fe({},e),Mt(h)),t(11,o=rs(e,i)),"class"in h&&t(6,u=h.class),"icon"in h&&t(0,g=h.icon),"title"in h&&t(1,f=h.title),"isClickable"in h&&t(2,$=h.isClickable),"$$scope"in h&&t(10,l=h.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[g,f,$,a,r,d,u,n,c,function(h){as.call(this,s,h)},l]}let zi=class extends $e{constructor(s){super(),ve(this,s,Cl,kl,he,{class:6,icon:0,title:1,isClickable:2})}};function Tl(s){let e;return{c(){e=L("SOURCE FOLDERS")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ml(s){let e;return{c(){e=L("FILES")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Al(s){let e,t=s[2].toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){4&r&&t!==(t=n[2].toLocaleString()+"")&&fe(e,t)},d(n){n&&v(e)}}}function Nl(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$;return n=new ie({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Tl]},$$scope:{ctx:s}}}),a=new ul({props:{folders:s[0],onRemove:s[7],onAddMore:s[8]}}),l=new ie({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Ml]},$$scope:{ctx:s}}}),u=new ie({props:{size:1,class:"file-count",$$slots:{default:[Al]},$$scope:{ctx:s}}}),f=new $l({props:{wsContextModel:s[3]}}),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),w(a.$$.fragment),i=E(),o=T("div"),c=T("div"),w(l.$$.fragment),d=E(),w(u.$$.fragment),g=E(),w(f.$$.fragment),_(c,"class","files-header svelte-qsnirf"),_(e,"class","context-list svelte-qsnirf")},m(h,S){y(h,e,S),k(e,t),x(n,t,null),k(t,r),x(a,t,null),k(e,i),k(e,o),k(o,c),x(l,c,null),k(c,d),x(u,c,null),k(o,g),x(f,o,null),$=!0},p(h,S){const M={};512&S&&(M.$$scope={dirty:S,ctx:h}),n.$set(M);const I={};1&S&&(I.folders=h[0]),a.$set(I);const Z={};512&S&&(Z.$$scope={dirty:S,ctx:h}),l.$set(Z);const C={};516&S&&(C.$$scope={dirty:S,ctx:h}),u.$set(C)},i(h){$||(p(n.$$.fragment,h),p(a.$$.fragment,h),p(l.$$.fragment,h),p(u.$$.fragment,h),p(f.$$.fragment,h),$=!0)},o(h){m(n.$$.fragment,h),m(a.$$.fragment,h),m(l.$$.fragment,h),m(u.$$.fragment,h),m(f.$$.fragment,h),$=!1},d(h){h&&v(e),b(n),b(a),b(l),b(u),b(f)}}}function qr(s){let e,t;return e=new Ts({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[El]},$$scope:{ctx:s}}}),e.$on("click",s[5]),e.$on("keyup",Po("Enter",s[6])),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};512&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function El(s){let e,t;return e=new ko({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Zl(s){let e,t,n=s[1]===zs.done&&qr(s);return{c(){e=T("div"),n&&n.c(),_(e,"slot","header-right")},m(r,a){y(r,e,a),n&&n.m(e,null),t=!0},p(r,a){r[1]===zs.done?n?(n.p(r,a),2&a&&p(n,1)):(n=qr(r),n.c(),p(n,1),n.m(e,null)):n&&(B(),m(n,1,1,()=>{n=null}),J())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&v(e),n&&n.d()}}}function Il(s){let e,t,n,r;return e=new zi({props:{icon:yl,title:"Context",$$slots:{"header-right":[Zl],default:[Nl]},$$scope:{ctx:s}}}),e.$on("contextmenu",Rl),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"message",s[3].handleMessageFromExtension),n=!0)},p(a,[i]){const o={};519&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}const Rl=s=>s.preventDefault();function Ol(s,e,t){let n,r,a,i,o=new Tt(je,new Oo(je.postMessage));return lt(s,o,c=>t(4,r=c)),s.$$.update=()=>{16&s.$$.dirty&&t(0,a=r.sourceFolders.sort((c,l)=>c.isWorkspaceFolder!==l.isWorkspaceFolder?c.isWorkspaceFolder?-1:1:c.fileId.folderRoot.localeCompare(l.fileId.folderRoot))),16&s.$$.dirty&&t(1,i=r.syncStatus),1&s.$$.dirty&&t(2,n=a.reduce((c,l)=>c+(l.trackedFileCount??0),0))},[a,i,n,o,r,()=>o.requestRefresh(),()=>o.requestRefresh(),c=>o.removeSourceFolder(c),()=>o.addMoreSourceFolders()]}class Pl extends $e{constructor(e){super(),ve(this,e,Ol,Il,he,{})}}function Di(s){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(s)&&"name"in s}function Br(s){return Di(s)&&"component"in s}function jl(s){let e,t;return{c(){e=et("svg"),t=et("path"),_(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","15"),_(e,"viewBox","0 0 16 15"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),k(e,t)},p:H,i:H,o:H,d(n){n&&v(e)}}}class Ui extends $e{constructor(e){super(),ve(this,e,null,jl,he,{})}}const Ll=s=>({item:1&s}),Jr=s=>({item:s[0]}),Fl=s=>({}),Gr=s=>({});function Wr(s){var l;let e,t,n,r,a;e=new ie({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[zl]},$$scope:{ctx:s}}});let i=((l=s[0])==null?void 0:l.description)&&Hr(s);const o=s[1].content,c=Ie(o,s,s[2],Jr);return{c(){w(e.$$.fragment),t=E(),i&&i.c(),n=E(),r=T("div"),c&&c.c(),_(r,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){x(e,d,u),y(d,t,u),i&&i.m(d,u),y(d,n,u),y(d,r,u),c&&c.m(r,null),a=!0},p(d,u){var f;const g={};5&u&&(g.$$scope={dirty:u,ctx:d}),e.$set(g),(f=d[0])!=null&&f.description?i?(i.p(d,u),1&u&&p(i,1)):(i=Hr(d),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c&&c.p&&(!a||5&u)&&Re(c,o,d,d[2],a?Pe(o,d[2],u,Ll):Oe(d[2]),Jr)},i(d){a||(p(e.$$.fragment,d),p(i),p(c,d),a=!0)},o(d){m(e.$$.fragment,d),m(i),m(c,d),a=!1},d(d){d&&(v(t),v(n),v(r)),b(e,d),i&&i.d(d),c&&c.d(d)}}}function zl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.name)+"";return{c(){e=T("div"),t=L(n),_(e,"class","c-navigation__content-header svelte-z0ijuz")},m(a,i){y(a,e,i),k(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.name)+"")&&fe(t,n)},d(a){a&&v(e)}}}function Hr(s){let e,t;return e=new ie({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[Dl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};5&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Dl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.description)+"";return{c(){e=T("div"),t=L(n),_(e,"class","c-navigation__content-description svelte-z0ijuz")},m(a,i){y(a,e,i),k(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.description)+"")&&fe(t,n)},d(a){a&&v(e)}}}function Ul(s){let e,t,n,r,a;const i=s[1].header,o=Ie(i,s,s[2],Gr);let c=s[0]!=null&&Wr(s);return{c(){var l;e=T("div"),o&&o.c(),t=E(),n=T("div"),c&&c.c(),_(e,"class","c-navigation__content svelte-z0ijuz"),_(e,"id",r=(l=s[0])==null?void 0:l.id)},m(l,d){y(l,e,d),o&&o.m(e,null),k(e,t),k(e,n),c&&c.m(n,null),a=!0},p(l,[d]){var u;o&&o.p&&(!a||4&d)&&Re(o,i,l,l[2],a?Pe(i,l[2],d,Fl):Oe(l[2]),Gr),l[0]!=null?c?(c.p(l,d),1&d&&p(c,1)):(c=Wr(l),c.c(),p(c,1),c.m(n,null)):c&&(B(),m(c,1,1,()=>{c=null}),J()),(!a||1&d&&r!==(r=(u=l[0])==null?void 0:u.id))&&_(e,"id",r)},i(l){a||(p(o,l),p(c),a=!0)},o(l){m(o,l),m(c),a=!1},d(l){l&&v(e),o&&o.d(l),c&&c.d()}}}function Vl(s,e,t){let{$$slots:n={},$$scope:r}=e,{item:a}=e;return s.$$set=i=>{"item"in i&&t(0,a=i.item),"$$scope"in i&&t(2,r=i.$$scope)},[a,n,r]}class Vi extends $e{constructor(e){super(),ve(this,e,Vl,Ul,he,{item:0})}}function ql(s,e){let t;function n({scrollTo:r,delay:a,options:i}){clearTimeout(t),r&&(t=setTimeout(()=>{s.scrollIntoView(i)},a))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function Kr(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Yr(s,e,t){const n=s.slice();return n[22]=e[t],n}const Bl=s=>({item:32&s}),Xr=s=>({slot:"content",item:s[22]}),Jl=s=>({label:32&s,mode:4&s}),Qr=s=>({label:s[13],mode:s[2]}),Gl=s=>({item:1&s}),ea=s=>({item:s[0]});function ta(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function na(s,e,t){const n=s.slice();return n[17]=e[t],n}const Wl=s=>({label:32&s,mode:4&s}),sa=s=>({label:s[13],mode:s[2]}),Hl=s=>({item:1&s,selectedId:2&s}),ra=s=>({slot:"header",item:s[0],selectedId:s[1]}),Kl=s=>({item:1&s,isSelected:3&s}),aa=s=>{var e;return{slot:"content",item:s[0],isSelected:((e=s[0])==null?void 0:e.id)===s[1]}};function Yl(s){let e,t,n;const r=s[10].header,a=Ie(r,s,s[12],ea);let i=_e(s[5]),o=[];for(let l=0;l<i.length;l+=1)o[l]=oa(Kr(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=T("div"),a&&a.c(),t=E();for(let l=0;l<o.length;l+=1)o[l].c();_(e,"class","c-navigation__flat svelte-n5ccbo")},m(l,d){y(l,e,d),a&&a.m(e,null),k(e,t);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);n=!0},p(l,d){if(a&&a.p&&(!n||4097&d)&&Re(a,r,l,l[12],n?Pe(r,l[12],d,Gl):Oe(l[12]),ea),4134&d){let u;for(i=_e(l[5]),u=0;u<i.length;u+=1){const g=Kr(l,i,u);o[u]?(o[u].p(g,d),p(o[u],1)):(o[u]=oa(g),o[u].c(),p(o[u],1),o[u].m(e,null))}for(B(),u=i.length;u<o.length;u+=1)c(u);J()}},i(l){if(!n){p(a,l);for(let d=0;d<i.length;d+=1)p(o[d]);n=!0}},o(l){m(a,l),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);n=!1},d(l){l&&v(e),a&&a.d(l),Ht(o,l)}}}function Xl(s){let e,t;return e=new jo({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:s[3],minimized:!1,$$slots:{right:[id],left:[sd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.showButton=n[3]),4135&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ql(s){let e,t,n,r,a,i,o=s[13]+"";return t=new Ui({}),{c(){e=T("span"),w(t.$$.fragment),n=E(),r=T("span"),a=L(o),_(e,"class","c-navigation__head-icon")},m(c,l){y(c,e,l),x(t,e,null),y(c,n,l),y(c,r,l),k(r,a),i=!0},p(c,l){(!i||32&l)&&o!==(o=c[13]+"")&&fe(a,o)},i(c){i||(p(t.$$.fragment,c),i=!0)},o(c){m(t.$$.fragment,c),i=!1},d(c){c&&(v(e),v(n),v(r)),b(t)}}}function ed(s){let e;const t=s[10].content,n=Ie(t,s,s[12],Xr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4128&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,Bl):Oe(r[12]),Xr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function ia(s){let e,t,n,r,a,i,o;return t=new Vi({props:{item:s[22],$$slots:{content:[ed]},$$scope:{ctx:s}}}),{c(){e=T("span"),w(t.$$.fragment),n=E()},m(c,l){y(c,e,l),x(t,e,null),k(e,n),a=!0,i||(o=ao(r=ql.call(null,e,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(c,l){s=c;const d={};32&l&&(d.item=s[22]),4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),r&&Ut(r.update)&&38&l&&r.update.call(null,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})},i(c){a||(p(t.$$.fragment,c),a=!0)},o(c){m(t.$$.fragment,c),a=!1},d(c){c&&v(e),b(t),i=!1,o()}}}function oa(s){let e,t,n,r;const a=s[10].group,i=Ie(a,s,s[12],Qr),o=i||function(u){let g,f;return g=new ie({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[Ql]},$$scope:{ctx:u}}}),{c(){w(g.$$.fragment)},m($,h){x(g,$,h),f=!0},p($,h){const S={};4128&h&&(S.$$scope={dirty:h,ctx:$}),g.$set(S)},i($){f||(p(g.$$.fragment,$),f=!0)},o($){m(g.$$.fragment,$),f=!1},d($){b(g,$)}}}(s);let c=_e(s[14]),l=[];for(let u=0;u<c.length;u+=1)l[u]=ia(Yr(s,c,u));const d=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){e=T("div"),o&&o.c(),t=E();for(let u=0;u<l.length;u+=1)l[u].c();n=Se(),_(e,"class","c-navigation__head svelte-n5ccbo")},m(u,g){y(u,e,g),o&&o.m(e,null),y(u,t,g);for(let f=0;f<l.length;f+=1)l[f]&&l[f].m(u,g);y(u,n,g),r=!0},p(u,g){if(i?i.p&&(!r||4132&g)&&Re(i,a,u,u[12],r?Pe(a,u[12],g,Jl):Oe(u[12]),Qr):o&&o.p&&(!r||32&g)&&o.p(u,r?g:-1),4134&g){let f;for(c=_e(u[14]),f=0;f<c.length;f+=1){const $=Yr(u,c,f);l[f]?(l[f].p($,g),p(l[f],1)):(l[f]=ia($),l[f].c(),p(l[f],1),l[f].m(n.parentNode,n))}for(B(),f=c.length;f<l.length;f+=1)d(f);J()}},i(u){if(!r){p(o,u);for(let g=0;g<c.length;g+=1)p(l[g]);r=!0}},o(u){m(o,u),l=l.filter(Boolean);for(let g=0;g<l.length;g+=1)m(l[g]);r=!1},d(u){u&&(v(e),v(t),v(n)),o&&o.d(u),Ht(l,u)}}}function td(s){let e,t=s[13]+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){32&r&&t!==(t=n[13]+"")&&fe(e,t)},d(n){n&&v(e)}}}function nd(s){let e,t,n,r,a,i=s[17].name+"";var o=s[17].icon;return o&&(t=At(o,{})),{c(){e=T("span"),t&&w(t.$$.fragment),n=E(),r=L(i),_(e,"class","c-navigation__head-icon")},m(c,l){y(c,e,l),t&&x(t,e,null),y(c,n,l),y(c,r,l),a=!0},p(c,l){if(32&l&&o!==(o=c[17].icon)){if(t){B();const d=t;m(d.$$.fragment,1,0,()=>{b(d,1)}),J()}o?(t=At(o,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}(!a||32&l)&&i!==(i=c[17].name+"")&&fe(r,i)},i(c){a||(t&&p(t.$$.fragment,c),a=!0)},o(c){t&&m(t.$$.fragment,c),a=!1},d(c){c&&(v(e),v(n),v(r)),t&&b(t)}}}function ca(s){let e,t,n,r,a,i;function o(){return s[11](s[17])}return t=new ie({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[nd]},$$scope:{ctx:s}}}),{c(){e=T("button"),w(t.$$.fragment),n=E(),_(e,"class","c-navigation__item svelte-n5ccbo"),be(e,"is-active",s[17].id===s[1])},m(c,l){y(c,e,l),x(t,e,null),k(e,n),r=!0,a||(i=Le(e,"click",o),a=!0)},p(c,l){s=c;const d={};4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),(!r||34&l)&&be(e,"is-active",s[17].id===s[1])},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&v(e),b(t),a=!1,i()}}}function la(s){let e,t,n,r,a;const i=s[10].group,o=Ie(i,s,s[12],sa),c=o||function(g){let f,$,h,S,M;return $=new Ui({}),S=new ie({props:{size:2,color:"primary",$$slots:{default:[td]},$$scope:{ctx:g}}}),{c(){f=T("div"),w($.$$.fragment),h=E(),w(S.$$.fragment),_(f,"class","c-navigation__head svelte-n5ccbo")},m(I,Z){y(I,f,Z),x($,f,null),k(f,h),x(S,f,null),M=!0},p(I,Z){const C={};4128&Z&&(C.$$scope={dirty:Z,ctx:I}),S.$set(C)},i(I){M||(p($.$$.fragment,I),p(S.$$.fragment,I),M=!0)},o(I){m($.$$.fragment,I),m(S.$$.fragment,I),M=!1},d(I){I&&v(f),b($),b(S)}}}(s);let l=_e(s[14]),d=[];for(let g=0;g<l.length;g+=1)d[g]=ca(na(s,l,g));const u=g=>m(d[g],1,1,()=>{d[g]=null});return{c(){e=T("div"),c&&c.c(),t=E(),n=T("div");for(let g=0;g<d.length;g+=1)d[g].c();r=E(),_(n,"class","c-navigation__items svelte-n5ccbo"),_(e,"class","c-navigation__group")},m(g,f){y(g,e,f),c&&c.m(e,null),k(e,t),k(e,n);for(let $=0;$<d.length;$+=1)d[$]&&d[$].m(n,null);k(e,r),a=!0},p(g,f){if(o?o.p&&(!a||4132&f)&&Re(o,i,g,g[12],a?Pe(i,g[12],f,Wl):Oe(g[12]),sa):c&&c.p&&(!a||32&f)&&c.p(g,a?f:-1),98&f){let $;for(l=_e(g[14]),$=0;$<l.length;$+=1){const h=na(g,l,$);d[$]?(d[$].p(h,f),p(d[$],1)):(d[$]=ca(h),d[$].c(),p(d[$],1),d[$].m(n,null))}for(B(),$=l.length;$<d.length;$+=1)u($);J()}},i(g){if(!a){p(c,g);for(let f=0;f<l.length;f+=1)p(d[f]);a=!0}},o(g){m(c,g),d=d.filter(Boolean);for(let f=0;f<d.length;f+=1)m(d[f]);a=!1},d(g){g&&v(e),c&&c.d(g),Ht(d,g)}}}function da(s){let e,t,n=_e(s[5]),r=[];for(let i=0;i<n.length;i+=1)r[i]=la(ta(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){for(let i=0;i<r.length;i+=1)r[i].c();e=Se()},m(i,o){for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(i,o);y(i,e,o),t=!0},p(i,o){if(4198&o){let c;for(n=_e(i[5]),c=0;c<n.length;c+=1){const l=ta(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=la(l),r[c].c(),p(r[c],1),r[c].m(e.parentNode,e))}for(B(),c=n.length;c<r.length;c+=1)a(c);J()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&v(e),Ht(r,i)}}}function sd(s){let e,t,n=s[1],r=da(s);return{c(){e=T("nav"),r.c(),_(e,"class","c-navigation__nav svelte-n5ccbo"),_(e,"slot","left")},m(a,i){y(a,e,i),r.m(e,null),t=!0},p(a,i){2&i&&he(n,n=a[1])?(B(),m(r,1,1,H),J(),r=da(a),r.c(),p(r,1),r.m(e,null)):r.p(a,i)},i(a){t||(p(r),t=!0)},o(a){m(r),t=!1},d(a){a&&v(e),r.d(a)}}}function rd(s){let e;const t=s[10].header,n=Ie(t,s,s[12],ra);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4099&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,Hl):Oe(r[12]),ra)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function ua(s){let e,t,n;const r=[s[0].props];var a=s[0].component;function i(o,c){let l={};for(let d=0;d<r.length;d+=1)l=Fe(l,r[d]);return c!==void 0&&1&c&&(l=Fe(l,Vt(r,[gr(o[0].props)]))),{props:l}}return a&&(e=At(a,i(s))),{c(){e&&w(e.$$.fragment),t=Se()},m(o,c){e&&x(e,o,c),y(o,t,c),n=!0},p(o,c){if(1&c&&a!==(a=o[0].component)){if(e){B();const l=e;m(l.$$.fragment,1,0,()=>{b(l,1)}),J()}a?(e=At(a,i(o,c)),w(e.$$.fragment),p(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(a){const l=1&c?Vt(r,[gr(o[0].props)]):{};e.$set(l)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&b(e,o)}}}function ad(s){let e;const t=s[10].content,n=Ie(t,s,s[12],aa),r=n||function(a){let i,o,c=Br(a[0])&&pa(a[0],a[2],a[1]),l=c&&ua(a);return{c(){l&&l.c(),i=Se()},m(d,u){l&&l.m(d,u),y(d,i,u),o=!0},p(d,u){7&u&&(c=Br(d[0])&&pa(d[0],d[2],d[1])),c?l?(l.p(d,u),7&u&&p(l,1)):(l=ua(d),l.c(),p(l,1),l.m(i.parentNode,i)):l&&(B(),m(l,1,1,()=>{l=null}),J())},i(d){o||(p(l),o=!0)},o(d){m(l),o=!1},d(d){d&&v(i),l&&l.d(d)}}}(s);return{c(){r&&r.c()},m(a,i){r&&r.m(a,i),e=!0},p(a,i){n?n.p&&(!e||4099&i)&&Re(n,t,a,a[12],e?Pe(t,a[12],i,Kl):Oe(a[12]),aa):r&&r.p&&(!e||7&i)&&r.p(a,e?i:-1)},i(a){e||(p(r,a),e=!0)},o(a){m(r,a),e=!1},d(a){r&&r.d(a)}}}function id(s){let e,t;return e=new Vi({props:{item:s[0],slot:"right",$$slots:{content:[ad],header:[rd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1&r&&(a.item=n[0]),4103&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function od(s){let e,t,n,r,a;const i=[Xl,Yl],o=[];function c(l,d){return l[2]==="tree"?0:1}return t=c(s),n=o[t]=i[t](s),{c(){e=T("div"),n.c(),_(e,"class",r="c-navigation c-navigation--mode__"+s[2]+" "+s[4]+" svelte-n5ccbo")},m(l,d){y(l,e,d),o[t].m(e,null),a=!0},p(l,[d]){let u=t;t=c(l),t===u?o[t].p(l,d):(B(),m(o[u],1,1,()=>{o[u]=null}),J(),n=o[t],n?n.p(l,d):(n=o[t]=i[t](l),n.c()),p(n,1),n.m(e,null)),(!a||20&d&&r!==(r="c-navigation c-navigation--mode__"+l[2]+" "+l[4]+" svelte-n5ccbo"))&&_(e,"class",r)},i(l){a||(p(n),a=!0)},o(l){m(n),a=!1},d(l){l&&v(e),o[t].d()}}}function es(s,e,t,n,r,a){return{name:s,description:e,icon:t,id:n}}function pa(s,e,t){return e!=="tree"||(s==null?void 0:s.id)===t}function cd(s,e,t){let{$$slots:n={},$$scope:r}=e,{group:a="Workspace Settings"}=e,{items:i=[]}=e,{item:o}=e,{mode:c="tree"}=e,{selectedId:l}=e,{onNavigationChangeItem:d=h=>{}}=e,{showButton:u=!0}=e,{class:g=""}=e,f=new Map;function $(h){t(0,o=h),t(1,l=h==null?void 0:h.id)}return s.$$set=h=>{"group"in h&&t(7,a=h.group),"items"in h&&t(8,i=h.items),"item"in h&&t(0,o=h.item),"mode"in h&&t(2,c=h.mode),"selectedId"in h&&t(1,l=h.selectedId),"onNavigationChangeItem"in h&&t(9,d=h.onNavigationChangeItem),"showButton"in h&&t(3,u=h.showButton),"class"in h&&t(4,g=h.class),"$$scope"in h&&t(12,r=h.$$scope)},s.$$.update=()=>{259&s.$$.dirty&&(l?t(0,o=i.find(h=>(h==null?void 0:h.id)===l)):t(1,l=o==null?void 0:o.id)),384&s.$$.dirty&&t(5,f=i.reduce((h,S)=>{if(!S)return h;const M=S.group??a,I=h.get(M)??[];return I.push(S),h.set(M,I),h},new Map)),257&s.$$.dirty&&(o||t(0,o=i[0])),514&s.$$.dirty&&d(l)},[o,l,c,u,g,f,$,a,i,d,n,h=>$(h),r]}class ld extends $e{constructor(e){super(),ve(this,e,cd,od,he,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function dd(s){let e,t;return{c(){e=et("svg"),t=et("path"),_(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),k(e,t)},p:H,i:H,o:H,d(n){n&&v(e)}}}class ud extends $e{constructor(e){super(),ve(this,e,null,dd,he,{})}}function pd(s){let e,t;return{c(){e=et("svg"),t=et("path"),_(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),k(e,t)},p:H,i:H,o:H,d(n){n&&v(e)}}}class md extends $e{constructor(e){super(),ve(this,e,null,pd,he,{})}}const fd=s=>({}),ma=s=>({}),hd=s=>({}),fa=s=>({});function gd(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],fa);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Re(n,t,r,r[10],e?Pe(t,r[10],a,hd):Oe(r[10]),fa)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function $d(s){let e,t,n,r=s[0]&&ha(s),a=s[1]&&ga(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Se()},m(i,o){r&&r.m(i,o),y(i,e,o),a&&a.m(i,o),y(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=ha(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(B(),m(r,1,1,()=>{r=null}),J()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=ga(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(v(e),v(t)),r&&r.d(i),a&&a.d(i)}}}function ha(s){let e,t,n;var r=s[0];return r&&(t=At(r,{})),{c(){e=T("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){y(a,e,i),t&&x(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){B();const o=t;m(o.$$.fragment,1,0,()=>{b(o,1)}),J()}r?(t=At(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&v(e),t&&b(t)}}}function ga(s){let e,t;return e=new ie({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[vd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function vd(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&fe(e,t[1])},d(t){t&&v(e)}}}function $a(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=T("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Re(r,n,a,a[10],t?Pe(n,a[10],i,null):Oe(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function yd(s){let e,t,n,r,a,i,o,c,l,d,u;const g=[$d,gd],f=[];function $(C,A){return C[0]||C[1]?0:1}r=$(s),a=f[r]=g[r](s);const h=s[8]["header-right"],S=Ie(h,s,s[10],ma);let M=s[5].default&&$a(s),I=[{role:"button"},{class:s[3]},s[4]],Z={};for(let C=0;C<I.length;C+=1)Z=Fe(Z,I[C]);return{c(){e=T("div"),t=T("div"),n=T("div"),a.c(),i=E(),o=T("div"),S&&S.c(),c=E(),M&&M.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),ss(e,Z),be(e,"clickable",s[2]),be(e,"svelte-13uht7n",!0)},m(C,A){y(C,e,A),k(e,t),k(t,n),f[r].m(n,null),k(t,i),k(t,o),S&&S.m(o,null),k(e,c),M&&M.m(e,null),l=!0,d||(u=Le(e,"click",s[9]),d=!0)},p(C,[A]){let R=r;r=$(C),r===R?f[r].p(C,A):(B(),m(f[R],1,1,()=>{f[R]=null}),J(),a=f[r],a?a.p(C,A):(a=f[r]=g[r](C),a.c()),p(a,1),a.m(n,null)),S&&S.p&&(!l||1024&A)&&Re(S,h,C,C[10],l?Pe(h,C[10],A,fd):Oe(C[10]),ma),C[5].default?M?(M.p(C,A),32&A&&p(M,1)):(M=$a(C),M.c(),p(M,1),M.m(e,null)):M&&(B(),m(M,1,1,()=>{M=null}),J()),ss(e,Z=Vt(I,[{role:"button"},(!l||8&A)&&{class:C[3]},16&A&&C[4]])),be(e,"clickable",C[2]),be(e,"svelte-13uht7n",!0)},i(C){l||(p(a),p(S,C),p(M),l=!0)},o(C){m(a),m(S,C),m(M),l=!1},d(C){C&&v(e),f[r].d(),S&&S.d(C),M&&M.d(),d=!1,u()}}}function _d(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=rs(e,i),{$$slots:c={},$$scope:l}=e;const d=Qs(c);let{class:u=""}=e,{icon:g}=e,{title:f}=e,{isClickable:$=!1}=e;return s.$$set=h=>{e=Fe(Fe({},e),Mt(h)),t(11,o=rs(e,i)),"class"in h&&t(6,u=h.class),"icon"in h&&t(0,g=h.icon),"title"in h&&t(1,f=h.title),"isClickable"in h&&t(2,$=h.isClickable),"$$scope"in h&&t(10,l=h.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[g,f,$,a,r,d,u,n,c,function(h){as.call(this,s,h)},l]}class As extends $e{constructor(e){super(),ve(this,e,_d,yd,he,{class:6,icon:0,title:1,isClickable:2})}}function wd(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Fe(r,n[a]);return{c(){e=et("svg"),t=new er(!0),this.h()},l(a){e=tr(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=nr(e);t=sr(i,!0),i.forEach(v),this.h()},h(){t.a=null,nn(e,r)},m(a,i){rr(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(a,[i]){nn(e,r=Vt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&a[0]]))},i:H,o:H,d(a){a&&v(e)}}}function xd(s,e,t){return s.$$set=n=>{t(0,e=Fe(Fe({},e),Mt(n)))},[e=Mt(e)]}class bd extends $e{constructor(e){super(),ve(this,e,xd,wd,he,{})}}function Sd(s){let e,t,n,r,a,i,o,c;return a=new yn({props:{triggerOn:[Lo.Hover],content:"Revoke Access",$$slots:{default:[Td]},$$scope:{ctx:s}}}),o=new pi.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Md]},$$scope:{ctx:s}}}),{c(){e=T("div"),t=T("div"),n=T("div"),r=T("div"),w(a.$$.fragment),i=E(),w(o.$$.fragment),_(r,"class","icon-button-wrapper svelte-js5lik"),be(r,"active",s[3]),_(n,"class","connection-status svelte-js5lik"),_(t,"class","icon-container svelte-js5lik"),_(e,"class","status-controls svelte-js5lik")},m(l,d){y(l,e,d),k(e,t),k(t,n),k(n,r),x(a,r,null),k(t,i),x(o,t,null),c=!0},p(l,d){const u={};1027&d&&(u.$$scope={dirty:d,ctx:l}),a.$set(u),(!c||8&d)&&be(r,"active",l[3]);const g={};1024&d&&(g.$$scope={dirty:d,ctx:l}),o.$set(g)},i(l){c||(p(a.$$.fragment,l),p(o.$$.fragment,l),c=!0)},o(l){m(a.$$.fragment,l),m(o.$$.fragment,l),c=!1},d(l){l&&v(e),b(a),b(o)}}}function kd(s){let e,t;return e=new Me({props:{variant:"ghost-block",color:s[2]?"neutral":"accent",size:1,$$slots:{default:[Ed]},$$scope:{ctx:s}}}),e.$on("click",s[4]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};4&r&&(a.color=n[2]?"neutral":"accent"),1028&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Cd(s){let e,t;return e=new bd({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Td(s){let e,t;return e=new Ts({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Cd]},$$scope:{ctx:s}}}),e.$on("click",s[7]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1024&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Md(s){let e;return{c(){e=L("Connected")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ad(s){let e;return{c(){e=T("span"),e.textContent="Connect"},m(t,n){y(t,e,n)},i:H,o:H,d(t){t&&v(e)}}}function Nd(s){let e,t,n,r,a;return t=new li({props:{size:1,useCurrentColor:!0}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),r=T("span"),r.textContent="Cancel",_(e,"class","connect-button-spinner svelte-js5lik")},m(i,o){y(i,e,o),x(t,e,null),y(i,n,o),y(i,r,o),a=!0},i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&(v(e),v(n),v(r)),b(t)}}}function Ed(s){let e,t,n,r;const a=[Nd,Ad],i=[];function o(c,l){return c[2]?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=T("div"),n.c(),_(e,"class","connect-button-content svelte-js5lik")},m(c,l){y(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t!==d&&(B(),m(i[d],1,1,()=>{i[d]=null}),J(),n=i[t],n||(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),i[t].d()}}}function Zd(s){let e,t,n,r;const a=[kd,Sd],i=[];function o(c,l){return!c[0].isConfigured&&c[0].authUrl?0:c[0].isConfigured?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=T("div"),n&&n.c(),_(e,"slot","header-right")},m(c,l){y(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(B(),m(i[d],1,1,()=>{i[d]=null}),J()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),~t&&i[t].d()}}}function va(s){let e,t,n,r=s[0].statusMessage+"";return{c(){e=T("div"),t=L(r),_(e,"class",n="status-message "+s[0].statusType+" svelte-js5lik")},m(a,i){y(a,e,i),k(e,t)},p(a,i){1&i&&r!==(r=a[0].statusMessage+"")&&fe(t,r),1&i&&n!==(n="status-message "+a[0].statusType+" svelte-js5lik")&&_(e,"class",n)},d(a){a&&v(e)}}}function Id(s){let e,t,n,r,a,i;t=new As({props:{icon:s[0].icon,title:s[0].displayName,$$slots:{"header-right":[Zd]},$$scope:{ctx:s}}});let o=s[0].showStatus&&va(s);return{c(){e=T("div"),w(t.$$.fragment),n=E(),o&&o.c(),_(e,"class","config-wrapper"),_(e,"role","group"),_(e,"aria-label","Connection status controls")},m(c,l){y(c,e,l),x(t,e,null),k(e,n),o&&o.m(e,null),r=!0,a||(i=[Le(e,"mouseenter",s[8]),Le(e,"mouseleave",s[9])],a=!0)},p(c,[l]){const d={};1&l&&(d.icon=c[0].icon),1&l&&(d.title=c[0].displayName),1039&l&&(d.$$scope={dirty:l,ctx:c}),t.$set(d),c[0].showStatus?o?o.p(c,l):(o=va(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&v(e),b(t),o&&o.d(),a=!1,Ss(i)}}}function Rd(s,e,t){let{config:n}=e,{onAuthenticate:r}=e,{onRevokeAccess:a}=e,i=!1,o=null,c=!1;return s.$$set=l=>{"config"in l&&t(0,n=l.config),"onAuthenticate"in l&&t(5,r=l.onAuthenticate),"onRevokeAccess"in l&&t(1,a=l.onRevokeAccess)},s.$$.update=()=>{69&s.$$.dirty&&n.isConfigured&&i&&(t(2,i=!1),o&&(clearTimeout(o),t(6,o=null)))},[n,a,i,c,function(){if(i)t(2,i=!1),o&&(clearTimeout(o),t(6,o=null));else{t(2,i=!0);const l=n.authUrl||"";r(l),t(6,o=setTimeout(()=>{t(2,i=!1),t(6,o=null)},6e4))}},r,o,()=>a(n),()=>t(3,c=!0),()=>t(3,c=!1)]}class Od extends $e{constructor(e){super(),ve(this,e,Rd,Id,he,{config:0,onAuthenticate:5,onRevokeAccess:1})}}function Pd(s){let e;return{c(){e=L(s[0])},m(t,n){y(t,e,n)},p(t,n){1&n&&fe(e,t[0])},d(t){t&&v(e)}}}function jd(s){let e,t;const n=s[2].default,r=Ie(n,s,s[3],null);return{c(){e=T("div"),r&&r.c(),_(e,"class","category-content")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||8&i)&&Re(r,n,a,a[3],t?Pe(n,a[3],i,null):Oe(a[3]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function Ld(s){let e,t,n,r,a;return t=new li({props:{size:1}}),r=new ie({props:{size:1,color:"secondary",$$slots:{default:[Fd]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"class","loading-container svelte-2bsejd")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};8&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Fd(s){let e;return{c(){e=L("Loading...")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function zd(s){let e,t,n,r,a,i,o;n=new ie({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[Pd]},$$scope:{ctx:s}}});const c=[Ld,jd],l=[];function d(u,g){return u[1]?0:1}return a=d(s),i=l[a]=c[a](s),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),i.c(),_(t,"class","category-heading"),_(e,"class","category")},m(u,g){y(u,e,g),k(e,t),x(n,t,null),k(e,r),l[a].m(e,null),o=!0},p(u,[g]){const f={};9&g&&(f.$$scope={dirty:g,ctx:u}),n.$set(f);let $=a;a=d(u),a===$?l[a].p(u,g):(B(),m(l[$],1,1,()=>{l[$]=null}),J(),i=l[a],i?i.p(u,g):(i=l[a]=c[a](u),i.c()),p(i,1),i.m(e,null))},i(u){o||(p(n.$$.fragment,u),p(i),o=!0)},o(u){m(n.$$.fragment,u),m(i),o=!1},d(u){u&&v(e),b(n),l[a].d()}}}function Dd(s,e,t){let{$$slots:n={},$$scope:r}=e,{title:a}=e,{loading:i=!1}=e;return s.$$set=o=>{"title"in o&&t(0,a=o.title),"loading"in o&&t(1,i=o.loading),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,n,r]}class Ud extends $e{constructor(e){super(),ve(this,e,Dd,zd,he,{title:0,loading:1})}}function ya(s,e,t){const n=s.slice();return n[4]=e[t],n}function _a(s){let e,t;return e=new Od({props:{config:s[4],onAuthenticate:s[2],onRevokeAccess:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.config=n[4]),4&r&&(a.onAuthenticate=n[2]),8&r&&(a.onRevokeAccess=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Vd(s){let e,t,n=_e(s[1]),r=[];for(let i=0;i<n.length;i+=1)r[i]=_a(ya(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){e=T("div");for(let i=0;i<r.length;i+=1)r[i].c();_(e,"class","tool-category-list svelte-on3wl5")},m(i,o){y(i,e,o);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);t=!0},p(i,o){if(14&o){let c;for(n=_e(i[1]),c=0;c<n.length;c+=1){const l=ya(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=_a(l),r[c].c(),p(r[c],1),r[c].m(e,null))}for(B(),c=n.length;c<r.length;c+=1)a(c);J()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&v(e),Ht(r,i)}}}function qd(s){let e,t;return e=new Ud({props:{title:s[0],loading:s[1].length===0,$$slots:{default:[Vd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.title=n[0]),2&r&&(a.loading=n[1].length===0),142&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Bd(s,e,t){let{title:n}=e,{tools:r=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:i}=e;return s.$$set=o=>{"title"in o&&t(0,n=o.title),"tools"in o&&t(1,r=o.tools),"onAuthenticate"in o&&t(2,a=o.onAuthenticate),"onRevokeAccess"in o&&t(3,i=o.onRevokeAccess)},[n,r,a,i]}class Jd extends $e{constructor(e){super(),ve(this,e,Bd,qd,he,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3})}}var me,Hs;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(me||(me={})),function(s){s.mergeShapes=(e,t)=>({...e,...t})}(Hs||(Hs={}));const q=me.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),yt=s=>{switch(typeof s){case"undefined":return q.undefined;case"string":return q.string;case"number":return isNaN(s)?q.nan:q.number;case"boolean":return q.boolean;case"function":return q.function;case"bigint":return q.bigint;case"symbol":return q.symbol;case"object":return Array.isArray(s)?q.array:s===null?q.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?q.promise:typeof Map<"u"&&s instanceof Map?q.map:typeof Set<"u"&&s instanceof Set?q.set:typeof Date<"u"&&s instanceof Date?q.date:q.object;default:return q.unknown}},P=me.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class We extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof We))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,me.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}We.create=s=>new We(s);const cn=(s,e)=>{let t;switch(s.code){case P.invalid_type:t=s.received===q.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case P.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,me.jsonStringifyReplacer)}`;break;case P.unrecognized_keys:t=`Unrecognized key(s) in object: ${me.joinValues(s.keys,", ")}`;break;case P.invalid_union:t="Invalid input";break;case P.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${me.joinValues(s.options)}`;break;case P.invalid_enum_value:t=`Invalid enum value. Expected ${me.joinValues(s.options)}, received '${s.received}'`;break;case P.invalid_arguments:t="Invalid function arguments";break;case P.invalid_return_type:t="Invalid function return type";break;case P.invalid_date:t="Invalid date";break;case P.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:me.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case P.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case P.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case P.custom:t="Invalid input";break;case P.invalid_intersection_types:t="Intersection results could not be merged";break;case P.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case P.not_finite:t="Number must be finite";break;default:t=e.defaultError,me.assertNever(s)}return{message:t}};let qi=cn;function gs(){return qi}const $s=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function D(s,e){const t=gs(),n=$s({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===cn?void 0:cn].filter(r=>!!r)});s.common.issues.push(n)}class qe{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return se;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return qe.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return se;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}}const se=Object.freeze({status:"aborted"}),vs=s=>({status:"dirty",value:s}),Je=s=>({status:"valid",value:s}),Ks=s=>s.status==="aborted",Ys=s=>s.status==="dirty",Jt=s=>s.status==="valid",jn=s=>typeof Promise<"u"&&s instanceof Promise;function ys(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function Bi(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var Y,$n,vn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(Y||(Y={}));class ft{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const wa=(s,e)=>{if(Jt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new We(s.common.issues);return this._error=t,this._error}}};function ae(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}class ce{get description(){return this._def.description}_getType(e){return yt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:yt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new qe,ctx:{common:e.parent.common,data:e.data,parsedType:yt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(jn(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:yt(e)},a=this._parseSync({data:e,path:r.path,parent:r});return wa(r,a)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:yt(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:r});return Jt(a)?{value:a.value}:{issues:r.common.issues}}catch(a){!((n=(t=a==null?void 0:a.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(a=>Jt(a)?{value:a.value}:{issues:r.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:yt(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(jn(r)?r:Promise.resolve(r));return wa(n,a)}refine(e,t){const n=r=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue({code:P.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new at({schema:this,typeName:te.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return pt.create(this,this._def)}nullable(){return Pt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ot.create(this)}promise(){return dn.create(this,this._def)}or(e){return Dn.create([this,e],this._def)}and(e){return Un.create(this,e,this._def)}transform(e){return new at({...ae(this._def),schema:this,typeName:te.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Jn({...ae(this._def),innerType:this,defaultValue:t,typeName:te.ZodDefault})}brand(){return new or({typeName:te.ZodBranded,type:this,...ae(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Gn({...ae(this._def),innerType:this,catchValue:t,typeName:te.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Kn.create(this,e)}readonly(){return Wn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Gd=/^c[^\s-]{8,}$/i,Wd=/^[0-9a-z]+$/,Hd=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Kd=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Yd=/^[a-z0-9_-]{21}$/i,Xd=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Qd=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,eu=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Ls;const tu=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,nu=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,su=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ru=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,au=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,iu=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ji="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ou=new RegExp(`^${Ji}$`);function Gi(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Wi(s){let e=`${Ji}T${Gi(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function cu(s,e){if(!Xd.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function lu(s,e){return!(e!=="v4"&&e||!nu.test(s))||!(e!=="v6"&&e||!ru.test(s))}class rt extends ce{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==q.string){const i=this._getOrReturnCtx(e);return D(i,{code:P.invalid_type,expected:q.string,received:i.parsedType}),se}const t=new qe;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),D(n,{code:P.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),D(n,{code:P.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?D(n,{code:P.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&D(n,{code:P.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")eu.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"email",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")Ls||(Ls=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Ls.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"emoji",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Kd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"uuid",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")Yd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"nanoid",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Gd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"cuid",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Wd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"cuid2",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Hd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"ulid",code:P.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),D(n,{validation:"url",code:P.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"regex",code:P.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Wi(i).test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?ou.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Gi(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:P.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Qd.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"duration",code:P.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!tu.test(r))&&(a!=="v6"&&a||!su.test(r))&&(n=this._getOrReturnCtx(e,n),D(n,{validation:"ip",code:P.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?cu(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"jwt",code:P.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?lu(e.data,i.version)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"cidr",code:P.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?au.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"base64",code:P.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?iu.test(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{validation:"base64url",code:P.invalid_string,message:i.message}),t.dirty()):me.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:P.invalid_string,...Y.errToObj(n)})}_addCheck(e){return new rt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Y.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Y.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Y.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Y.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Y.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Y.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Y.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Y.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Y.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...Y.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...Y.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Y.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...Y.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...Y.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...Y.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...Y.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...Y.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...Y.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...Y.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...Y.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...Y.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...Y.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...Y.errToObj(t)})}nonempty(e){return this.min(1,Y.errToObj(e))}trim(){return new rt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new rt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new rt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function du(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}rt.create=s=>{var e;return new rt({checks:[],typeName:te.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};class It extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==q.number){const r=this._getOrReturnCtx(e);return D(r,{code:P.invalid_type,expected:q.number,received:r.parsedType}),se}let t;const n=new qe;for(const r of this._def.checks)r.kind==="int"?me.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:P.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?du(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:P.not_finite,message:r.message}),n.dirty()):me.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Y.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Y.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Y.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Y.toString(t))}setLimit(e,t,n,r){return new It({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Y.toString(r)}]})}_addCheck(e){return new It({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Y.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Y.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Y.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Y.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Y.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Y.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:Y.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Y.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Y.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&me.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}It.create=s=>new It({checks:[],typeName:te.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});class Rt extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==q.bigint)return this._getInvalidInput(e);let t;const n=new qe;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),D(t,{code:P.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):me.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.bigint,received:t.parsedType}),se}gte(e,t){return this.setLimit("min",e,!0,Y.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Y.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Y.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Y.toString(t))}setLimit(e,t,n,r){return new Rt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Y.toString(r)}]})}_addCheck(e){return new Rt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Y.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Y.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Y.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Y.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Y.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Rt.create=s=>{var e;return new Rt({checks:[],typeName:te.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};class Ln extends ce{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==q.boolean){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.boolean,received:t.parsedType}),se}return Je(e.data)}}Ln.create=s=>new Ln({typeName:te.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});class Gt extends ce{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==q.date){const r=this._getOrReturnCtx(e);return D(r,{code:P.invalid_type,expected:q.date,received:r.parsedType}),se}if(isNaN(e.data.getTime()))return D(this._getOrReturnCtx(e),{code:P.invalid_date}),se;const t=new qe;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),D(n,{code:P.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),D(n,{code:P.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):me.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Gt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:Y.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:Y.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}Gt.create=s=>new Gt({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:te.ZodDate,...ae(s)});class _s extends ce{_parse(e){if(this._getType(e)!==q.symbol){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.symbol,received:t.parsedType}),se}return Je(e.data)}}_s.create=s=>new _s({typeName:te.ZodSymbol,...ae(s)});class Fn extends ce{_parse(e){if(this._getType(e)!==q.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.undefined,received:t.parsedType}),se}return Je(e.data)}}Fn.create=s=>new Fn({typeName:te.ZodUndefined,...ae(s)});class zn extends ce{_parse(e){if(this._getType(e)!==q.null){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.null,received:t.parsedType}),se}return Je(e.data)}}zn.create=s=>new zn({typeName:te.ZodNull,...ae(s)});class ln extends ce{constructor(){super(...arguments),this._any=!0}_parse(e){return Je(e.data)}}ln.create=s=>new ln({typeName:te.ZodAny,...ae(s)});class Dt extends ce{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Je(e.data)}}Dt.create=s=>new Dt({typeName:te.ZodUnknown,...ae(s)});class xt extends ce{_parse(e){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.never,received:t.parsedType}),se}}xt.create=s=>new xt({typeName:te.ZodNever,...ae(s)});class ws extends ce{_parse(e){if(this._getType(e)!==q.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.void,received:t.parsedType}),se}return Je(e.data)}}ws.create=s=>new ws({typeName:te.ZodVoid,...ae(s)});class ot extends ce{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==q.array)return D(t,{code:P.invalid_type,expected:q.array,received:t.parsedType}),se;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(D(t,{code:i?P.too_big:P.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(D(t,{code:P.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(D(t,{code:P.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ft(t,i,t.path,o)))).then(i=>qe.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ft(t,i,t.path,o)));return qe.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new ot({...this._def,minLength:{value:e,message:Y.toString(t)}})}max(e,t){return new ot({...this._def,maxLength:{value:e,message:Y.toString(t)}})}length(e,t){return new ot({...this._def,exactLength:{value:e,message:Y.toString(t)}})}nonempty(e){return this.min(1,e)}}function en(s){if(s instanceof Ce){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=pt.create(en(n))}return new Ce({...s._def,shape:()=>e})}return s instanceof ot?new ot({...s._def,type:en(s.element)}):s instanceof pt?pt.create(en(s.unwrap())):s instanceof Pt?Pt.create(en(s.unwrap())):s instanceof ht?ht.create(s.items.map(e=>en(e))):s}ot.create=(s,e)=>new ot({type:s,minLength:null,maxLength:null,exactLength:null,typeName:te.ZodArray,...ae(e)});class Ce extends ce{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=me.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==q.object){const c=this._getOrReturnCtx(e);return D(c,{code:P.invalid_type,expected:q.object,received:c.parsedType}),se}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof xt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new ft(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof xt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(D(n,{code:P.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new ft(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>qe.mergeObjectSync(t,c)):qe.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return Y.errToObj,new Ce({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=Y.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new Ce({...this._def,unknownKeys:"strip"})}passthrough(){return new Ce({...this._def,unknownKeys:"passthrough"})}extend(e){return new Ce({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Ce({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:te.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Ce({...this._def,catchall:e})}pick(e){const t={};return me.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new Ce({...this._def,shape:()=>t})}omit(e){const t={};return me.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new Ce({...this._def,shape:()=>t})}deepPartial(){return en(this)}partial(e){const t={};return me.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new Ce({...this._def,shape:()=>t})}required(e){const t={};return me.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof pt;)r=r._def.innerType;t[n]=r}}),new Ce({...this._def,shape:()=>t})}keyof(){return Hi(me.objectKeys(this.shape))}}Ce.create=(s,e)=>new Ce({shape:()=>s,unknownKeys:"strip",catchall:xt.create(),typeName:te.ZodObject,...ae(e)}),Ce.strictCreate=(s,e)=>new Ce({shape:()=>s,unknownKeys:"strict",catchall:xt.create(),typeName:te.ZodObject,...ae(e)}),Ce.lazycreate=(s,e)=>new Ce({shape:s,unknownKeys:"strip",catchall:xt.create(),typeName:te.ZodObject,...ae(e)});class Dn extends ce{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async r=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await r._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(r){for(const i of r)if(i.result.status==="valid")return i.result;for(const i of r)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const a=r.map(i=>new We(i.ctx.common.issues));return D(t,{code:P.invalid_union,unionErrors:a}),se});{let r;const a=[];for(const o of n){const c={...t,common:{...t.common,issues:[]},parent:null},l=o._parseSync({data:t.data,path:t.path,parent:c});if(l.status==="valid")return l;l.status!=="dirty"||r||(r={result:l,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(r)return t.common.issues.push(...r.ctx.common.issues),r.result;const i=a.map(o=>new We(o));return D(t,{code:P.invalid_union,unionErrors:i}),se}}get options(){return this._def.options}}Dn.create=(s,e)=>new Dn({options:s,typeName:te.ZodUnion,...ae(e)});const Ct=s=>s instanceof Vn?Ct(s.schema):s instanceof at?Ct(s.innerType()):s instanceof qn?[s.value]:s instanceof Ot?s.options:s instanceof Bn?me.objectValues(s.enum):s instanceof Jn?Ct(s._def.innerType):s instanceof Fn?[void 0]:s instanceof zn?[null]:s instanceof pt?[void 0,...Ct(s.unwrap())]:s instanceof Pt?[null,...Ct(s.unwrap())]:s instanceof or||s instanceof Wn?Ct(s.unwrap()):s instanceof Gn?Ct(s._def.innerType):[];class Ns extends ce{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.object)return D(t,{code:P.invalid_type,expected:q.object,received:t.parsedType}),se;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(D(t,{code:P.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),se)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=Ct(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Ns({typeName:te.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ae(n)})}}function Xs(s,e){const t=yt(s),n=yt(e);if(s===e)return{valid:!0,data:s};if(t===q.object&&n===q.object){const r=me.objectKeys(e),a=me.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Xs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===q.array&&n===q.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Xs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===q.date&&n===q.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Un extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(Ks(a)||Ks(i))return se;const o=Xs(a.value,i.value);return o.valid?((Ys(a)||Ys(i))&&t.dirty(),{status:t.value,value:o.data}):(D(n,{code:P.invalid_intersection_types}),se)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Un.create=(s,e,t)=>new Un({left:s,right:e,typeName:te.ZodIntersection,...ae(t)});class ht extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.array)return D(n,{code:P.invalid_type,expected:q.array,received:n.parsedType}),se;if(n.data.length<this._def.items.length)return D(n,{code:P.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),se;!this._def.rest&&n.data.length>this._def.items.length&&(D(n,{code:P.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new ft(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>qe.mergeArray(t,a)):qe.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ht({...this._def,rest:e})}}ht.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ht({items:s,typeName:te.ZodTuple,rest:null,...ae(e)})};class Es extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.object)return D(n,{code:P.invalid_type,expected:q.object,received:n.parsedType}),se;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ft(n,o,n.path,o)),value:i._parse(new ft(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?qe.mergeObjectAsync(t,r):qe.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Es(t instanceof ce?{keyType:e,valueType:t,typeName:te.ZodRecord,...ae(n)}:{keyType:rt.create(),valueType:e,typeName:te.ZodRecord,...ae(t)})}}class xs extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.map)return D(n,{code:P.invalid_type,expected:q.map,received:n.parsedType}),se;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],l)=>({key:r._parse(new ft(n,o,n.path,[l,"key"])),value:a._parse(new ft(n,c,n.path,[l,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,d=await c.value;if(l.status==="aborted"||d.status==="aborted")return se;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const l=c.key,d=c.value;if(l.status==="aborted"||d.status==="aborted")return se;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}}}}xs.create=(s,e,t)=>new xs({valueType:e,keyType:s,typeName:te.ZodMap,...ae(t)});class Wt extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.set)return D(n,{code:P.invalid_type,expected:q.set,received:n.parsedType}),se;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(D(n,{code:P.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(D(n,{code:P.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return se;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new ft(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Wt({...this._def,minSize:{value:e,message:Y.toString(t)}})}max(e,t){return new Wt({...this._def,maxSize:{value:e,message:Y.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Wt.create=(s,e)=>new Wt({valueType:s,minSize:null,maxSize:null,typeName:te.ZodSet,...ae(e)});class tn extends ce{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.function)return D(t,{code:P.invalid_type,expected:q.function,received:t.parsedType}),se;function n(o,c){return $s({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,gs(),cn].filter(l=>!!l),issueData:{code:P.invalid_arguments,argumentsError:c}})}function r(o,c){return $s({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,gs(),cn].filter(l=>!!l),issueData:{code:P.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof dn){const o=this;return Je(async function(...c){const l=new We([]),d=await o._def.args.parseAsync(c,a).catch(g=>{throw l.addIssue(n(c,g)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(g=>{throw l.addIssue(r(u,g)),l})})}{const o=this;return Je(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new We([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new We([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new tn({...this._def,args:ht.create(e).rest(Dt.create())})}returns(e){return new tn({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new tn({args:e||ht.create([]).rest(Dt.create()),returns:t||Dt.create(),typeName:te.ZodFunction,...ae(n)})}}class Vn extends ce{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Vn.create=(s,e)=>new Vn({getter:s,typeName:te.ZodLazy,...ae(e)});class qn extends ce{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return D(t,{received:t.data,code:P.invalid_literal,expected:this._def.value}),se}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Hi(s,e){return new Ot({values:s,typeName:te.ZodEnum,...ae(e)})}qn.create=(s,e)=>new qn({value:s,typeName:te.ZodLiteral,...ae(e)});class Ot extends ce{constructor(){super(...arguments),$n.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return D(t,{expected:me.joinValues(n),received:t.parsedType,code:P.invalid_type}),se}if(ys(this,$n)||Bi(this,$n,new Set(this._def.values)),!ys(this,$n).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return D(t,{received:t.data,code:P.invalid_enum_value,options:n}),se}return Je(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Ot.create(e,{...this._def,...t})}exclude(e,t=this._def){return Ot.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}$n=new WeakMap,Ot.create=Hi;class Bn extends ce{constructor(){super(...arguments),vn.set(this,void 0)}_parse(e){const t=me.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==q.string&&n.parsedType!==q.number){const r=me.objectValues(t);return D(n,{expected:me.joinValues(r),received:n.parsedType,code:P.invalid_type}),se}if(ys(this,vn)||Bi(this,vn,new Set(me.getValidEnumValues(this._def.values))),!ys(this,vn).has(e.data)){const r=me.objectValues(t);return D(n,{received:n.data,code:P.invalid_enum_value,options:r}),se}return Je(e.data)}get enum(){return this._def.values}}vn=new WeakMap,Bn.create=(s,e)=>new Bn({values:s,typeName:te.ZodNativeEnum,...ae(e)});class dn extends ce{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.promise&&t.common.async===!1)return D(t,{code:P.invalid_type,expected:q.promise,received:t.parsedType}),se;const n=t.parsedType===q.promise?t.data:Promise.resolve(t.data);return Je(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}dn.create=(s,e)=>new dn({type:s,typeName:te.ZodPromise,...ae(e)});class at extends ce{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===te.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{D(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return se;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?se:c.status==="dirty"||t.value==="dirty"?vs(c.value):c});{if(t.value==="aborted")return se;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?se:o.status==="dirty"||t.value==="dirty"?vs(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?se:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?se:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Jt(i))return i;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Jt(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):i)}me.assertNever(r)}}at.create=(s,e,t)=>new at({schema:s,typeName:te.ZodEffects,effect:e,...ae(t)}),at.createWithPreprocess=(s,e,t)=>new at({schema:e,effect:{type:"preprocess",transform:s},typeName:te.ZodEffects,...ae(t)});class pt extends ce{_parse(e){return this._getType(e)===q.undefined?Je(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}pt.create=(s,e)=>new pt({innerType:s,typeName:te.ZodOptional,...ae(e)});class Pt extends ce{_parse(e){return this._getType(e)===q.null?Je(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Pt.create=(s,e)=>new Pt({innerType:s,typeName:te.ZodNullable,...ae(e)});class Jn extends ce{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===q.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Jn.create=(s,e)=>new Jn({innerType:s,typeName:te.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ae(e)});class Gn extends ce{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return jn(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new We(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new We(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Gn.create=(s,e)=>new Gn({innerType:s,typeName:te.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ae(e)});class bs extends ce{_parse(e){if(this._getType(e)!==q.nan){const t=this._getOrReturnCtx(e);return D(t,{code:P.invalid_type,expected:q.nan,received:t.parsedType}),se}return{status:"valid",value:e.data}}}bs.create=s=>new bs({typeName:te.ZodNaN,...ae(s)});const uu=Symbol("zod_brand");class or extends ce{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Kn extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?se:r.status==="dirty"?(t.dirty(),vs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?se:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Kn({in:e,out:t,typeName:te.ZodPipeline})}}class Wn extends ce{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Jt(r)&&(r.value=Object.freeze(r.value)),r);return jn(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}function xa(s,e={},t){return s?ln.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):ln.create()}Wn.create=(s,e)=>new Wn({innerType:s,typeName:te.ZodReadonly,...ae(e)});const pu={object:Ce.lazycreate};var te;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(te||(te={}));const ba=rt.create,Sa=It.create,mu=bs.create,fu=Rt.create,ka=Ln.create,hu=Gt.create,gu=_s.create,$u=Fn.create,vu=zn.create,yu=ln.create,_u=Dt.create,wu=xt.create,xu=ws.create,bu=ot.create,Su=Ce.create,ku=Ce.strictCreate,Cu=Dn.create,Tu=Ns.create,Mu=Un.create,Au=ht.create,Nu=Es.create,Eu=xs.create,Zu=Wt.create,Iu=tn.create,Ru=Vn.create,Ou=qn.create,Pu=Ot.create,ju=Bn.create,Lu=dn.create,Ca=at.create,Fu=pt.create,zu=Pt.create,Du=at.createWithPreprocess,Uu=Kn.create,Vu={string:s=>rt.create({...s,coerce:!0}),number:s=>It.create({...s,coerce:!0}),boolean:s=>Ln.create({...s,coerce:!0}),bigint:s=>Rt.create({...s,coerce:!0}),date:s=>Gt.create({...s,coerce:!0})},qu=se;var xe=Object.freeze({__proto__:null,defaultErrorMap:cn,setErrorMap:function(s){qi=s},getErrorMap:gs,makeIssue:$s,EMPTY_PATH:[],addIssueToContext:D,ParseStatus:qe,INVALID:se,DIRTY:vs,OK:Je,isAborted:Ks,isDirty:Ys,isValid:Jt,isAsync:jn,get util(){return me},get objectUtil(){return Hs},ZodParsedType:q,getParsedType:yt,ZodType:ce,datetimeRegex:Wi,ZodString:rt,ZodNumber:It,ZodBigInt:Rt,ZodBoolean:Ln,ZodDate:Gt,ZodSymbol:_s,ZodUndefined:Fn,ZodNull:zn,ZodAny:ln,ZodUnknown:Dt,ZodNever:xt,ZodVoid:ws,ZodArray:ot,ZodObject:Ce,ZodUnion:Dn,ZodDiscriminatedUnion:Ns,ZodIntersection:Un,ZodTuple:ht,ZodRecord:Es,ZodMap:xs,ZodSet:Wt,ZodFunction:tn,ZodLazy:Vn,ZodLiteral:qn,ZodEnum:Ot,ZodNativeEnum:Bn,ZodPromise:dn,ZodEffects:at,ZodTransformer:at,ZodOptional:pt,ZodNullable:Pt,ZodDefault:Jn,ZodCatch:Gn,ZodNaN:bs,BRAND:uu,ZodBranded:or,ZodPipeline:Kn,ZodReadonly:Wn,custom:xa,Schema:ce,ZodSchema:ce,late:pu,get ZodFirstPartyTypeKind(){return te},coerce:Vu,any:yu,array:bu,bigint:fu,boolean:ka,date:hu,discriminatedUnion:Tu,effect:Ca,enum:Pu,function:Iu,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>xa(t=>t instanceof s,e),intersection:Mu,lazy:Ru,literal:Ou,map:Eu,nan:mu,nativeEnum:ju,never:wu,null:vu,nullable:zu,number:Sa,object:Su,oboolean:()=>ka().optional(),onumber:()=>Sa().optional(),optional:Fu,ostring:()=>ba().optional(),pipeline:Uu,preprocess:Du,promise:Lu,record:Nu,set:Zu,strictObject:ku,string:ba,symbol:gu,transformer:Ca,tuple:Au,undefined:$u,union:Cu,unknown:_u,void:xu,NEVER:qu,ZodIssueCode:P,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:We});const Qe=xe.object({name:xe.string().optional(),title:xe.string().optional(),type:xe.enum(["stdio","http","sse"]).optional(),command:xe.string().optional(),args:xe.array(xe.union([xe.string(),xe.number(),xe.boolean()])).optional(),env:xe.record(xe.union([xe.string(),xe.number(),xe.boolean(),xe.null(),xe.undefined()])).optional(),url:xe.string().optional()}).passthrough(),Bu=xe.array(Qe),Ju=xe.object({servers:xe.array(Qe)}).passthrough(),Gu=xe.object({mcpServers:xe.array(Qe)}).passthrough(),Wu=xe.object({servers:xe.record(Qe)}).passthrough(),Hu=xe.object({mcpServers:xe.record(Qe)}).passthrough(),Ku=xe.record(Qe),Yu=Qe.refine(s=>s.command!==void 0||s.url!==void 0,{message:"Server must have either a 'command' or 'url' property"}),Ki=Symbol("MCPServerError");class Ue extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Ue.prototype)}}var ci;ci=Ki;class cr{constructor(e){ye(this,"servers",Ve([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===ge.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:ge.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:ge.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Ue("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const n=[...t,...e.map(r=>({...r,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=_n(this.servers).find(r=>r.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Ue(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(r=>r.id===e.id?e:r);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(r=>r.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(r=>r.id===e?{...r,disabled:!r.disabled}:r);return this.saveServers(n),n})}static convertServerToJSON(e){if(dt(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(a=>{var i;a.disabled?t.set(a.id,"MCP server has been manually disabled"):a.tools&&a.tools.length===0?t.set(a.id,"No tools are available for this MCP server"):a.disabledTools&&a.disabledTools.length===((i=a.tools)==null?void 0:i.length)?t.set(a.id,"All tools for this MCP server have validation errors: "+a.disabledTools.join(", ")):a.disabledTools&&a.disabledTools.length>0&&n.set(a.id,"MCP server has validation errors in the following tools which have been disabled: "+a.disabledTools.join(", "))});const r=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...r]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const r of e)t.has(r.name)||t.set(r.name,[]),t.get(r.name).push(r.id);const n=new Map;for(const[,r]of t)if(r.length>1)for(let a=1;a<r.length;a++)n.set(r[a],"MCP server is disabled due to duplicate server names");return n}parseServerConfigFromJSON(e){try{const t=JSON.parse(e),n=xe.union([Bu.transform(r=>r.map(a=>this.normalizeServerConfig(a))),Ju.transform(r=>r.servers.map(a=>this.normalizeServerConfig(a))),Gu.transform(r=>r.mcpServers.map(a=>this.normalizeServerConfig(a))),Wu.transform(r=>Object.entries(r.servers).map(([a,i])=>{const o=Qe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Hu.transform(r=>Object.entries(r.mcpServers).map(([a,i])=>{const o=Qe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Ku.transform(r=>{if(!Object.values(r).some(a=>{const i=Qe.safeParse(a);return i.success&&(i.data.command!==void 0||i.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(r).map(([a,i])=>{const o=Qe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})}),Yu.transform(r=>[this.normalizeServerConfig(r)])]).safeParse(t);if(n.success)return n.data;throw new Ue("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof Ue?t:new Ue("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(e){try{const t=this.parseServerConfigFromJSON(e),n=_n(this.servers),r=new Set(n.map(a=>a.name));for(const a of t){if(!a.name)throw new Ue("All servers must have a name.");if(r.has(a.name))throw new Ue(`A server with the name '${a.name}' already exists.`);r.add(a.name)}return this.servers.update(a=>{const i=[...a,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Ue?t:new Ue("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(e){try{const t=Qe.transform(n=>{let r;if(n.type)r=n.type;else if(n.url)r="http";else{if(!n.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");r="stdio"}if(r==="http"||r==="sse"){if(!n.url)throw new Error(`${r.toUpperCase()} server must have a 'url' property`);return{type:r,name:n.name||n.title||n.url,url:n.url}}{const a=n.command||"",i=n.args?n.args.map(d=>String(d)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const o=i.length>0?`${a} ${i.join(" ")}`:a,c=n.name||n.title||(a?a.split(" ")[0]:""),l=n.env?Object.fromEntries(Object.entries(n.env).filter(([d,u])=>u!=null).map(([d,u])=>[d,String(u)])):void 0;return{type:"stdio",name:c,command:o,arguments:"",useShellInterpolation:!0,env:Object.keys(l||{}).length>0?l:void 0}}}).refine(n=>!!n.name,{message:"Server must have a name",path:["name"]}).refine(n=>n.type==="http"||n.type==="sse"?!!n.url:!!n.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(e);if(!t.success)throw new Ue(t.error.message);return t.data}catch(t){throw t instanceof Error?new Ue(`Invalid server configuration: ${t.message}`):new Ue("Invalid server configuration")}}}ye(cr,ci,"MCPServerError");function Ta(s,e,t){const n=s.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function Xu(s){let e;return{c(){e=L("Environment Variables")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ma(s){let e,t,n=[],r=new Map,a=_e(s[0]);const i=o=>o[11].id;for(let o=0;o<a.length;o+=1){let c=Ta(s,a,o),l=i(c);r.set(l,n[o]=Aa(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Se()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){59&c&&(a=_e(o[0]),B(),n=jt(n,c,i,1,o,a,r,e.parentNode,Lt,Aa,e,Ta),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Qu(s){let e,t;return e=new mi({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ep(s){let e,t;return e=new Me({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[Qu]},$$scope:{ctx:s}}}),e.$on("focus",function(){Ut(s[1])&&s[1].apply(this,arguments)}),e.$on("click",function(){return s[10](s[11])}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){s=n;const a={};16384&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Aa(s,e){let t,n,r,a,i,o,c,l,d,u,g,f,$;function h(Z){e[6](Z,e[11])}let S={size:1,placeholder:"Name",class:"full-width"};function M(Z){e[8](Z,e[11])}e[11].key!==void 0&&(S.value=e[11].key),r=new Nt({props:S}),Ae.push(()=>Ne(r,"value",h)),r.$on("focus",function(){Ut(e[1])&&e[1].apply(this,arguments)}),r.$on("change",function(){return e[7](e[11])});let I={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(I.value=e[11].value),c=new Nt({props:I}),Ae.push(()=>Ne(c,"value",M)),c.$on("focus",function(){Ut(e[1])&&e[1].apply(this,arguments)}),c.$on("change",function(){return e[9](e[11])}),g=new yn({props:{content:"Remove",$$slots:{default:[ep]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=T("tr"),n=T("td"),w(r.$$.fragment),i=E(),o=T("td"),w(c.$$.fragment),d=E(),u=T("td"),w(g.$$.fragment),f=E(),_(n,"class","name-cell svelte-1mazg1z"),_(o,"class","value-cell svelte-1mazg1z"),_(u,"class","action-cell svelte-1mazg1z"),_(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(Z,C){y(Z,t,C),k(t,n),x(r,n,null),k(t,i),k(t,o),x(c,o,null),k(t,d),k(t,u),x(g,u,null),k(t,f),$=!0},p(Z,C){e=Z;const A={};!a&&1&C&&(a=!0,A.value=e[11].key,Ee(()=>a=!1)),r.$set(A);const R={};!l&&1&C&&(l=!0,R.value=e[11].value,Ee(()=>l=!1)),c.$set(R);const G={};16387&C&&(G.$$scope={dirty:C,ctx:e}),g.$set(G)},i(Z){$||(p(r.$$.fragment,Z),p(c.$$.fragment,Z),p(g.$$.fragment,Z),$=!0)},o(Z){m(r.$$.fragment,Z),m(c.$$.fragment,Z),m(g.$$.fragment,Z),$=!1},d(Z){Z&&v(t),b(r),b(c),b(g)}}}function tp(s){let e;return{c(){e=L("Variable")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function np(s){let e,t;return e=new un({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function sp(s){let e,t,n,r,a,i,o,c;e=new ie({props:{size:1,weight:"medium",$$slots:{default:[Xu]},$$scope:{ctx:s}}});let l=s[0].length>0&&Ma(s);return o=new Me({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[np],default:[tp]},$$scope:{ctx:s}}}),o.$on("click",s[2]),{c(){w(e.$$.fragment),t=E(),n=T("table"),r=T("tbody"),l&&l.c(),a=E(),i=T("div"),w(o.$$.fragment),_(n,"class","env-vars-table svelte-1mazg1z"),_(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){x(e,d,u),y(d,t,u),y(d,n,u),k(n,r),l&&l.m(r,null),y(d,a,u),y(d,i,u),x(o,i,null),c=!0},p(d,[u]){const g={};16384&u&&(g.$$scope={dirty:u,ctx:d}),e.$set(g),d[0].length>0?l?(l.p(d,u),1&u&&p(l,1)):(l=Ma(d),l.c(),p(l,1),l.m(r,null)):l&&(B(),m(l,1,1,()=>{l=null}),J());const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),o.$set(f)},i(d){c||(p(e.$$.fragment,d),p(l),p(o.$$.fragment,d),c=!0)},o(d){m(e.$$.fragment,d),m(l),m(o.$$.fragment,d),c=!1},d(d){d&&(v(t),v(n),v(a),v(i)),b(e,d),l&&l.d(),b(o)}}}function rp(s,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:r=[]}=e;function a(c){n(),t(0,r=r.filter(l=>l.id!==c))}function i(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].key=l,r),t(0,r))}function o(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].value=l,r),t(0,r))}return s.$$set=c=>{"handleEnterEditMode"in c&&t(1,n=c.handleEnterEditMode),"envVarEntries"in c&&t(0,r=c.envVarEntries)},[r,n,function(){n(),t(0,r=[...r,{id:crypto.randomUUID(),key:"",value:""}])},a,i,o,function(c,l){s.$$.not_equal(l.key,c)&&(l.key=c,t(0,r))},c=>i(c.id,c.key),function(c,l){s.$$.not_equal(l.value,c)&&(l.value=c,t(0,r))},c=>o(c.id,c.value),c=>a(c.id)]}class ap extends $e{constructor(e){super(),ve(this,e,rp,sp,he,{handleEnterEditMode:1,envVarEntries:0})}}function ip(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,S,M,I,Z,C,A,R,G,le,ke,W=(s[0]==="add"||s[0]==="edit")&&!dt(s[3]);i=new wo({}),c=new ie({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[cp]},$$scope:{ctx:s}}});const j=[dp,lp],Q=[];function N(F,de){return F[0]==="addJson"?0:F[0]==="add"||F[0]==="addHttp"||F[0]==="addSse"||F[0]==="edit"?1:-1}~(d=N(s))&&(u=Q[d]=j[d](s));let U=W&&Na(s);return S=new Ms({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[wp],default:[_p]},$$scope:{ctx:s}}}),Z=new Me({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[xp]},$$scope:{ctx:s}}}),Z.$on("click",s[19]),A=new Me({props:{size:1,variant:"solid",color:"accent",loading:s[2],type:"submit",disabled:s[15],$$slots:{default:[Mp]},$$scope:{ctx:s}}}),{c(){e=T("form"),t=T("div"),n=T("div"),r=T("div"),a=T("div"),w(i.$$.fragment),o=E(),w(c.$$.fragment),l=E(),u&&u.c(),g=E(),U&&U.c(),f=E(),$=T("div"),h=T("div"),w(S.$$.fragment),M=E(),I=T("div"),w(Z.$$.fragment),C=E(),w(A.$$.fragment),_(a,"class","server-icon svelte-nlsbjs"),_(r,"class","server-title svelte-nlsbjs"),_(n,"class","server-header svelte-nlsbjs"),_(h,"class","error-container svelte-nlsbjs"),be(h,"is-error",!!s[1]),_(I,"class","form-actions svelte-nlsbjs"),_($,"class","form-actions-row svelte-nlsbjs"),_(t,"class","server-edit-form svelte-nlsbjs"),_(e,"class",R="c-mcp-server-card "+(s[0]==="add"||s[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs")},m(F,de){y(F,e,de),k(e,t),k(t,n),k(n,r),k(r,a),x(i,a,null),k(r,o),x(c,r,null),k(t,l),~d&&Q[d].m(t,null),k(t,g),U&&U.m(t,null),k(t,f),k(t,$),k($,h),x(S,h,null),k($,M),k($,I),x(Z,I,null),k(I,C),x(A,I,null),G=!0,le||(ke=Le(e,"submit",io(s[18])),le=!0)},p(F,de){const ze={};16384&de[0]|128&de[1]&&(ze.$$scope={dirty:de,ctx:F}),c.$set(ze);let He=d;d=N(F),d===He?~d&&Q[d].p(F,de):(u&&(B(),m(Q[He],1,1,()=>{Q[He]=null}),J()),~d?(u=Q[d],u?u.p(F,de):(u=Q[d]=j[d](F),u.c()),p(u,1),u.m(t,g)):u=null),9&de[0]&&(W=(F[0]==="add"||F[0]==="edit")&&!dt(F[3])),W?U?(U.p(F,de),9&de[0]&&p(U,1)):(U=Na(F),U.c(),p(U,1),U.m(t,f)):U&&(B(),m(U,1,1,()=>{U=null}),J());const ct={};2&de[0]|128&de[1]&&(ct.$$scope={dirty:de,ctx:F}),S.$set(ct),(!G||2&de[0])&&be(h,"is-error",!!F[1]);const pn={};128&de[1]&&(pn.$$scope={dirty:de,ctx:F}),Z.$set(pn);const Ft={};4&de[0]&&(Ft.loading=F[2]),32768&de[0]&&(Ft.disabled=F[15]),1&de[0]|128&de[1]&&(Ft.$$scope={dirty:de,ctx:F}),A.$set(Ft),(!G||1&de[0]&&R!==(R="c-mcp-server-card "+(F[0]==="add"||F[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs"))&&_(e,"class",R)},i(F){G||(p(i.$$.fragment,F),p(c.$$.fragment,F),p(u),p(U),p(S.$$.fragment,F),p(Z.$$.fragment,F),p(A.$$.fragment,F),G=!0)},o(F){m(i.$$.fragment,F),m(c.$$.fragment,F),m(u),m(U),m(S.$$.fragment,F),m(Z.$$.fragment,F),m(A.$$.fragment,F),G=!1},d(F){F&&v(e),b(i),b(c),~d&&Q[d].d(),U&&U.d(),b(S),b(Z),b(A),le=!1,ke()}}}function op(s){let e,t;return e=new As({props:{$$slots:{"header-right":[Bp],"header-left":[Rp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8440&r[0]|128&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function cp(s){let e;return{c(){e=L(s[14])},m(t,n){y(t,e,n)},p(t,n){16384&n[0]&&fe(e,t[14])},d(t){t&&v(e)}}}function lp(s){let e,t,n,r,a,i,o,c,l;function d(h){s[33](h)}let u={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[pp]},$$scope:{ctx:s}};s[8]!==void 0&&(u.value=s[8]),n=new Nt({props:u}),Ae.push(()=>Ne(n,"value",d)),n.$on("focus",s[16]);const g=[fp,mp],f=[];function $(h,S){var M,I;return h[0]==="addHttp"||h[0]==="addSse"||((M=h[3])==null?void 0:M.type)==="http"||((I=h[3])==null?void 0:I.type)==="sse"?0:1}return i=$(s),o=f[i]=g[i](s),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),a=E(),o.c(),c=Se(),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs")},m(h,S){y(h,e,S),k(e,t),x(n,t,null),y(h,a,S),f[i].m(h,S),y(h,c,S),l=!0},p(h,S){const M={};128&S[1]&&(M.$$scope={dirty:S,ctx:h}),!r&&256&S[0]&&(r=!0,M.value=h[8],Ee(()=>r=!1)),n.$set(M);let I=i;i=$(h),i===I?f[i].p(h,S):(B(),m(f[I],1,1,()=>{f[I]=null}),J(),o=f[i],o?o.p(h,S):(o=f[i]=g[i](h),o.c()),p(o,1),o.m(c.parentNode,c))},i(h){l||(p(n.$$.fragment,h),p(o),l=!0)},o(h){m(n.$$.fragment,h),m(o),l=!1},d(h){h&&(v(e),v(a),v(c)),b(n),f[i].d(h)}}}function dp(s){let e,t,n,r,a,i,o,c,l;function d(g){s[32](g)}n=new ie({props:{size:1,weight:"medium",$$slots:{default:[yp]},$$scope:{ctx:s}}});let u={size:1,placeholder:"Paste JSON here..."};return s[11]!==void 0&&(u.value=s[11]),o=new yi({props:u}),Ae.push(()=>Ne(o,"value",d)),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),a=T("div"),i=T("div"),w(o.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs"),_(i,"class","input-field svelte-nlsbjs"),_(a,"class","form-row svelte-nlsbjs")},m(g,f){y(g,e,f),k(e,t),x(n,t,null),y(g,r,f),y(g,a,f),k(a,i),x(o,i,null),l=!0},p(g,f){const $={};128&f[1]&&($.$$scope={dirty:f,ctx:g}),n.$set($);const h={};!c&&2048&f[0]&&(c=!0,h.value=g[11],Ee(()=>c=!1)),o.$set(h)},i(g){l||(p(n.$$.fragment,g),p(o.$$.fragment,g),l=!0)},o(g){m(n.$$.fragment,g),m(o.$$.fragment,g),l=!1},d(g){g&&(v(e),v(r),v(a)),b(n),b(o)}}}function up(s){let e;return{c(){e=L("Name")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function pp(s){let e,t;return e=new ie({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[up]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};128&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function mp(s){let e,t,n,r,a;function i(c){s[35](c)}let o={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[gp]},$$scope:{ctx:s}};return s[9]!==void 0&&(o.value=s[9]),n=new Nt({props:o}),Ae.push(()=>Ne(n,"value",i)),n.$on("focus",s[16]),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs")},m(c,l){y(c,e,l),k(e,t),x(n,t,null),a=!0},p(c,l){const d={};128&l[1]&&(d.$$scope={dirty:l,ctx:c}),!r&&512&l[0]&&(r=!0,d.value=c[9],Ee(()=>r=!1)),n.$set(d)},i(c){a||(p(n.$$.fragment,c),a=!0)},o(c){m(n.$$.fragment,c),a=!1},d(c){c&&v(e),b(n)}}}function fp(s){let e,t,n,r,a;function i(c){s[34](c)}let o={size:1,placeholder:"Enter the HTTP URL (e.g., 'https://api.example.com/mcp')",$$slots:{label:[vp]},$$scope:{ctx:s}};return s[10]!==void 0&&(o.value=s[10]),n=new Nt({props:o}),Ae.push(()=>Ne(n,"value",i)),n.$on("focus",s[16]),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs")},m(c,l){y(c,e,l),k(e,t),x(n,t,null),a=!0},p(c,l){const d={};128&l[1]&&(d.$$scope={dirty:l,ctx:c}),!r&&1024&l[0]&&(r=!0,d.value=c[10],Ee(()=>r=!1)),n.$set(d)},i(c){a||(p(n.$$.fragment,c),a=!0)},o(c){m(n.$$.fragment,c),a=!1},d(c){c&&v(e),b(n)}}}function hp(s){let e;return{c(){e=L("Command")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gp(s){let e,t;return e=new ie({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[hp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};128&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function $p(s){let e;return{c(){e=L("URL")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function vp(s){let e,t;return e=new ie({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[$p]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};128&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function yp(s){let e;return{c(){e=L("Code Snippet")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Na(s){let e,t,n;function r(i){s[36](i)}let a={handleEnterEditMode:s[16]};return s[12]!==void 0&&(a.envVarEntries=s[12]),e=new ap({props:a}),Ae.push(()=>Ne(e,"envVarEntries",r)),{c(){w(e.$$.fragment)},m(i,o){x(e,i,o),n=!0},p(i,o){const c={};!t&&4096&o[0]&&(t=!0,c.envVarEntries=i[12],Ee(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){b(e,i)}}}function _p(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n[0]&&fe(e,t[1])},d(t){t&&v(e)}}}function wp(s){let e,t;return e=new Co({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function xp(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function bp(s){let e;return{c(){e=L("Save")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Sp(s){let e;return{c(){e=L("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function kp(s){let e;return{c(){e=L("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Cp(s){let e;return{c(){e=L("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Tp(s){let e;return{c(){e=L("Import")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Mp(s){let e;function t(a,i){return a[0]==="addJson"?Tp:a[0]==="add"?Cp:a[0]==="addHttp"?kp:a[0]==="addSse"?Sp:a[0]==="edit"?bp:void 0}let n=t(s),r=n&&n(s);return{c(){r&&r.c(),e=Se()},m(a,i){r&&r.m(a,i),y(a,e,i)},p(a,i){n!==(n=t(a))&&(r&&r.d(1),r=n&&n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&v(e),r&&r.d(a)}}}function Ap(s){let e;return{c(){e=T("div"),_(e,"class","c-dot svelte-nlsbjs"),be(e,"c-green",!s[6]),be(e,"c-warning",!s[6]&&!!s[7]),be(e,"c-red",!!s[6]),be(e,"c-disabled",s[3].disabled)},m(t,n){y(t,e,n)},p(t,n){64&n[0]&&be(e,"c-green",!t[6]),192&n[0]&&be(e,"c-warning",!t[6]&&!!t[7]),64&n[0]&&be(e,"c-red",!!t[6]),8&n[0]&&be(e,"c-disabled",t[3].disabled)},d(t){t&&v(e)}}}function Np(s){let e,t=s[3].name+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].name+"")&&fe(e,t)},d(n){n&&v(e)}}}function Ep(s){let e,t,n;return t=new ie({props:{size:1,weight:"medium",$$slots:{default:[Np]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","server-name svelte-nlsbjs")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8&a[0]|128&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Zp(s){let e,t=hs(s[3])+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){8&r[0]&&t!==(t=hs(n[3])+"")&&fe(e,t)},d(n){n&&v(e)}}}function Ip(s){let e,t,n;return t=new ie({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[Zp]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","command-text svelte-nlsbjs")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8&a[0]|128&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Rp(s){let e,t,n,r,a,i,o;return t=new yn({props:{content:s[6]||s[7],$$slots:{default:[Ap]},$$scope:{ctx:s}}}),r=new yn({props:{content:s[3].name,side:"top",align:"start",$$slots:{default:[Ep]},$$scope:{ctx:s}}}),i=new yn({props:{content:hs(s[3]),side:"top",align:"start",$$slots:{default:[Ip]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),a=E(),w(i.$$.fragment),_(e,"slot","header-left"),_(e,"class","l-header svelte-nlsbjs")},m(c,l){y(c,e,l),x(t,e,null),k(e,n),x(r,e,null),k(e,a),x(i,e,null),o=!0},p(c,l){const d={};192&l[0]&&(d.content=c[6]||c[7]),200&l[0]|128&l[1]&&(d.$$scope={dirty:l,ctx:c}),t.$set(d);const u={};8&l[0]&&(u.content=c[3].name),8&l[0]|128&l[1]&&(u.$$scope={dirty:l,ctx:c}),r.$set(u);const g={};8&l[0]&&(g.content=hs(c[3])),8&l[0]|128&l[1]&&(g.$$scope={dirty:l,ctx:c}),i.$set(g)},i(c){o||(p(t.$$.fragment,c),p(r.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(r.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(r),b(i)}}}function Op(s){let e,t;return e=new Fo({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Pp(s){let e,t;return e=new Ts({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Op]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};128&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function jp(s){let e;return{c(){e=L("Edit")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Lp(s){let e,t,n,r,a;return t=new zo({}),r=new ie({props:{size:1,weight:"medium",$$slots:{default:[jp]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};128&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Fp(s){let e;return{c(){e=L("Copy JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function zp(s){let e,t,n,r,a;return t=new Io({}),r=new ie({props:{size:1,weight:"medium",$$slots:{default:[Fp]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};128&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Dp(s){let e;return{c(){e=L("Delete")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Up(s){let e,t,n,r,a;return t=new mi({}),r=new ie({props:{size:1,weight:"medium",$$slots:{default:[Dp]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};128&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Vp(s){let e,t,n,r,a,i;return e=new Te.Item({props:{onSelect:s[16],$$slots:{default:[Lp]},$$scope:{ctx:s}}}),n=new Te.Item({props:{onSelect:s[29],$$slots:{default:[zp]},$$scope:{ctx:s}}}),a=new Te.Item({props:{color:"error",onSelect:s[30],$$slots:{default:[Up]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment),r=E(),w(a.$$.fragment)},m(o,c){x(e,o,c),y(o,t,c),x(n,o,c),y(o,r,c),x(a,o,c),i=!0},p(o,c){const l={};128&c[1]&&(l.$$scope={dirty:c,ctx:o}),e.$set(l);const d={};8192&c[0]&&(d.onSelect=o[29]),128&c[1]&&(d.$$scope={dirty:c,ctx:o}),n.$set(d);const u={};8216&c[0]&&(u.onSelect=o[30]),128&c[1]&&(u.$$scope={dirty:c,ctx:o}),a.$set(u)},i(o){i||(p(e.$$.fragment,o),p(n.$$.fragment,o),p(a.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(n.$$.fragment,o),m(a.$$.fragment,o),i=!1},d(o){o&&(v(t),v(r)),b(e,o),b(n,o),b(a,o)}}}function qp(s){let e,t,n,r;return e=new Te.Trigger({props:{$$slots:{default:[Pp]},$$scope:{ctx:s}}}),n=new Te.Content({props:{side:"bottom",align:"end",$$slots:{default:[Vp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};128&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};8216&i[0]|128&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Bp(s){let e,t,n,r,a,i,o;function c(d){s[31](d)}n=new oo({props:{size:1,checked:!s[3].disabled}}),n.$on("change",s[28]);let l={$$slots:{default:[qp]},$$scope:{ctx:s}};return s[13]!==void 0&&(l.requestClose=s[13]),a=new Te.Root({props:l}),Ae.push(()=>Ne(a,"requestClose",c)),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),w(a.$$.fragment),_(t,"class","status-controls svelte-nlsbjs"),_(e,"class","server-actions svelte-nlsbjs"),_(e,"slot","header-right")},m(d,u){y(d,e,u),k(e,t),x(n,t,null),k(t,r),x(a,t,null),o=!0},p(d,u){const g={};8&u[0]&&(g.checked=!d[3].disabled),n.$set(g);const f={};8216&u[0]|128&u[1]&&(f.$$scope={dirty:u,ctx:d}),!i&&8192&u[0]&&(i=!0,f.requestClose=d[13],Ee(()=>i=!1)),a.$set(f)},i(d){o||(p(n.$$.fragment,d),p(a.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(a.$$.fragment,d),o=!1},d(d){d&&v(e),b(n),b(a)}}}function Jp(s){let e,t,n,r;const a=[op,ip],i=[];function o(c,l){return c[0]==="view"&&c[3]?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Se()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function Gp({key:s,value:e}){return s.trim()&&e.trim()}function Wp(s,e,t){let n,r,a,i,{server:o=null}=e,{onDelete:c}=e,{onAdd:l}=e,{onSave:d}=e,{onEdit:u}=e,{onToggleDisableServer:g}=e,{onJSONImport:f}=e,{onCancel:$}=e,{disabledText:h}=e,{warningText:S}=e,{mode:M="view"}=e,{mcpServerError:I=""}=e,Z=(o==null?void 0:o.name)??"",C=dt(o)?"":Qt(o)?o.command:"",A=dt(o)?o.url:"",R=Qt(o)?o.env??{}:{},G="",le=[];function ke(){t(12,le=Object.entries(R).map(([N,U])=>({id:crypto.randomUUID(),key:N,value:U})))}ke();let W=()=>{},{busy:j=!1}=e;function Q(){if(o){const N=cr.convertServerToJSON(o);navigator.clipboard.writeText(N)}}return s.$$set=N=>{"server"in N&&t(3,o=N.server),"onDelete"in N&&t(4,c=N.onDelete),"onAdd"in N&&t(20,l=N.onAdd),"onSave"in N&&t(21,d=N.onSave),"onEdit"in N&&t(22,u=N.onEdit),"onToggleDisableServer"in N&&t(5,g=N.onToggleDisableServer),"onJSONImport"in N&&t(23,f=N.onJSONImport),"onCancel"in N&&t(24,$=N.onCancel),"disabledText"in N&&t(6,h=N.disabledText),"warningText"in N&&t(7,S=N.warningText),"mode"in N&&t(0,M=N.mode),"mcpServerError"in N&&t(1,I=N.mcpServerError),"busy"in N&&t(2,j=N.busy)},s.$$.update=()=>{768&s.$$.dirty[0]&&Z&&C&&t(1,I=""),1793&s.$$.dirty[0]&&t(27,n=!((M!=="add"||Z.trim()&&C.trim())&&(M!=="addHttp"&&M!=="addSse"||Z.trim()&&A.trim()))),2049&s.$$.dirty[0]&&t(26,r=M==="addJson"&&!G.trim()),201326593&s.$$.dirty[0]&&t(15,a=n||M==="view"||r),1&s.$$.dirty[0]&&t(14,i=(()=>{switch(M){case"add":return"New Stdio MCP Server";case"addHttp":return"New HTTP MCP Server";case"addSse":return"New SSE MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())},[M,I,j,o,c,g,h,S,Z,C,A,G,le,W,i,a,function(){o&&M==="view"&&(t(0,M="edit"),u(o),W())},Q,async function(){t(1,I=""),t(2,j=!0);const N=le.filter(Gp);R=Object.fromEntries(N.map(({key:F,value:de})=>[F.trim(),de.trim()])),ke();try{if(M==="add"){const F={type:"stdio",name:Z.trim(),command:C.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(R).length>0?R:void 0};await l(F)}else if(M==="addHttp"){const F={type:"http",name:Z.trim(),url:A.trim()};await l(F)}else if(M==="addSse"){const F={type:"sse",name:Z.trim(),url:A.trim()};await l(F)}else if(M==="addJson"){try{JSON.parse(G)}catch(F){const de=F instanceof Error?F.message:String(F);throw new Ue(`Invalid JSON format: ${de}`)}await f(G)}else if(M==="edit"&&o){if(dt(o)){const F={...o,name:Z.trim(),url:A.trim()};await d(F)}else if(Qt(o)){const F={...o,name:Z.trim(),command:C.trim(),arguments:"",env:Object.keys(R).length>0?R:void 0};await d(F)}}}catch(F){t(1,I=(U=F)!=null&&typeof U=="object"&&(U instanceof Ue||Ki in(U.constructor||U))?F.message:"Failed to save server"),console.warn(F)}finally{t(2,j=!1)}var U},function(){t(2,j=!1),t(1,I=""),$==null||$(),t(11,G=""),t(8,Z=(o==null?void 0:o.name)??""),t(9,C=dt(o)?"":Qt(o)?o.command:""),t(10,A=dt(o)?o.url:""),R=Qt(o)&&o.env?{...o.env}:{},ke()},l,d,u,f,$,ke,r,n,()=>{o&&g(o.id),W()},()=>{Q(),W()},()=>{c(o.id),W()},function(N){W=N,t(13,W)},function(N){G=N,t(11,G)},function(N){Z=N,t(8,Z)},function(N){A=N,t(10,A)},function(N){C=N,t(9,C)},function(N){le=N,t(12,le)}]}class Yi extends $e{constructor(e){super(),ve(this,e,Wp,Jp,he,{server:3,onDelete:4,onAdd:20,onSave:21,onEdit:22,onToggleDisableServer:5,onJSONImport:23,onCancel:24,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:25,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[25]}}function Ea(s,e,t){const n=s.slice();return n[20]=e[t],n}function Hp(s){let e,t,n;return t=new To({}),{c(){e=T("div"),w(t.$$.fragment),_(e,"slot","iconLeft"),_(e,"class","search-icon")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p:H,i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Kp(s){let e,t=s[20].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:H,d(n){n&&v(e)}}}function Yp(s){let e,t=s[20].description+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:H,d(n){n&&v(e)}}}function Za(s){let e,t,n,r,a,i,o,c,l,d;function u($){s[15]($)}function g($){s[16]($)}let f={placeholder:"Enter your API key...",size:1,variant:"surface",type:"password"};return s[4]!==void 0&&(f.value=s[4]),s[5]!==void 0&&(f.textInput=s[5]),t=new Nt({props:f}),Ae.push(()=>Ne(t,"value",u)),Ae.push(()=>Ne(t,"textInput",g)),t.$on("keydown",function(...$){return s[17](s[20],...$)}),o=new Me({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Xp]},$$scope:{ctx:s}}}),o.$on("click",function(){return s[18](s[20])}),l=new Me({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Qp]},$$scope:{ctx:s}}}),l.$on("click",s[10]),{c(){e=T("div"),w(t.$$.fragment),a=E(),i=T("div"),w(o.$$.fragment),c=E(),w(l.$$.fragment),_(i,"class","api-key-actions svelte-1y2bury"),_(e,"class","api-key-input-container svelte-1y2bury")},m($,h){y($,e,h),x(t,e,null),k(e,a),k(e,i),x(o,i,null),k(i,c),x(l,i,null),d=!0},p($,h){s=$;const S={};!n&&16&h&&(n=!0,S.value=s[4],Ee(()=>n=!1)),!r&&32&h&&(r=!0,S.textInput=s[5],Ee(()=>r=!1)),t.$set(S);const M={};8388608&h&&(M.$$scope={dirty:h,ctx:s}),o.$set(M);const I={};8388608&h&&(I.$$scope={dirty:h,ctx:s}),l.$set(I)},i($){d||(p(t.$$.fragment,$),p(o.$$.fragment,$),p(l.$$.fragment,$),d=!0)},o($){m(t.$$.fragment,$),m(o.$$.fragment,$),m(l.$$.fragment,$),d=!1},d($){$&&v(e),b(t),b(o),b(l)}}}function Xp(s){let e;return{c(){e=L("Install")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Qp(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function em(s){let e,t,n,r,a,i;n=new ie({props:{size:1,weight:"medium",$$slots:{default:[Kp]},$$scope:{ctx:s}}});let o=s[20].description&&function(l){let d,u;return d=new ie({props:{size:1,color:"secondary",$$slots:{default:[Yp]},$$scope:{ctx:l}}}),{c(){w(d.$$.fragment)},m(g,f){x(d,g,f),u=!0},p(g,f){const $={};8388608&f&&($.$$scope={dirty:f,ctx:g}),d.$set($)},i(g){u||(p(d.$$.fragment,g),u=!0)},o(g){m(d.$$.fragment,g),u=!1},d(g){b(d,g)}}}(s),c=s[3]===s[20].value&&Za(s);return{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),o&&o.c(),a=E(),c&&c.c(),_(t,"class","mcp-service-title svelte-1y2bury"),_(e,"slot","header-left"),_(e,"class","mcp-service-info svelte-1y2bury")},m(l,d){y(l,e,d),k(e,t),x(n,t,null),k(e,r),o&&o.m(e,null),k(e,a),c&&c.m(e,null),i=!0},p(l,d){const u={};8388608&d&&(u.$$scope={dirty:d,ctx:l}),n.$set(u),l[20].description&&o.p(l,d),l[3]===l[20].value?c?(c.p(l,d),8&d&&p(c,1)):(c=Za(l),c.c(),p(c,1),c.m(e,null)):c&&(B(),m(c,1,1,()=>{c=null}),J())},i(l){i||(p(n.$$.fragment,l),p(o),p(c),i=!0)},o(l){m(n.$$.fragment,l),m(o),m(c),i=!1},d(l){l&&v(e),b(n),o&&o.d(),c&&c.d()}}}function tm(s){let e,t;return e=new Me({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[sm]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[14](s[20])}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){s=n;const a={};8388608&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function nm(s){let e,t,n;return t=new pi.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[rm]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","installed-indicator svelte-1y2bury")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8388608&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function sm(s){let e;return{c(){e=T("span"),e.textContent="+"},m(t,n){y(t,e,n)},p:H,d(t){t&&v(e)}}}function rm(s){let e;return{c(){e=L("Installed")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function am(s){let e,t,n,r,a;function i(...d){return s[12](s[20],...d)}const o=[nm,tm],c=[];function l(d,u){return 1&u&&(t=null),t==null&&(t=!!d[0].some(i)),t?0:1}return n=l(s,-1),r=c[n]=o[n](s),{c(){e=T("div"),r.c(),_(e,"slot","header-right"),_(e,"class","mcp-service-actions svelte-1y2bury")},m(d,u){y(d,e,u),c[n].m(e,null),a=!0},p(d,u){let g=n;n=l(s=d,u),n===g?c[n].p(s,u):(B(),m(c[g],1,1,()=>{c[g]=null}),J(),r=c[n],r?r.p(s,u):(r=c[n]=o[n](s),r.c()),p(r,1),r.m(e,null))},i(d){a||(p(r),a=!0)},o(d){m(r),a=!1},d(d){d&&v(e),c[n].d()}}}function Ia(s){let e,t,n,r;return t=new As({props:{$$slots:{"header-right":[am],"header-left":[em]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),_(e,"class","mcp-service-item")},m(a,i){y(a,e,i),x(t,e,null),k(e,n),r=!0},p(a,i){const o={};8388665&i&&(o.$$scope={dirty:i,ctx:a}),t.$set(o)},i(a){r||(p(t.$$.fragment,a),r=!0)},o(a){m(t.$$.fragment,a),r=!1},d(a){a&&v(e),b(t)}}}function im(s){let e,t,n,r,a=s[6].length>9&&function(l){let d,u,g,f;function $(S){l[13](S)}let h={placeholder:"Search MCPs...",size:1,variant:"surface",$$slots:{iconLeft:[Hp]},$$scope:{ctx:l}};return l[2]!==void 0&&(h.value=l[2]),u=new Nt({props:h}),Ae.push(()=>Ne(u,"value",$)),u.$on("input",l[7]),{c(){d=T("div"),w(u.$$.fragment),_(d,"class","mcp-search-container")},m(S,M){y(S,d,M),x(u,d,null),f=!0},p(S,M){const I={};8388608&M&&(I.$$scope={dirty:M,ctx:S}),!g&&4&M&&(g=!0,I.value=S[2],Ee(()=>g=!1)),u.$set(I)},i(S){f||(p(u.$$.fragment,S),f=!0)},o(S){m(u.$$.fragment,S),f=!1},d(S){S&&v(d),b(u)}}}(s),i=_e(s[6]),o=[];for(let l=0;l<i.length;l+=1)o[l]=Ia(Ea(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=T("div"),a&&a.c(),t=E(),n=T("div");for(let l=0;l<o.length;l+=1)o[l].c();_(n,"class","mcp-list-container svelte-1y2bury"),_(e,"class","mcp-install-content svelte-1y2bury")},m(l,d){y(l,e,d),a&&a.m(e,null),k(e,t),k(e,n);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(n,null);r=!0},p(l,d){if(l[6].length>9&&a.p(l,d),1913&d){let u;for(i=_e(l[6]),u=0;u<i.length;u+=1){const g=Ea(l,i,u);o[u]?(o[u].p(g,d),p(o[u],1)):(o[u]=Ia(g),o[u].c(),p(o[u],1),o[u].m(n,null))}for(B(),u=i.length;u<o.length;u+=1)c(u);J()}},i(l){if(!r){p(a);for(let d=0;d<i.length;d+=1)p(o[d]);r=!0}},o(l){m(a),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);r=!1},d(l){l&&v(e),a&&a.d(),Ht(o,l)}}}function om(s){let e;return{c(){e=L("Easy MCP Installation")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function cm(s){let e,t,n,r,a;return t=new Uo({}),r=new ie({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[om]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"slot","header-left"),_(e,"class","mcp-install-left svelte-1y2bury")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};8388608&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function lm(s){let e,t,n;return t=new As({props:{$$slots:{"header-left":[cm]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),_(e,"slot","header"),_(e,"class","mcp-install-header svelte-1y2bury")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8388608&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function dm(s){let e,t,n,r;function a(o){s[19](o)}let i={$$slots:{header:[lm],default:[im]},$$scope:{ctx:s}};return s[1]!==void 0&&(i.collapsed=s[1]),t=new Do({props:i}),Ae.push(()=>Ne(t,"collapsed",a)),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","mcp-install-wrapper svelte-1y2bury")},m(o,c){y(o,e,c),x(t,e,null),r=!0},p(o,[c]){const l={};8388669&c&&(l.$$scope={dirty:c,ctx:o}),!n&&2&c&&(n=!0,l.collapsed=o[1],Ee(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&v(e),b(t)}}}function um(s,e){return s.value==="tavily"?`npx -y tavily-mcp@latest --TAVILY_API_KEY=${e}`:s.command}function pm(s,e){switch(s.value){case"tavily":return{};case"exa-search":return{EXA_API_KEY:e};default:return{API_KEY:e}}}function mm(s,e,t){let{onMCPServerAdd:n}=e,{servers:r=[]}=e,a,i=!0,o="",c=null,l="";async function d(f){try{if(r.some(h=>h.name===f.label))return;const $={type:"stdio",name:f.label,command:f.command,arguments:"",useShellInterpolation:!0,env:void 0};n&&n($)}catch($){console.error(`Failed to install ${f.label}:`,$)}}async function u(f){try{if(!l.trim())return void(a==null?void 0:a.focus());const $={type:"stdio",name:f.label,command:um(f,l.trim()),arguments:"",useShellInterpolation:!0,env:pm(f,l.trim())};n&&n($),t(3,c=null),t(4,l="")}catch($){console.error(`Failed to install ${f.label}:`,$)}}function g(){t(3,c=null),t(4,l="")}return s.$$set=f=>{"onMCPServerAdd"in f&&t(11,n=f.onMCPServerAdd),"servers"in f&&t(0,r=f.servers)},[r,i,o,c,l,a,[{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],function(f){const $=f.target;t(2,o=$.value)},d,u,g,n,(f,$)=>$.name===f.label,function(f){o=f,t(2,o)},f=>d(f),function(f){l=f,t(4,l)},function(f){a=f,t(5,a)},(f,$)=>{$.key==="Enter"?u(f):$.key==="Escape"&&g()},f=>u(f),function(f){i=f,t(1,i)}]}class fm extends $e{constructor(e){super(),ve(this,e,mm,dm,he,{onMCPServerAdd:11,servers:0})}}const hm={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},gm={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},$m=uo(),vm=new class{constructor(s){ye(this,"strings");let e={[_r.vscode]:{},[_r.jetbrains]:gm};this.strings={...hm,...e[s]}}get(s){return this.strings[s]}}($m.clientType);function Ra(s,e,t){const n=s.slice();return n[25]=e[t],n}function ym(s){let e;return{c(){e=T("div"),e.textContent="MCP",_(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:H,d(t){t&&v(e)}}}function Oa(s,e){let t,n,r;return n=new Yi({props:{mode:e[2]===e[25].id?"edit":"view",server:e[25],onAdd:e[8],onSave:e[9],onDelete:e[11],onToggleDisableServer:e[12],onEdit:e[7],onCancel:e[6],onJSONImport:e[10],disabledText:e[4].errors.get(e[25].id),warningText:e[4].warnings.get(e[25].id)}}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};5&i&&(o.mode=e[2]===e[25].id?"edit":"view"),1&i&&(o.server=e[25]),17&i&&(o.disabledText=e[4].errors.get(e[25].id)),17&i&&(o.warningText=e[4].warnings.get(e[25].id)),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function Pa(s){let e,t;return e=new Yi({props:{mode:s[3],onAdd:s[8],onSave:s[9],onDelete:s[11],onToggleDisableServer:s[12],onEdit:s[7],onCancel:s[6],onJSONImport:s[10]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.mode=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function _m(s){let e;return{c(){e=L("Add Stdio MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function wm(s){let e,t;return e=new un({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function xm(s){let e;return{c(){e=L("Add SSE MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function bm(s){let e,t;return e=new un({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Sm(s){let e;return{c(){e=L("Add HTTP MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function km(s){let e,t;return e=new un({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ja(s){let e,t;return e=new Me({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[Tm],default:[Cm]},$$scope:{ctx:s}}}),e.$on("click",s[23]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r&&(a.disabled=n[5]),268435456&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Cm(s){let e;return{c(){e=L("Import from JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Tm(s){let e,t;return e=new fi({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Mm(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,S,M,I,Z,C,A,R,G=[],le=new Map;n=new ie({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[ym]},$$scope:{ctx:s}}}),u=new fm({props:{onMCPServerAdd:s[8],servers:s[0]}});let ke=_e(s[0]);const W=N=>N[25].id;for(let N=0;N<ke.length;N+=1){let U=Ra(s,ke,N),F=W(U);le.set(F,G[N]=Oa(F,U))}let j=(s[3]==="add"||s[3]==="addJson"||s[3]==="addHttp"||s[3]==="addSse")&&Pa(s);S=new Me({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[wm],default:[_m]},$$scope:{ctx:s}}}),S.$on("click",s[20]),I=new Me({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[bm],default:[xm]},$$scope:{ctx:s}}}),I.$on("click",s[21]),C=new Me({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[km],default:[Sm]},$$scope:{ctx:s}}}),C.$on("click",s[22]);let Q=s[1]&&ja(s);return{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),a=T("div"),i=L(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),o=T("a"),c=L("in the docs"),l=L("."),d=E(),w(u.$$.fragment),g=E();for(let N=0;N<G.length;N+=1)G[N].c();f=E(),j&&j.c(),$=E(),h=T("div"),w(S.$$.fragment),M=E(),w(I.$$.fragment),Z=E(),w(C.$$.fragment),A=E(),Q&&Q.c(),_(t,"class","section-heading svelte-1vnq4q3"),_(o,"href",s[13]),_(a,"class","description-text svelte-1vnq4q3"),_(e,"class","mcp-servers svelte-1vnq4q3"),_(h,"class","add-mcp-button-container svelte-1vnq4q3")},m(N,U){y(N,e,U),k(e,t),x(n,t,null),k(e,r),k(e,a),k(a,i),k(a,o),k(o,c),k(a,l),k(e,d),x(u,e,null),k(e,g);for(let F=0;F<G.length;F+=1)G[F]&&G[F].m(e,null);y(N,f,U),j&&j.m(N,U),y(N,$,U),y(N,h,U),x(S,h,null),k(h,M),x(I,h,null),k(h,Z),x(C,h,null),k(h,A),Q&&Q.m(h,null),R=!0},p(N,[U]){const F={};268435456&U&&(F.$$scope={dirty:U,ctx:N}),n.$set(F);const de={};1&U&&(de.servers=N[0]),u.$set(de),8149&U&&(ke=_e(N[0]),B(),G=jt(G,U,W,1,N,ke,le,e,Lt,Oa,null,Ra),J()),N[3]==="add"||N[3]==="addJson"||N[3]==="addHttp"||N[3]==="addSse"?j?(j.p(N,U),8&U&&p(j,1)):(j=Pa(N),j.c(),p(j,1),j.m($.parentNode,$)):j&&(B(),m(j,1,1,()=>{j=null}),J());const ze={};32&U&&(ze.disabled=N[5]),268435456&U&&(ze.$$scope={dirty:U,ctx:N}),S.$set(ze);const He={};32&U&&(He.disabled=N[5]),268435456&U&&(He.$$scope={dirty:U,ctx:N}),I.$set(He);const ct={};32&U&&(ct.disabled=N[5]),268435456&U&&(ct.$$scope={dirty:U,ctx:N}),C.$set(ct),N[1]?Q?(Q.p(N,U),2&U&&p(Q,1)):(Q=ja(N),Q.c(),p(Q,1),Q.m(h,null)):Q&&(B(),m(Q,1,1,()=>{Q=null}),J())},i(N){if(!R){p(n.$$.fragment,N),p(u.$$.fragment,N);for(let U=0;U<ke.length;U+=1)p(G[U]);p(j),p(S.$$.fragment,N),p(I.$$.fragment,N),p(C.$$.fragment,N),p(Q),R=!0}},o(N){m(n.$$.fragment,N),m(u.$$.fragment,N);for(let U=0;U<G.length;U+=1)m(G[U]);m(j),m(S.$$.fragment,N),m(I.$$.fragment,N),m(C.$$.fragment,N),m(Q),R=!1},d(N){N&&(v(e),v(f),v($),v(h)),b(n),b(u);for(let U=0;U<G.length;U+=1)G[U].d();j&&j.d(N),b(S),b(I),b(C),Q&&Q.d()}}}function Am(s,e,t){let n,r,{servers:a}=e,{onMCPServerAdd:i}=e,{onMCPServerSave:o}=e,{onMCPServerDelete:c}=e,{onMCPServerToggleDisable:l}=e,{onCancel:d}=e,{onMCPServerJSONImport:u}=e,{isMCPImportEnabled:g=!0}=e,f=null,$=null;function h(R){return async function(...G){const le=await R(...G);return t(3,$=null),t(2,f=null),le}}const S=h(i),M=h(o),I=h(u),Z=h(c),C=h(l),A=vm.get("mcpDocsURL");return s.$$set=R=>{"servers"in R&&t(0,a=R.servers),"onMCPServerAdd"in R&&t(14,i=R.onMCPServerAdd),"onMCPServerSave"in R&&t(15,o=R.onMCPServerSave),"onMCPServerDelete"in R&&t(16,c=R.onMCPServerDelete),"onMCPServerToggleDisable"in R&&t(17,l=R.onMCPServerToggleDisable),"onCancel"in R&&t(18,d=R.onCancel),"onMCPServerJSONImport"in R&&t(19,u=R.onMCPServerJSONImport),"isMCPImportEnabled"in R&&t(1,g=R.isMCPImportEnabled)},s.$$.update=()=>{12&s.$$.dirty&&t(5,n=$==="add"||$==="addJson"||$==="addHttp"||$==="addSse"||f!==null),1&s.$$.dirty&&t(4,r=cr.parseServerValidationMessages(a))},[a,g,f,$,r,n,function(){t(2,f=null),t(3,$=null),d==null||d()},function(R){t(2,f=R.id)},S,M,I,Z,C,A,i,o,c,l,d,u,()=>{t(3,$="add")},()=>{t(3,$="addSse")},()=>{t(3,$="addHttp")},()=>{t(3,$="addJson")}]}class Nm extends $e{constructor(e){super(),ve(this,e,Am,Mm,he,{servers:0,onMCPServerAdd:14,onMCPServerSave:15,onMCPServerDelete:16,onMCPServerToggleDisable:17,onCancel:18,onMCPServerJSONImport:19,isMCPImportEnabled:1})}}function La(s,e,t){const n=s.slice();return n[12]=e[t],n}function Em(s){let e;return{c(){e=T("div"),e.textContent="Terminal",_(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:H,d(t){t&&v(e)}}}function Zm(s){let e;return{c(){e=L("Shell:")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Im(s){let e;return{c(){e=L("Select a shell")},m(t,n){y(t,e,n)},p:H,d(t){t&&v(e)}}}function Rm(s){let e;return{c(){e=L("No shells available")},m(t,n){y(t,e,n)},p:H,d(t){t&&v(e)}}}function Om(s){let e,t,n,r,a=s[5].friendlyName+"",i=s[5].supportString+"";return{c(){e=L(a),t=L(`
            (`),n=L(i),r=L(")")},m(o,c){y(o,e,c),y(o,t,c),y(o,n,c),y(o,r,c)},p(o,c){32&c&&a!==(a=o[5].friendlyName+"")&&fe(e,a),32&c&&i!==(i=o[5].supportString+"")&&fe(n,i)},d(o){o&&(v(e),v(t),v(n),v(r))}}}function Pm(s){let e;function t(a,i){return a[5]&&a[1].length>0?Om:a[1].length===0?Rm:Im}let n=t(s),r=n(s);return{c(){r.c(),e=Se()},m(a,i){r.m(a,i),y(a,e,i)},p(a,i){n===(n=t(a))&&r?r.p(a,i):(r.d(1),r=n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&v(e),r.d(a)}}}function jm(s){let e,t;return e=new Mo({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Lm(s){let e,t;return e=new Me({props:{size:1,variant:"outline",color:"neutral",disabled:s[1].length===0,$$slots:{iconRight:[jm],default:[Pm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.disabled=n[1].length===0),32802&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Fm(s){let e,t;return e=new Te.Label({props:{$$slots:{default:[Dm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32768&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function zm(s){let e,t,n=[],r=new Map,a=_e(s[1]);const i=o=>o[12].friendlyName;for(let o=0;o<a.length;o+=1){let c=La(s,a,o),l=i(c);r.set(l,n[o]=Fa(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Se()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){30&c&&(a=_e(o[1]),B(),n=jt(n,c,i,1,o,a,r,e.parentNode,Lt,Fa,e,La),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Dm(s){let e;return{c(){e=L("No shells available")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Um(s){let e,t,n,r,a=s[12].friendlyName+"",i=s[12].supportString+"";return{c(){e=L(a),t=L(`
              (`),n=L(i),r=L(`)
            `)},m(o,c){y(o,e,c),y(o,t,c),y(o,n,c),y(o,r,c)},p(o,c){2&c&&a!==(a=o[12].friendlyName+"")&&fe(e,a),2&c&&i!==(i=o[12].supportString+"")&&fe(n,i)},d(o){o&&(v(e),v(t),v(n),v(r))}}}function Fa(s,e){let t,n,r;function a(){return e[8](e[12])}return n=new Te.Item({props:{onSelect:a,highlight:e[2]===e[12].friendlyName,$$slots:{default:[Um]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(i,o){y(i,t,o),x(n,i,o),r=!0},p(i,o){e=i;const c={};26&o&&(c.onSelect=a),6&o&&(c.highlight=e[2]===e[12].friendlyName),32770&o&&(c.$$scope={dirty:o,ctx:e}),n.$set(c)},i(i){r||(p(n.$$.fragment,i),r=!0)},o(i){m(n.$$.fragment,i),r=!1},d(i){i&&v(t),b(n,i)}}}function Vm(s){let e,t,n,r;const a=[zm,Fm],i=[];function o(c,l){return c[1].length>0?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Se()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function qm(s){let e,t,n,r;return e=new Te.Trigger({props:{$$slots:{default:[Lm]},$$scope:{ctx:s}}}),n=new Te.Content({props:{side:"bottom",align:"start",$$slots:{default:[Vm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};32802&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};32798&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Bm(s){let e;return{c(){e=L("Start-up script: Code to run wherever a new terminal is opened")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Jm(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h;function S(C){s[9](C)}t=new ie({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Em]},$$scope:{ctx:s}}}),a=new ie({props:{size:1,$$slots:{default:[Zm]},$$scope:{ctx:s}}});let M={$$slots:{default:[qm]},$$scope:{ctx:s}};function I(C){s[10](C)}s[4]!==void 0&&(M.requestClose=s[4]),o=new Te.Root({props:M}),Ae.push(()=>Ne(o,"requestClose",S)),u=new ie({props:{size:1,$$slots:{default:[Bm]},$$scope:{ctx:s}}});let Z={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return s[0]!==void 0&&(Z.value=s[0]),f=new yi({props:Z}),Ae.push(()=>Ne(f,"value",I)),f.$on("change",s[6]),{c(){e=T("div"),w(t.$$.fragment),n=E(),r=T("div"),w(a.$$.fragment),i=E(),w(o.$$.fragment),l=E(),d=T("div"),w(u.$$.fragment),g=E(),w(f.$$.fragment),_(r,"class","shell-selector svelte-dndd5n"),_(d,"class","startup-script-container svelte-dndd5n"),_(e,"class","terminal-settings svelte-dndd5n")},m(C,A){y(C,e,A),x(t,e,null),k(e,n),k(e,r),x(a,r,null),k(r,i),x(o,r,null),k(e,l),k(e,d),x(u,d,null),k(d,g),x(f,d,null),h=!0},p(C,[A]){const R={};32768&A&&(R.$$scope={dirty:A,ctx:C}),t.$set(R);const G={};32768&A&&(G.$$scope={dirty:A,ctx:C}),a.$set(G);const le={};32830&A&&(le.$$scope={dirty:A,ctx:C}),!c&&16&A&&(c=!0,le.requestClose=C[4],Ee(()=>c=!1)),o.$set(le);const ke={};32768&A&&(ke.$$scope={dirty:A,ctx:C}),u.$set(ke);const W={};!$&&1&A&&($=!0,W.value=C[0],Ee(()=>$=!1)),f.$set(W)},i(C){h||(p(t.$$.fragment,C),p(a.$$.fragment,C),p(o.$$.fragment,C),p(u.$$.fragment,C),p(f.$$.fragment,C),h=!0)},o(C){m(t.$$.fragment,C),m(a.$$.fragment,C),m(o.$$.fragment,C),m(u.$$.fragment,C),m(f.$$.fragment,C),h=!1},d(C){C&&v(e),b(t),b(a),b(o),b(u),b(f)}}}function Gm(s,e,t){let n,r,{supportedShells:a=[]}=e,{selectedShell:i}=e,{startupScript:o}=e,{onShellSelect:c}=e,{onStartupScriptChange:l}=e;return s.$$set=d=>{"supportedShells"in d&&t(1,a=d.supportedShells),"selectedShell"in d&&t(2,i=d.selectedShell),"startupScript"in d&&t(0,o=d.startupScript),"onShellSelect"in d&&t(3,c=d.onShellSelect),"onStartupScriptChange"in d&&t(7,l=d.onStartupScriptChange)},s.$$.update=()=>{var d;4&s.$$.dirty&&t(5,n=i?(d=i,a.find(u=>u.friendlyName===d)):void 0)},[o,a,i,c,r,n,function(d){const u=d.target;l(u.value)},l,d=>{c(d.friendlyName),r()},function(d){r=d,t(4,r)},function(d){o=d,t(0,o)}]}class Wm extends $e{constructor(e){super(),ve(this,e,Gm,Jm,he,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function za(s){let e,t;return e=new Nm({props:{servers:s[1],onMCPServerAdd:s[7],onMCPServerSave:s[8],onMCPServerDelete:s[9],onMCPServerToggleDisable:s[10],onMCPServerJSONImport:s[11],onCancel:s[12],isMCPImportEnabled:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.servers=n[1]),128&r&&(a.onMCPServerAdd=n[7]),256&r&&(a.onMCPServerSave=n[8]),512&r&&(a.onMCPServerDelete=n[9]),1024&r&&(a.onMCPServerToggleDisable=n[10]),2048&r&&(a.onMCPServerJSONImport=n[11]),4096&r&&(a.onCancel=n[12]),8&r&&(a.isMCPImportEnabled=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Da(s){let e,t;return e=new Wm({props:{supportedShells:s[13],selectedShell:s[14],startupScript:s[15],onShellSelect:s[16],onStartupScriptChange:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.supportedShells=n[13]),16384&r&&(a.selectedShell=n[14]),32768&r&&(a.startupScript=n[15]),65536&r&&(a.onShellSelect=n[16]),131072&r&&(a.onStartupScriptChange=n[17]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Hm(s){let e,t,n,r,a;t=new Jd({props:{title:"Services",tools:s[0],onAuthenticate:s[5],onRevokeAccess:s[6]}});let i=s[2]&&za(s),o=s[4]&&Da(s);return{c(){e=T("div"),w(t.$$.fragment),n=E(),i&&i.c(),r=E(),o&&o.c(),_(e,"class","c-settings-tools svelte-181yusq")},m(c,l){y(c,e,l),x(t,e,null),k(e,n),i&&i.m(e,null),k(e,r),o&&o.m(e,null),a=!0},p(c,[l]){const d={};1&l&&(d.tools=c[0]),32&l&&(d.onAuthenticate=c[5]),64&l&&(d.onRevokeAccess=c[6]),t.$set(d),c[2]?i?(i.p(c,l),4&l&&p(i,1)):(i=za(c),i.c(),p(i,1),i.m(e,r)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c[4]?o?(o.p(c,l),16&l&&p(o,1)):(o=Da(c),o.c(),p(o,1),o.m(e,null)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){a||(p(t.$$.fragment,c),p(i),p(o),a=!0)},o(c){m(t.$$.fragment,c),m(i),m(o),a=!1},d(c){c&&v(e),b(t),i&&i.d(),o&&o.d()}}}function Km(s,e,t){let{tools:n=[]}=e,{servers:r=[]}=e,{isMCPEnabled:a=!0}=e,{isMCPImportEnabled:i=!0}=e,{isTerminalEnabled:o=!0}=e,{onAuthenticate:c}=e,{onRevokeAccess:l}=e,{onMCPServerAdd:d}=e,{onMCPServerSave:u}=e,{onMCPServerDelete:g}=e,{onMCPServerToggleDisable:f}=e,{onMCPServerJSONImport:$}=e,{onCancel:h}=e,{supportedShells:S=[]}=e,{selectedShell:M}=e,{startupScript:I}=e,{onShellSelect:Z=()=>{}}=e,{onStartupScriptChange:C=()=>{}}=e;return s.$$set=A=>{"tools"in A&&t(0,n=A.tools),"servers"in A&&t(1,r=A.servers),"isMCPEnabled"in A&&t(2,a=A.isMCPEnabled),"isMCPImportEnabled"in A&&t(3,i=A.isMCPImportEnabled),"isTerminalEnabled"in A&&t(4,o=A.isTerminalEnabled),"onAuthenticate"in A&&t(5,c=A.onAuthenticate),"onRevokeAccess"in A&&t(6,l=A.onRevokeAccess),"onMCPServerAdd"in A&&t(7,d=A.onMCPServerAdd),"onMCPServerSave"in A&&t(8,u=A.onMCPServerSave),"onMCPServerDelete"in A&&t(9,g=A.onMCPServerDelete),"onMCPServerToggleDisable"in A&&t(10,f=A.onMCPServerToggleDisable),"onMCPServerJSONImport"in A&&t(11,$=A.onMCPServerJSONImport),"onCancel"in A&&t(12,h=A.onCancel),"supportedShells"in A&&t(13,S=A.supportedShells),"selectedShell"in A&&t(14,M=A.selectedShell),"startupScript"in A&&t(15,I=A.startupScript),"onShellSelect"in A&&t(16,Z=A.onShellSelect),"onStartupScriptChange"in A&&t(17,C=A.onStartupScriptChange)},[n,r,a,i,o,c,l,d,u,g,f,$,h,S,M,I,Z,C]}class Ym extends $e{constructor(e){super(),ve(this,e,Km,Hm,he,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,onAuthenticate:5,onRevokeAccess:6,onMCPServerAdd:7,onMCPServerSave:8,onMCPServerDelete:9,onMCPServerToggleDisable:10,onMCPServerJSONImport:11,onCancel:12,supportedShells:13,selectedShell:14,startupScript:15,onShellSelect:16,onStartupScriptChange:17})}}function Xm(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Fe(r,n[a]);return{c(){e=et("svg"),t=new er(!0),this.h()},l(a){e=tr(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=nr(e);t=sr(i,!0),i.forEach(v),this.h()},h(){t.a=null,nn(e,r)},m(a,i){rr(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',e)},p(a,[i]){nn(e,r=Vt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&v(e)}}}function Qm(s,e,t){return s.$$set=n=>{t(0,e=Fe(Fe({},e),Mt(n)))},[e=Mt(e)]}class ef extends $e{constructor(e){super(),ve(this,e,Qm,Xm,he,{})}}function tf(s){let e,t,n,r;function a(o){s[6](o)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:s[3]};return s[0]!==void 0&&(i.value=s[0]),t=new qo({props:i}),Ae.push(()=>Ne(t,"value",a)),t.$on("focus",s[7]),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(o,c){y(o,e,c),x(t,e,null),r=!0},p(o,[c]){const l={};!n&&1&c&&(n=!0,l.value=o[0],Ee(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&v(e),b(t)}}}function nf(s,e,t){let n;const r=Cs();let{userGuidelines:a=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:o=()=>!1}=e;const c=Ve(void 0);function l(){const d=a.trim();if(n!==d){if(!o(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";$r(c,n=d,n)}}return lt(s,c,d=>t(8,n=d)),di(()=>{$r(c,n=a.trim(),n)}),ui(()=>{l()}),s.$$set=d=>{"userGuidelines"in d&&t(0,a=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,o=d.updateUserGuideline)},[a,r,c,l,i,o,function(d){a=d,t(0,a)},d=>{r("focus",d)}]}class Xi extends $e{constructor(e){super(),ve(this,e,nf,tf,he,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}var $t=(s=>(s.getRulesListRequest="get-rules-list-request",s.getRulesListResponse="get-rules-list-response",s.createRule="create-rule",s.createRuleResponse="create-rule-response",s.openRule="open-rule",s.openGuidelines="open-guidelines",s.deleteRule="delete-rule",s.updateRuleFile="update-rule-file",s.updateRuleFileResponse="update-rule-file-response",s.getWorkspaceRoot="get-workspace-root",s.getWorkspaceRootResponse="get-workspace-root-response",s.autoImportRules="auto-import-rules",s.autoImportRulesOptionsResponse="auto-import-rules-options-response",s.autoImportRulesSelectionRequest="auto-import-rules-selection-request",s.autoImportRulesResponse="auto-import-rules-response",s.processSelectedPathsRequest="process-selected-paths-request",s.processSelectedPathsResponse="process-selected-paths-response",s))($t||{});class sf{constructor(e){ye(this,"_rulesFiles",Ve([]));ye(this,"_loading",Ve(!0));this._msgBroker=e,this.requestRules()}handleMessageFromExtension(e){return!(!e.data||e.data.type!==ge.getRulesListResponse)&&(this._rulesFiles.set(e.data.data),this._loading.set(!1),!0)}async requestRules(){this._loading.set(!0);try{const e=await this._msgBroker.sendToSidecar({type:$t.getRulesListRequest});this._rulesFiles.set(e.data)}catch(e){console.error("Failed to get rules list:",e)}finally{this._loading.set(!1)}}async createRule(e){try{const t=await this._msgBroker.sendToSidecar({type:$t.createRule,data:{ruleName:e.trim()}});return await this.requestRules(),t.data.createdRule||null}catch(t){throw console.error("Failed to create rule:",t),t}}async getWorkspaceRoot(){try{return(await this._msgBroker.sendToSidecar({type:$t.getWorkspaceRoot})).data.workspaceRoot||""}catch(e){return console.error("Failed to get workspace root:",e),""}}async updateRuleType(e,t,n){const r=Ro.updateAlwaysApplyFrontmatterKey(t,n);try{await this._msgBroker.sendToSidecar({type:$t.updateRuleFile,data:{path:e,content:r}})}catch(a){console.error("Failed to update rule file:",a)}await this.requestRules()}async deleteRule(e){try{await this._msgBroker.sendToSidecar({type:$t.deleteRule,data:{path:e,confirmed:!0}}),await this.requestRules()}catch(t){throw console.error("Failed to delete rule:",t),t}}async processSelectedPaths(e){try{const t=await this._msgBroker.sendToSidecar({type:$t.processSelectedPathsRequest,data:{selectedPaths:e,autoImport:!0}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,directoryOrFile:t.data.directoryOrFile,errors:t.data.errors}}catch(t){throw console.error("Failed to process selected paths:",t),t}}async getAutoImportOptions(){return await this._msgBroker.sendToSidecar({type:$t.autoImportRules})}async processAutoImportSelection(e){try{const t=await this._msgBroker.sendToSidecar({type:$t.autoImportRulesSelectionRequest,data:{selectedLabel:e.label}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,duplicatesCount:t.data.duplicatesCount,totalAttempted:t.data.totalAttempted,source:t.data.source}}catch(t){throw console.error("Failed to process auto-import selection:",t),t}}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}class rf{constructor(e,t,n){ye(this,"_showCreateRuleDialog",Ve(!1));ye(this,"_createRuleError",Ve(""));ye(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=n;const r=new Go;this._extensionClient=new Wo(e,t,r)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:Ps.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Qn.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const n=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(n)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===wr?this._extensionClient.openFile({repoRoot:t,pathName:wr}):this._extensionClient.openFile({repoRoot:t,pathName:`${$i}/${vi}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:ge.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}async updateRuleType(e,t,n){await this._rulesModel.updateRuleType(e,t,n)}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?Qn.selectedDirectory:(e.directoryOrFile,Qn.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:Ps.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:Ps.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Qn.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}const af=s=>({}),Ua=s=>({}),of=s=>({}),Va=s=>({}),cf=s=>({}),qa=s=>({});function Ba(s){let e,t,n,r,a,i;return n=new Ko({props:{variant:"soft",size:3,$$slots:{default:[pf]},$$scope:{ctx:s}}}),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),_(t,"class","c-modal svelte-1hwqfwo"),_(t,"role","dialog"),_(t,"aria-modal","true"),_(t,"aria-labelledby",s[3]),vr(t,"max-width",s[2]),_(e,"class","c-modal-backdrop svelte-1hwqfwo"),_(e,"role","presentation")},m(o,c){y(o,e,c),k(e,t),x(n,t,null),r=!0,a||(i=[Le(t,"click",yr(s[10])),Le(t,"keydown",yr(s[11])),Le(e,"click",s[4]),Le(e,"keydown",s[5])],a=!0)},p(o,c){const l={};4170&c&&(l.$$scope={dirty:c,ctx:o}),n.$set(l),(!r||8&c)&&_(t,"aria-labelledby",o[3]),(!r||4&c)&&vr(t,"max-width",o[2])},i(o){r||(p(n.$$.fragment,o),r=!0)},o(o){m(n.$$.fragment,o),r=!1},d(o){o&&v(e),b(n),a=!1,Ss(i)}}}function Ja(s){let e,t,n,r;const a=[df,lf],i=[];function o(c,l){return c[6].header?0:c[1]?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=T("div"),n&&n.c(),_(e,"class","c-modal-header svelte-1hwqfwo")},m(c,l){y(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(B(),m(i[d],1,1,()=>{i[d]=null}),J()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),~t&&i[t].d()}}}function lf(s){let e,t;return e=new ie({props:{id:s[3],size:3,weight:"bold",color:"primary",$$slots:{default:[uf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.id=n[3]),4098&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function df(s){let e;const t=s[9].header,n=Ie(t,s,s[12],qa);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4096&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,cf):Oe(r[12]),qa)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function uf(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&fe(e,t[1])},d(t){t&&v(e)}}}function Ga(s){let e,t;const n=s[9].body,r=Ie(n,s,s[12],Va),a=r||function(i){let o;const c=i[9].default,l=Ie(c,i,i[12],null);return{c(){l&&l.c()},m(d,u){l&&l.m(d,u),o=!0},p(d,u){l&&l.p&&(!o||4096&u)&&Re(l,c,d,d[12],o?Pe(c,d[12],u,null):Oe(d[12]),null)},i(d){o||(p(l,d),o=!0)},o(d){m(l,d),o=!1},d(d){l&&l.d(d)}}}(s);return{c(){e=T("div"),a&&a.c(),_(e,"class","c-modal-body svelte-1hwqfwo")},m(i,o){y(i,e,o),a&&a.m(e,null),t=!0},p(i,o){r?r.p&&(!t||4096&o)&&Re(r,n,i,i[12],t?Pe(n,i[12],o,of):Oe(i[12]),Va):a&&a.p&&(!t||4096&o)&&a.p(i,t?o:-1)},i(i){t||(p(a,i),t=!0)},o(i){m(a,i),t=!1},d(i){i&&v(e),a&&a.d(i)}}}function Wa(s){let e,t;const n=s[9].footer,r=Ie(n,s,s[12],Ua);return{c(){e=T("div"),r&&r.c(),_(e,"class","c-modal-footer svelte-1hwqfwo")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||4096&i)&&Re(r,n,a,a[12],t?Pe(n,a[12],i,af):Oe(a[12]),Ua)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function pf(s){let e,t,n,r,a=(s[1]||s[6].header)&&Ja(s),i=(s[6].body||s[6].default)&&Ga(s),o=s[6].footer&&Wa(s);return{c(){e=T("div"),a&&a.c(),t=E(),i&&i.c(),n=E(),o&&o.c(),_(e,"class","c-modal-content svelte-1hwqfwo")},m(c,l){y(c,e,l),a&&a.m(e,null),k(e,t),i&&i.m(e,null),k(e,n),o&&o.m(e,null),r=!0},p(c,l){c[1]||c[6].header?a?(a.p(c,l),66&l&&p(a,1)):(a=Ja(c),a.c(),p(a,1),a.m(e,t)):a&&(B(),m(a,1,1,()=>{a=null}),J()),c[6].body||c[6].default?i?(i.p(c,l),64&l&&p(i,1)):(i=Ga(c),i.c(),p(i,1),i.m(e,n)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c[6].footer?o?(o.p(c,l),64&l&&p(o,1)):(o=Wa(c),o.c(),p(o,1),o.m(e,null)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){r||(p(a),p(i),p(o),r=!0)},o(c){m(a),m(i),m(o),r=!1},d(c){c&&v(e),a&&a.d(),i&&i.d(),o&&o.d()}}}function mf(s){let e,t,n=s[0]&&Ba(s);return{c(){n&&n.c(),e=Se()},m(r,a){n&&n.m(r,a),y(r,e,a),t=!0},p(r,[a]){r[0]?n?(n.p(r,a),1&a&&p(n,1)):(n=Ba(r),n.c(),p(n,1),n.m(e.parentNode,e)):n&&(B(),m(n,1,1,()=>{n=null}),J())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&v(e),n&&n.d(r)}}}function ff(s,e,t){let{$$slots:n={},$$scope:r}=e;const a=Qs(n),i=Cs();let{show:o=!1}=e,{title:c=""}=e,{maxWidth:l="400px"}=e,{preventBackdropClose:d=!1}=e,{preventEscapeClose:u=!1}=e,{ariaLabelledBy:g="modal-title"}=e;return s.$$set=f=>{"show"in f&&t(0,o=f.show),"title"in f&&t(1,c=f.title),"maxWidth"in f&&t(2,l=f.maxWidth),"preventBackdropClose"in f&&t(7,d=f.preventBackdropClose),"preventEscapeClose"in f&&t(8,u=f.preventEscapeClose),"ariaLabelledBy"in f&&t(3,g=f.ariaLabelledBy),"$$scope"in f&&t(12,r=f.$$scope)},[o,c,l,g,function(){d||i("cancel"),i("backdropClick")},function(f){f.key!=="Escape"||u||(f.preventDefault(),i("cancel")),i("keydown",f)},a,d,u,n,function(f){as.call(this,s,f)},function(f){as.call(this,s,f)},r]}class Qi extends $e{constructor(e){super(),ve(this,e,ff,mf,he,{show:0,title:1,maxWidth:2,preventBackdropClose:7,preventEscapeClose:8,ariaLabelledBy:3})}}function hf(s){let e;return{c(){e=L("Enter a name for the new rule file (e.g., architecture.md):")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ha(s){let e,t;return e=new Ms({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[$f],default:[gf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};4098&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function gf(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&fe(e,t[1])},d(t){t&&v(e)}}}function $f(s){let e,t;return e=new _i({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function vf(s){let e,t,n,r,a,i,o,c;function l(f){s[9](f)}function d(f){s[10](f)}e=new ie({props:{size:2,color:"secondary",$$slots:{default:[hf]},$$scope:{ctx:s}}});let u={placeholder:"rule-name.md",disabled:s[4]};s[3]!==void 0&&(u.value=s[3]),s[2]!==void 0&&(u.textInput=s[2]),n=new Nt({props:u}),Ae.push(()=>Ne(n,"value",l)),Ae.push(()=>Ne(n,"textInput",d)),n.$on("keydown",s[8]);let g=s[1]&&Ha(s);return{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment),i=E(),g&&g.c(),o=Se()},m(f,$){x(e,f,$),y(f,t,$),x(n,f,$),y(f,i,$),g&&g.m(f,$),y(f,o,$),c=!0},p(f,$){const h={};4096&$&&(h.$$scope={dirty:$,ctx:f}),e.$set(h);const S={};16&$&&(S.disabled=f[4]),!r&&8&$&&(r=!0,S.value=f[3],Ee(()=>r=!1)),!a&&4&$&&(a=!0,S.textInput=f[2],Ee(()=>a=!1)),n.$set(S),f[1]?g?(g.p(f,$),2&$&&p(g,1)):(g=Ha(f),g.c(),p(g,1),g.m(o.parentNode,o)):g&&(B(),m(g,1,1,()=>{g=null}),J())},i(f){c||(p(e.$$.fragment,f),p(n.$$.fragment,f),p(g),c=!0)},o(f){m(e.$$.fragment,f),m(n.$$.fragment,f),m(g),c=!1},d(f){f&&(v(t),v(i),v(o)),b(e,f),b(n,f),g&&g.d(f)}}}function yf(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function _f(s){let e,t=s[4]?"Creating...":"Create";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){16&r&&t!==(t=n[4]?"Creating...":"Create")&&fe(e,t)},d(n){n&&v(e)}}}function wf(s){let e,t,n,r,a;return t=new Me({props:{variant:"solid",color:"neutral",disabled:s[4],$$slots:{default:[yf]},$$scope:{ctx:s}}}),t.$on("click",s[6]),r=new Me({props:{variant:"solid",color:"accent",disabled:!s[3].trim()||s[4],loading:s[4],$$slots:{default:[_f]},$$scope:{ctx:s}}}),r.$on("click",s[5]),{c(){e=T("div"),w(t.$$.fragment),n=E(),w(r.$$.fragment),_(e,"slot","footer")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),x(r,e,null),a=!0},p(i,o){const c={};16&o&&(c.disabled=i[4]),4096&o&&(c.$$scope={dirty:o,ctx:i}),t.$set(c);const l={};24&o&&(l.disabled=!i[3].trim()||i[4]),16&o&&(l.loading=i[4]),4112&o&&(l.$$scope={dirty:o,ctx:i}),r.$set(l)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function xf(s){let e,t;return e=new Qi({props:{show:s[0],title:"Create New Rule",ariaLabelledBy:"dialog-title",preventBackdropClose:s[4],preventEscapeClose:s[4],$$slots:{footer:[wf],body:[vf]},$$scope:{ctx:s}}}),e.$on("cancel",s[6]),e.$on("keydown",s[7]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.show=n[0]),16&r&&(a.preventBackdropClose=n[4]),16&r&&(a.preventEscapeClose=n[4]),4126&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function bf(s,e,t){const n=Cs();let r,{show:a=!1}=e,{errorMessage:i=""}=e,o="",c=!1;function l(){o.trim()&&!c&&(t(4,c=!0),n("create",o.trim()))}function d(){c||(n("cancel"),t(3,o=""))}return s.$$set=u=>{"show"in u&&t(0,a=u.show),"errorMessage"in u&&t(1,i=u.errorMessage)},s.$$.update=()=>{5&s.$$.dirty&&a&&r&&setTimeout(()=>r==null?void 0:r.focus(),100),3&s.$$.dirty&&(a&&!i||t(4,c=!1)),3&s.$$.dirty&&(a||i||t(3,o=""))},[a,i,r,o,c,l,d,function(u){c||u.detail.key==="Enter"&&(u.detail.preventDefault(),l())},function(u){c||(u.key==="Enter"?(u.preventDefault(),l()):u.key==="Escape"&&(u.preventDefault(),d()))},function(u){o=u,t(3,o),t(0,a),t(1,i)},function(u){r=u,t(2,r)}]}class Sf extends $e{constructor(e){super(),ve(this,e,bf,xf,he,{show:0,errorMessage:1})}}function Ka(s,e,t){const n=s.slice();return n[18]=e[t],n}function kf(s){let e,t,n,r,a,i,o,c,l;function d(h){s[15](h)}function u(h){s[16](h)}e=new ie({props:{size:2,color:"secondary",$$slots:{default:[Tf]},$$scope:{ctx:s}}});let g={triggerOn:s[1].length===0?[]:void 0,$$slots:{default:[Zf]},$$scope:{ctx:s}};s[7]!==void 0&&(g.requestClose=s[7]),s[6]!==void 0&&(g.focusedIndex=s[6]),n=new Te.Root({props:g}),Ae.push(()=>Ne(n,"requestClose",d)),Ae.push(()=>Ne(n,"focusedIndex",u));let f=s[3]&&Qa(s),$=s[4]&&ei(s);return{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment),i=E(),f&&f.c(),o=E(),$&&$.c(),c=Se()},m(h,S){x(e,h,S),y(h,t,S),x(n,h,S),y(h,i,S),f&&f.m(h,S),y(h,o,S),$&&$.m(h,S),y(h,c,S),l=!0},p(h,S){const M={};2097152&S&&(M.$$scope={dirty:S,ctx:h}),e.$set(M);const I={};2&S&&(I.triggerOn=h[1].length===0?[]:void 0),2097442&S&&(I.$$scope={dirty:S,ctx:h}),!r&&128&S&&(r=!0,I.requestClose=h[7],Ee(()=>r=!1)),!a&&64&S&&(a=!0,I.focusedIndex=h[6],Ee(()=>a=!1)),n.$set(I),h[3]?f?(f.p(h,S),8&S&&p(f,1)):(f=Qa(h),f.c(),p(f,1),f.m(o.parentNode,o)):f&&(B(),m(f,1,1,()=>{f=null}),J()),h[4]?$?($.p(h,S),16&S&&p($,1)):($=ei(h),$.c(),p($,1),$.m(c.parentNode,c)):$&&(B(),m($,1,1,()=>{$=null}),J())},i(h){l||(p(e.$$.fragment,h),p(n.$$.fragment,h),p(f),p($),l=!0)},o(h){m(e.$$.fragment,h),m(n.$$.fragment,h),m(f),m($),l=!1},d(h){h&&(v(t),v(i),v(o),v(c)),b(e,h),b(n,h),f&&f.d(h),$&&$.d(h)}}}function Cf(s){let e;return{c(){e=T("input"),_(e,"type","text"),e.value="No existing rules found",e.readOnly=!0,_(e,"class","c-dropdown-input svelte-z1s6x7")},m(t,n){y(t,e,n)},p:H,i:H,o:H,d(t){t&&v(e)}}}function Tf(s){let e;return{c(){e=L("Select existing rules to auto import to .augment/rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Mf(s){let e,t,n,r,a,i;return a=new wi({props:{class:"c-dropdown-chevron"}}),{c(){e=T("div"),t=T("input"),r=E(),w(a.$$.fragment),_(t,"type","text"),t.value=n=s[5]?s[5].label:"Existing rules",t.readOnly=!0,_(t,"class","c-dropdown-input svelte-z1s6x7"),_(e,"class","c-dropdown-trigger svelte-z1s6x7")},m(o,c){y(o,e,c),k(e,t),k(e,r),x(a,e,null),i=!0},p(o,c){(!i||32&c&&n!==(n=o[5]?o[5].label:"Existing rules")&&t.value!==n)&&(t.value=n)},i(o){i||(p(a.$$.fragment,o),i=!0)},o(o){m(a.$$.fragment,o),i=!1},d(o){o&&v(e),b(a)}}}function Af(s){let e,t=s[18].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){2&r&&t!==(t=n[18].label+"")&&fe(e,t)},d(n){n&&v(e)}}}function Ya(s){var r;let e,t;function n(){return s[14](s[18])}return e=new Te.Item({props:{onSelect:n,highlight:((r=s[5])==null?void 0:r.label)===s[18].label,$$slots:{default:[Af]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0},p(a,i){var c;s=a;const o={};2&i&&(o.onSelect=n),34&i&&(o.highlight=((c=s[5])==null?void 0:c.label)===s[18].label),2097154&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a)}}}function Xa(s){let e,t,n,r;return e=new Te.Separator({}),n=new Te.Label({props:{$$slots:{default:[Nf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2097442&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Nf(s){var n;let e,t=(s[8]!==void 0?s[1][s[8]].description:(n=s[5])==null?void 0:n.description)+"";return{c(){e=L(t)},m(r,a){y(r,e,a)},p(r,a){var i;290&a&&t!==(t=(r[8]!==void 0?r[1][r[8]].description:(i=r[5])==null?void 0:i.description)+"")&&fe(e,t)},d(r){r&&v(e)}}}function Ef(s){let e,t,n,r=_e(s[1]),a=[];for(let c=0;c<r.length;c+=1)a[c]=Ya(Ka(s,r,c));const i=c=>m(a[c],1,1,()=>{a[c]=null});let o=(s[8]!==void 0||s[5])&&Xa(s);return{c(){for(let c=0;c<a.length;c+=1)a[c].c();e=E(),o&&o.c(),t=Se()},m(c,l){for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(c,l);y(c,e,l),o&&o.m(c,l),y(c,t,l),n=!0},p(c,l){if(546&l){let d;for(r=_e(c[1]),d=0;d<r.length;d+=1){const u=Ka(c,r,d);a[d]?(a[d].p(u,l),p(a[d],1)):(a[d]=Ya(u),a[d].c(),p(a[d],1),a[d].m(e.parentNode,e))}for(B(),d=r.length;d<a.length;d+=1)i(d);J()}c[8]!==void 0||c[5]?o?(o.p(c,l),288&l&&p(o,1)):(o=Xa(c),o.c(),p(o,1),o.m(t.parentNode,t)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){if(!n){for(let l=0;l<r.length;l+=1)p(a[l]);p(o),n=!0}},o(c){a=a.filter(Boolean);for(let l=0;l<a.length;l+=1)m(a[l]);m(o),n=!1},d(c){c&&(v(e),v(t)),Ht(a,c),o&&o.d(c)}}}function Zf(s){let e,t,n,r;return e=new Te.Trigger({props:{$$slots:{default:[Mf]},$$scope:{ctx:s}}}),n=new Te.Content({props:{align:"start",side:"bottom",$$slots:{default:[Ef]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2097184&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};2097442&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Qa(s){let e,t;return e=new Ms({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Rf],default:[If]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2097160&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function If(s){let e;return{c(){e=L(s[3])},m(t,n){y(t,e,n)},p(t,n){8&n&&fe(e,t[3])},d(t){t&&v(e)}}}function Rf(s){let e,t;return e=new _i({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ei(s){let e,t;return e=new Ms({props:{variant:"soft",color:"success",size:1,$$slots:{icon:[Pf],default:[Of]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2097168&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Of(s){let e;return{c(){e=L(s[4])},m(t,n){y(t,e,n)},p(t,n){16&n&&fe(e,t[4])},d(t){t&&v(e)}}}function Pf(s){let e,t;return e=new Ao({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function jf(s){let e,t,n,r;const a=[Cf,kf],i=[];function o(c,l){return c[1].length===0?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=T("div"),n.c(),_(e,"slot","body"),_(e,"class","c-auto-import-rules-dialog svelte-z1s6x7")},m(c,l){y(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?i[t].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),i[t].d()}}}function Lf(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ti(s){let e,t;return e=new Me({props:{color:"accent",variant:"solid",disabled:!s[5]||s[2],loading:s[2],$$slots:{default:[Ff]},$$scope:{ctx:s}}}),e.$on("click",s[10]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};36&r&&(a.disabled=!n[5]||n[2]),4&r&&(a.loading=n[2]),2097156&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ff(s){let e,t=s[2]?"Importing...":"Import ";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){4&r&&t!==(t=n[2]?"Importing...":"Import ")&&fe(e,t)},d(n){n&&v(e)}}}function zf(s){let e,t,n,r;t=new Me({props:{variant:"solid",color:"neutral",disabled:s[2],$$slots:{default:[Lf]},$$scope:{ctx:s}}}),t.$on("click",s[11]);let a=s[1].length>0&&ti(s);return{c(){e=T("div"),w(t.$$.fragment),n=E(),a&&a.c(),_(e,"slot","footer")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),a&&a.m(e,null),r=!0},p(i,o){const c={};4&o&&(c.disabled=i[2]),2097152&o&&(c.$$scope={dirty:o,ctx:i}),t.$set(c),i[1].length>0?a?(a.p(i,o),2&o&&p(a,1)):(a=ti(i),a.c(),p(a,1),a.m(e,null)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){r||(p(t.$$.fragment,i),p(a),r=!0)},o(i){m(t.$$.fragment,i),m(a),r=!1},d(i){i&&v(e),b(t),a&&a.d()}}}function Df(s){let e,t,n,r;return e=new Qi({props:{show:s[0],title:"Auto Import Rules",ariaLabelledBy:"dialog-title",preventBackdropClose:s[2],preventEscapeClose:s[2],$$slots:{footer:[zf],body:[jf]},$$scope:{ctx:s}}}),e.$on("cancel",s[11]),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"keydown",s[12]),n=!0)},p(a,[i]){const o={};1&i&&(o.show=a[0]),4&i&&(o.preventBackdropClose=a[2]),4&i&&(o.preventEscapeClose=a[2]),2097662&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}function Uf(s,e,t){let n,r,a=H,i=()=>(a(),a=ks(c,Z=>t(8,r=Z)),c);s.$$.on_destroy.push(()=>a());const o=Cs();let c,{show:l=!1}=e,{options:d=[]}=e,{isLoading:u=!1}=e,{errorMessage:g=""}=e,{successMessage:f=""}=e,$=n;i();let h=()=>{};function S(Z){t(5,$=Z),h()}function M(){$&&!u&&o("select",$)}function I(){u||(o("cancel"),t(5,$=n))}return s.$$set=Z=>{"show"in Z&&t(0,l=Z.show),"options"in Z&&t(1,d=Z.options),"isLoading"in Z&&t(2,u=Z.isLoading),"errorMessage"in Z&&t(3,g=Z.errorMessage),"successMessage"in Z&&t(4,f=Z.successMessage)},s.$$.update=()=>{2&s.$$.dirty&&t(13,n=d.length>0?d[0]:null),8193&s.$$.dirty&&l&&t(5,$=n)},[l,d,u,g,f,$,c,h,r,S,M,I,function(Z){l&&!u&&(Z.key==="Escape"?(Z.preventDefault(),I()):Z.key==="Enter"&&$&&(Z.preventDefault(),M()))},n,Z=>S(Z),function(Z){h=Z,t(7,h)},function(Z){c=Z,i(t(6,c))}]}class Vf extends $e{constructor(e){super(),ve(this,e,Uf,Df,he,{show:0,options:1,isLoading:2,errorMessage:3,successMessage:4})}}function ni(s,e,t){const n=s.slice();return n[35]=e[t],n}function si(s,e,t){const n=s.slice();return n[38]=e[t],n}function qf(s){let e;return{c(){e=L("Rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Bf(s){let e;return{c(){e=L("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Jf(s){let e,t,n=[],r=new Map,a=_e(s[10]);const i=o=>o[38].path;for(let o=0;o<a.length;o+=1){let c=si(s,a,o),l=i(c);r.set(l,n[o]=ri(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Se()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){33792&c[0]&&(a=_e(o[10]),B(),n=jt(n,c,i,1,o,a,r,e.parentNode,Lt,ri,e,si),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Gf(s){let e,t,n;return t=new ie({props:{size:1,color:"neutral",$$slots:{default:[Qf]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),_(e,"class","c-rules-list-empty svelte-16sxavx")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};1024&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Wf(s){let e,t=s[38].path+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1024&r[0]&&t!==(t=n[38].path+"")&&fe(e,t)},d(n){n&&v(e)}}}function Hf(s){let e,t,n,r,a,i,o;return t=new co({}),a=new ie({props:{size:1,color:"neutral",$$slots:{default:[Wf]},$$scope:{ctx:s}}}),{c(){e=T("div"),w(t.$$.fragment),n=E(),r=T("div"),w(a.$$.fragment),i=E(),_(r,"class","c-rule-item-path svelte-16sxavx"),_(e,"class","c-rule-item-info svelte-16sxavx"),_(e,"slot","header-left")},m(c,l){y(c,e,l),x(t,e,null),k(e,n),k(e,r),x(a,r,null),k(e,i),o=!0},p(c,l){const d={};1024&l[0]|1024&l[1]&&(d.$$scope={dirty:l,ctx:c}),a.$set(d)},i(c){o||(p(t.$$.fragment,c),p(a.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(a.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(a)}}}function Kf(s){let e,t;return e=new Bo({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Yf(s){let e,t;return e=new No({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Xf(s){let e,t,n,r,a,i,o,c,l,d;function u(...g){return s[25](s[38],...g)}return r=new Ho({props:{path:s[38].path,onSave:u,alwaysApply:s[38].type===xr.ALWAYS_ATTACHED}}),i=new Me({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Kf]},$$scope:{ctx:s}}}),i.$on("click",function(...g){return s[26](s[38],...g)}),c=new Me({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Yf]},$$scope:{ctx:s}}}),c.$on("click",function(...g){return s[27](s[38],...g)}),{c(){e=T("div"),t=T("div"),n=T("div"),w(r.$$.fragment),a=E(),w(i.$$.fragment),o=E(),w(c.$$.fragment),l=E(),_(n,"class","c-rules-dropdown svelte-16sxavx"),_(t,"class","status-controls svelte-16sxavx"),_(e,"class","server-actions"),_(e,"slot","header-right")},m(g,f){y(g,e,f),k(e,t),k(t,n),x(r,n,null),k(t,a),x(i,t,null),k(t,o),x(c,t,null),k(e,l),d=!0},p(g,f){s=g;const $={};1024&f[0]&&($.path=s[38].path),1024&f[0]&&($.onSave=u),1024&f[0]&&($.alwaysApply=s[38].type===xr.ALWAYS_ATTACHED),r.$set($);const h={};1024&f[1]&&(h.$$scope={dirty:f,ctx:s}),i.$set(h);const S={};1024&f[1]&&(S.$$scope={dirty:f,ctx:s}),c.$set(S)},i(g){d||(p(r.$$.fragment,g),p(i.$$.fragment,g),p(c.$$.fragment,g),d=!0)},o(g){m(r.$$.fragment,g),m(i.$$.fragment,g),m(c.$$.fragment,g),d=!1},d(g){g&&v(e),b(r),b(i),b(c)}}}function ri(s,e){let t,n,r;return n=new zi({props:{isClickable:!0,$$slots:{"header-right":[Xf],"header-left":[Hf]},$$scope:{ctx:e}}}),n.$on("click",function(){return e[28](e[38])}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1024&i[0]|1024&i[1]&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function Qf(s){let e;return{c(){e=L("No rules files found")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function eh(s){let e,t,n,r,a;return t=new un({}),{c(){e=T("div"),w(t.$$.fragment),n=E(),r=T("span"),r.textContent="Create new rule file",_(e,"class","c-rules-actions-button-content svelte-16sxavx")},m(i,o){y(i,e,o),x(t,e,null),k(e,n),k(e,r),a=!0},p:H,i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&v(e),b(t)}}}function th(s){let e,t,n,r,a,i,o;return t=new fi({}),i=new wi({}),{c(){e=T("div"),w(t.$$.fragment),n=E(),r=T("span"),r.textContent="Import rules",a=E(),w(i.$$.fragment),_(e,"class","c-rules-actions-button-content svelte-16sxavx")},m(c,l){y(c,e,l),x(t,e,null),k(e,n),k(e,r),k(e,a),x(i,e,null),o=!0},p:H,i(c){o||(p(t.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(i)}}}function nh(s){let e,t;return e=new Me({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[th]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1024&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function sh(s){let e,t=s[35].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:H,d(n){n&&v(e)}}}function ai(s,e){let t,n,r;return n=new Te.Item({props:{onSelect:function(){return e[30](e[35])},$$slots:{default:[sh]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Se(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1024&i[1]&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function ii(s){let e,t,n,r;return e=new Te.Separator({}),n=new Te.Label({props:{$$slots:{default:[rh]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2048&i[0]|1024&i[1]&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function rh(s){let e,t=(s[11]!==void 0?s[19][s[11]].description:s[19][0])+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){2048&r[0]&&t!==(t=(n[11]!==void 0?n[19][n[11]].description:n[19][0])+"")&&fe(e,t)},d(n){n&&v(e)}}}function ah(s){let e,t,n,r=[],a=new Map,i=_e(s[19]);const o=l=>l[35].id;for(let l=0;l<i.length;l+=1){let d=ni(s,i,l),u=o(d);a.set(u,r[l]=ai(u,d))}let c=s[11]!==void 0&&ii(s);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();e=E(),c&&c.c(),t=Se()},m(l,d){for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(l,d);y(l,e,d),c&&c.m(l,d),y(l,t,d),n=!0},p(l,d){1572864&d[0]&&(i=_e(l[19]),B(),r=jt(r,d,o,1,l,i,a,e.parentNode,Lt,ai,e,ni),J()),l[11]!==void 0?c?(c.p(l,d),2048&d[0]&&p(c,1)):(c=ii(l),c.c(),p(c,1),c.m(t.parentNode,t)):c&&(B(),m(c,1,1,()=>{c=null}),J())},i(l){if(!n){for(let d=0;d<i.length;d+=1)p(r[d]);p(c),n=!0}},o(l){for(let d=0;d<r.length;d+=1)m(r[d]);m(c),n=!1},d(l){l&&(v(e),v(t));for(let d=0;d<r.length;d+=1)r[d].d(l);c&&c.d(l)}}}function ih(s){let e,t,n,r;return e=new Te.Trigger({props:{$$slots:{default:[nh]},$$scope:{ctx:s}}}),n=new Te.Content({props:{align:"start",side:"bottom",$$slots:{default:[ah]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=E(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};1024&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};2048&i[0]|1024&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function oh(s){let e;return{c(){e=L("User Guidelines")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ch(s){let e;return{c(){e=L("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function lh(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,S,M,I,Z,C,A,R,G,le,ke,W,j,Q,N,U,F,de,ze,He,ct,pn;n=new ie({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[qf]},$$scope:{ctx:s}}}),c=new ie({props:{size:1,weight:"regular",$$slots:{default:[Bf]},$$scope:{ctx:s}}});const Ft=[Gf,Jf],bt=[];function lr(X,ue){return X[10].length===0?0:1}function eo(X){s[31](X)}function to(X){s[32](X)}u=lr(s),g=bt[u]=Ft[u](s),h=new Me({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[eh]},$$scope:{ctx:s}}}),h.$on("click",s[29]);let Zs={$$slots:{default:[ih]},$$scope:{ctx:s}};return s[9]!==void 0&&(Zs.requestClose=s[9]),s[8]!==void 0&&(Zs.focusedIndex=s[8]),M=new Te.Root({props:Zs}),Ae.push(()=>Ne(M,"requestClose",eo)),Ae.push(()=>Ne(M,"focusedIndex",to)),R=new ie({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[oh]},$$scope:{ctx:s}}}),j=new ie({props:{size:1,weight:"regular",$$slots:{default:[ch]},$$scope:{ctx:s}}}),N=new Xi({props:{userGuidelines:s[0],userGuidelinesLengthLimit:s[1],updateUserGuideline:s[2]}}),F=new Sf({props:{show:s[12],errorMessage:s[13]}}),F.$on("create",s[23]),F.$on("cancel",s[24]),ze=new Vf({props:{show:s[3],options:s[4],isLoading:s[5],errorMessage:s[6],successMessage:s[7]}}),ze.$on("select",s[21]),ze.$on("cancel",s[22]),{c(){e=T("div"),t=T("div"),w(n.$$.fragment),r=E(),a=T("div"),i=L(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),o=T("a"),w(c.$$.fragment),l=E(),d=T("div"),g.c(),f=E(),$=T("div"),w(h.$$.fragment),S=E(),w(M.$$.fragment),C=E(),A=T("div"),w(R.$$.fragment),G=E(),le=T("div"),ke=L(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),W=T("a"),w(j.$$.fragment),Q=E(),w(N.$$.fragment),U=E(),w(F.$$.fragment),de=E(),w(ze.$$.fragment),_(o,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(o,"target","_blank"),_(d,"class","c-rules-list svelte-16sxavx"),_($,"class","c-rules-actions-container svelte-16sxavx"),_(t,"class","c-rules-section svelte-16sxavx"),_(W,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(W,"target","_blank"),_(A,"class","c-user-guidelines-section svelte-16sxavx"),_(e,"class","c-rules-category svelte-16sxavx")},m(X,ue){y(X,e,ue),k(e,t),x(n,t,null),k(t,r),k(t,a),k(a,i),k(a,o),x(c,o,null),k(t,l),k(t,d),bt[u].m(d,null),k(t,f),k(t,$),x(h,$,null),k($,S),x(M,$,null),k(e,C),k(e,A),x(R,A,null),k(A,G),k(A,le),k(le,ke),k(le,W),x(j,W,null),k(A,Q),x(N,A,null),y(X,U,ue),x(F,X,ue),y(X,de,ue),x(ze,X,ue),He=!0,ct||(pn=Le(window,"message",s[14].onMessageFromExtension),ct=!0)},p(X,ue){const dr={};1024&ue[1]&&(dr.$$scope={dirty:ue,ctx:X}),n.$set(dr);const ur={};1024&ue[1]&&(ur.$$scope={dirty:ue,ctx:X}),c.$set(ur);let Is=u;u=lr(X),u===Is?bt[u].p(X,ue):(B(),m(bt[Is],1,1,()=>{bt[Is]=null}),J(),g=bt[u],g?g.p(X,ue):(g=bt[u]=Ft[u](X),g.c()),p(g,1),g.m(d,null));const pr={};1024&ue[1]&&(pr.$$scope={dirty:ue,ctx:X}),h.$set(pr);const Yn={};2048&ue[0]|1024&ue[1]&&(Yn.$$scope={dirty:ue,ctx:X}),!I&&512&ue[0]&&(I=!0,Yn.requestClose=X[9],Ee(()=>I=!1)),!Z&&256&ue[0]&&(Z=!0,Yn.focusedIndex=X[8],Ee(()=>Z=!1)),M.$set(Yn);const mr={};1024&ue[1]&&(mr.$$scope={dirty:ue,ctx:X}),R.$set(mr);const fr={};1024&ue[1]&&(fr.$$scope={dirty:ue,ctx:X}),j.$set(fr);const Xn={};1&ue[0]&&(Xn.userGuidelines=X[0]),2&ue[0]&&(Xn.userGuidelinesLengthLimit=X[1]),4&ue[0]&&(Xn.updateUserGuideline=X[2]),N.$set(Xn);const Rs={};4096&ue[0]&&(Rs.show=X[12]),8192&ue[0]&&(Rs.errorMessage=X[13]),F.$set(Rs);const Kt={};8&ue[0]&&(Kt.show=X[3]),16&ue[0]&&(Kt.options=X[4]),32&ue[0]&&(Kt.isLoading=X[5]),64&ue[0]&&(Kt.errorMessage=X[6]),128&ue[0]&&(Kt.successMessage=X[7]),ze.$set(Kt)},i(X){He||(p(n.$$.fragment,X),p(c.$$.fragment,X),p(g),p(h.$$.fragment,X),p(M.$$.fragment,X),p(R.$$.fragment,X),p(j.$$.fragment,X),p(N.$$.fragment,X),p(F.$$.fragment,X),p(ze.$$.fragment,X),He=!0)},o(X){m(n.$$.fragment,X),m(c.$$.fragment,X),m(g),m(h.$$.fragment,X),m(M.$$.fragment,X),m(R.$$.fragment,X),m(j.$$.fragment,X),m(N.$$.fragment,X),m(F.$$.fragment,X),m(ze.$$.fragment,X),He=!1},d(X){X&&(v(e),v(U),v(de)),b(n),b(c),bt[u].d(),b(h),b(M),b(R),b(j),b(N),b(F,X),b(ze,X),ct=!1,pn()}}}function dh(s,e,t){let n,r,a,i,o=H,c=()=>(o(),o=ks(G,W=>t(11,r=W)),G);s.$$.on_destroy.push(()=>o());let{userGuidelines:l=""}=e,{userGuidelinesLengthLimit:d}=e,{updateUserGuideline:u=()=>!1}=e;const g=new hi(je),f=new sf(g),$=new rf(je,g,f);g.registerConsumer(f);const h=f.getRulesFiles();lt(s,h,W=>t(10,n=W));const S=$.getShowCreateRuleDialog();lt(s,S,W=>t(12,a=W));const M=$.getCreateRuleError();lt(s,M,W=>t(13,i=W));let I=!1,Z=[],C=!1,A="",R="",G;c();let le=()=>{};async function ke(W){try{W.id==="select_file_or_directory"?await $.selectFileToImport():W.id==="auto_import"&&await async function(){try{t(6,A=""),t(7,R="");const j=await $.getAutoImportOptions();t(4,Z=j.data.options),t(3,I=!0)}catch(j){console.error("Failed to get auto-import options:",j),t(6,A="Failed to detect existing rules in workspace.")}}()}catch(j){console.error("Failed to handle import select:",j)}le&&le()}return di(()=>{f.requestRules()}),s.$$set=W=>{"userGuidelines"in W&&t(0,l=W.userGuidelines),"userGuidelinesLengthLimit"in W&&t(1,d=W.userGuidelinesLengthLimit),"updateUserGuideline"in W&&t(2,u=W.updateUserGuideline)},[l,d,u,I,Z,C,A,R,G,le,n,r,a,i,g,$,h,S,M,[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],ke,async function(W){const j=W.detail;try{t(5,C=!0),t(6,A="");const Q=await $.processAutoImportSelection(j);let N=`Successfully imported ${Q.importedRulesCount} rule${Q.importedRulesCount!==1?"s":""} from ${j.label}`;Q.duplicatesCount>0&&(N+=`, ${Q.duplicatesCount} duplicate${Q.duplicatesCount!==1?"s":""} skipped`),Q.totalAttempted>Q.importedRulesCount+Q.duplicatesCount&&(N+=`, ${Q.totalAttempted-Q.importedRulesCount-Q.duplicatesCount} failed`),t(7,R=N),setTimeout(()=>{t(3,I=!1),t(7,R="")},500)}catch(Q){console.error("Failed to process auto-import selection:",Q),t(6,A="Failed to import rules. Please try again.")}finally{t(5,C=!1)}},function(){t(3,I=!1),t(6,A=""),t(7,R="")},function(W){$.handleCreateRuleWithName(W.detail)},function(){$.hideCreateRuleDialog()},(W,j)=>{$.updateRuleType(`${$i}/${vi}/${W.path}`,W.content,j)},(W,j)=>{j.stopPropagation(),$.openRule(W.path)},(W,j)=>{j.stopPropagation(),$.deleteRule(W.path)},W=>$.openRule(W.path),()=>$.createRule(),W=>ke(W),function(W){le=W,t(9,le)},function(W){G=W,c(t(8,G))}]}class uh extends $e{constructor(e){super(),ve(this,e,dh,lh,he,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2},null,[-1,-1])}}function ph(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Fe(r,n[a]);return{c(){e=et("svg"),t=new er(!0),this.h()},l(a){e=tr(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=nr(e);t=sr(i,!0),i.forEach(v),this.h()},h(){t.a=null,nn(e,r)},m(a,i){rr(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',e)},p(a,[i]){nn(e,r=Vt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:H,o:H,d(a){a&&v(e)}}}function mh(s,e,t){return s.$$set=n=>{t(0,e=Fe(Fe({},e),Mt(n)))},[e=Mt(e)]}class fh extends $e{constructor(e){super(),ve(this,e,mh,ph,he,{})}}function hh(s){let e;return{c(){e=L("Sign Out")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gh(s){let e,t;return e=new fh({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function $h(s){let e,t;return e=new Me({props:{loading:s[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[gh],default:[hh]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.loading=n[0]),8&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function vh(s,e,t){let{onSignOut:n}=e,r=!1;return s.$$set=a=>{"onSignOut"in a&&t(2,n=a.onSignOut)},[r,function(){n(),t(0,r=!0)},n]}class yh extends $e{constructor(e){super(),ve(this,e,vh,$h,he,{onSignOut:2})}}function _h(s){let e,t;return e=new Ym({props:{tools:s[6],onAuthenticate:s[18],onRevokeAccess:s[19],servers:s[7],onMCPServerAdd:s[24],onMCPServerSave:s[25],onMCPServerDelete:s[26],onMCPServerToggleDisable:s[27],onMCPServerJSONImport:s[28],isMCPEnabled:s[8]&&s[2].mcpServerList,isMCPImportEnabled:s[8]&&s[2].mcpServerImport,supportedShells:s[9].supportedShells,selectedShell:s[9].selectedShell,startupScript:s[9].startupScript,onShellSelect:s[20],onStartupScriptChange:s[21],isTerminalEnabled:s[2].terminal}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};64&r[0]&&(a.tools=n[6]),128&r[0]&&(a.servers=n[7]),260&r[0]&&(a.isMCPEnabled=n[8]&&n[2].mcpServerList),260&r[0]&&(a.isMCPImportEnabled=n[8]&&n[2].mcpServerImport),512&r[0]&&(a.supportedShells=n[9].supportedShells),512&r[0]&&(a.selectedShell=n[9].selectedShell),512&r[0]&&(a.startupScript=n[9].startupScript),4&r[0]&&(a.isTerminalEnabled=n[2].terminal),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function wh(s){let e,t;return e=new yh({props:{onSignOut:s[22]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function xh(s){let e,t,n,r;const a=[Ch,kh],i=[];function o(c,l){return c[2].rules?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Se()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function bh(s){let e,t;return e=new Pl({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:H,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Sh(s){return{c:H,m:H,p:H,i:H,o:H,d:H}}function kh(s){let e,t;return e=new Xi({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ch(s){let e,t;return e=new uh({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Th(s){let e,t,n,r,a;const i=[Sh,bh,xh,wh,_h],o=[];function c(l,d){var u,g,f;return 8&d[1]&&(t=null),t==null&&(t=!Di(l[34])),t?0:((u=l[34])==null?void 0:u.id)==="context"?1:((g=l[34])==null?void 0:g.id)==="guidelines"?2:((f=l[34])==null?void 0:f.id)==="account"?3:4}return n=c(s,[-1,-1]),r=o[n]=i[n](s),{c(){e=T("span"),r.c(),_(e,"slot","content")},m(l,d){y(l,e,d),o[n].m(e,null),a=!0},p(l,d){let u=n;n=c(l,d),n===u?o[n].p(l,d):(B(),m(o[u],1,1,()=>{o[u]=null}),J(),r=o[n],r?r.p(l,d):(r=o[n]=i[n](l),r.c()),p(r,1),r.m(e,null))},i(l){a||(p(r),a=!0)},o(l){m(r),a=!1},d(l){l&&v(e),o[n].d()}}}function Mh(s){let e,t;return e=new ld({props:{items:s[1],mode:"tree",class:"c-settings-navigation",selectedId:s[0],$$slots:{content:[Th,({item:n})=>({34:n}),({item:n})=>[0,n?8:0]]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r[0]&&(a.items=n[1]),1&r[0]&&(a.selectedId=n[0]),1012&r[0]|24&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ah(s){let e,t,n,r;return e=new Vo.Root({props:{$$slots:{default:[Mh]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"message",s[11].onMessageFromExtension),n=!0)},p(a,i){const o={};1015&i[0]|16&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}function Nh(s,e,t){let n,r,a,i,o,c,l,d,u,g,f=H;s.$$.on_destroy.push(()=>f());const $=new Xo(je),h=new al(je),S=new il(je),M=new hi(je),I=new Zo,Z=new Jo(je,M,I),C=$.getSettingsComponentSupported();lt(s,C,j=>t(2,o=j));const A=$.getEnableAgentMode();lt(s,A,j=>t(8,u=j)),M.registerConsumer($),M.registerConsumer(h),M.registerConsumer(S);const R=S.getTerminalSettings();let G;lt(s,R,j=>t(9,g=j));const le={handleMessageFromExtension:j=>!(!j.data||j.data.type!==ge.navigateToSettingsSection)&&(j.data.data&&typeof j.data.data=="string"&&t(0,G=j.data.data),!0)};M.registerConsumer(le);const ke=$.getDisplayableTools();lt(s,ke,j=>t(6,l=j));const W=$.getGuidelines();return lt(s,W,j=>t(23,c=j)),ui(()=>{$.dispose()}),$.notifyLoaded(),je.postMessage({type:ge.getOrientationStatus}),je.postMessage({type:ge.settingsPanelLoaded}),s.$$.update=()=>{var j,Q,N;8388608&s.$$.dirty[0]&&t(5,n=(j=c.userGuidelines)==null?void 0:j.contents),8388608&s.$$.dirty[0]&&t(4,r=(Q=c.userGuidelines)==null?void 0:Q.lengthLimit),4&s.$$.dirty[0]&&t(1,i=[o.remoteTools?es("Tools","",ud,"section-tools"):void 0,o.userGuidelines&&!o.rules?es("User Guidelines","Guidelines for Augment Chat to follow.",lo,"guidelines"):void 0,o.rules?es("Rules and User Guidelines","",ef,"guidelines"):void 0,o.workspaceContext?{name:"Context",description:"",icon:md,id:"context"}:void 0,es("Account","Manage your Augment account settings.",Eo,"account")].filter(Boolean)),3&s.$$.dirty[0]&&i.length>1&&!G&&t(0,G=(N=i[0])==null?void 0:N.id)},t(3,a=h.getServers()),f(),f=ks(a,j=>t(7,d=j)),[G,i,o,a,r,n,l,d,u,g,h,M,C,A,R,ke,W,function(j){const Q=j.trim();return!(r&&Q.length>r)&&($.updateLocalUserGuidelines(Q),je.postMessage({type:ge.updateUserGuidelines,data:j}),!0)},function(j){je.postMessage({type:ge.toolConfigStartOAuth,data:{authUrl:j}}),$.startPolling()},async function(j){await Z.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${j.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&je.postMessage({type:ge.toolConfigRevokeAccess,data:{toolId:j.identifier}})},function(j){S.updateSelectedShell(j)},function(j){S.updateStartupScript(j)},function(){je.postMessage({type:ge.signOut})},c,j=>h.addServer(j),j=>h.updateServer(j),j=>h.deleteServer(j),j=>h.toggleDisabledServer(j),j=>h.importServersFromJSON(j)]}class Eh extends $e{constructor(e){super(),ve(this,e,Nh,Ah,he,{},null,[-1,-1])}}(async function(){je&&je.initialize&&await je.initialize(),new Eh({target:document.getElementById("app")})})();
