var kr=Object.defineProperty;var Rn=r=>{throw TypeError(r)};var br=(r,t,e)=>t in r?kr(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var u=(r,t,e)=>br(r,typeof t!="symbol"?t+"":t,e),Ws=(r,t,e)=>t.has(r)||Rn("Cannot "+e);var h=(r,t,e)=>(Ws(r,t,"read from private field"),e?e.call(r):t.get(r)),q=(r,t,e)=>t.has(r)?Rn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),A=(r,t,e,s)=>(Ws(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),T=(r,t,e)=>(Ws(r,t,"access private method"),e);var as=(r,t,e,s)=>({set _(n){A(r,t,n,e)},get _(){return h(r,t,s)}});import{t as xr,u as wt,a as Ke,m as Dn,C as Cr,n as Tr,o as Un,v as we,w as Ne,x as ys,y as ws}from"./types-DwxhLPcD.js";import{S as Sr,W as it,e as Re,u as Pi,o as Hi}from"./BaseButton-ESlFPUk1.js";import{g as Tt,n as ke,E as ht,q as Se,k as Be,r as qe,S as xe,C as Ce,s as Bi,m as $r,t as js,u as Er,v as Ln,w as Fn,x as Ar,y as On,z as Mr,B as Nn,D as Ir,F as qn,G as Vs,H as Rr}from"./arrow-up-right-from-square-ChzPb9WB.js";import{S as W,i as j,s as V,a as P,b as J,H as Jt,w as Kt,x as Xt,y as te,h as E,d as dt,z as ee,g as Ut,n as R,j as rt,N as st,al as z,P as Gi,ak as Te,aD as zn,c as v,e as U,f as tt,W as et,J as Gt,u as x,q as kt,t as S,r as bt,T as Wi,E as L,F,I as O,K as Xe,M as kn,aA as De,ac as ls,$ as Ge,a3 as Pn,a0 as We,a1 as je,a2 as Ve,Y as $t,a8 as Vt,aa as Dr,_ as Ur,A as se,ab as K,C as Qt,D as Ue,G as Le,Z as ks,ag as bs,a4 as bn,L as Lr,ao as Fr,am as ji,B as Or,aj as Nr}from"./SpinnerAugment-CQKp6jSN.js";import{C as X,P as Dt,b as Zs,I as Yt,a as Bt,E as qr,L as Ys}from"./chat-types-DOHETl9Q.js";import{g as Hn,f as zr,D as Pr,h as Hr,i as Br,U as Gr,d as Wr,e as Vi,C as jr,T as Vr}from"./github-CwfQWdpa.js";import{d as Zr,T as Ps}from"./Content-D7Q35t53.js";import{F as Yr,s as Qr,P as Jr}from"./folder-opened-Bb7oiaOR.js";import{T as le,a as Bn,b as Qs}from"./check-DlU29TPV.js";import{C as Kr}from"./types-CGlLNakm.js";import{I as ns}from"./IconButtonAugment-D-fvrWAT.js";import{C as Xr,D as xs,T as to}from"./index-DvKVcjj3.js";import{_ as eo,a as so,i as no,b as xn}from"./isObjectLike-BYlJR0wA.js";import{T as io}from"./TextAreaAugment-b9B2aQlO.js";import{T as Cn}from"./TextTooltipAugment--NM_J2iY.js";import{e as ro,f as oo,b as Gn,i as ao,d as lo}from"./diff-utils-Dvc7ppQm.js";function co(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function ho(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class uo extends W{constructor(t){super(),j(this,t,ho,co,V,{})}}function po(r){var t;return((t=r.extraData)==null?void 0:t.isAutofix)===!0}function $e(r){var t;return((t=r.extraData)==null?void 0:t.isAgentConversation)===!0}var at=(r=>(r[r.active=0]="active",r[r.inactive=1]="inactive",r))(at||{}),fo=(r=>(r.normal="Normal",r.autofixCommand="AutofixCommand",r.autofixPrompt="AutofixPrompt",r))(fo||{});function go(r,t,e=1e3){let s=null,n=0;const i=st(t),o=()=>{const a=(()=>{const l=Date.now();if(s!==null&&l-n<e)return s;const c=r();return s=c,n=l,c})();i.set(a)};return{subscribe:i.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Zi=(r=>(r[r.unset=0]="unset",r[r.positive=1]="positive",r[r.negative=2]="negative",r))(Zi||{});const yt="__NEW_AGENT__";function mo(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let ve={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Wn(r){ve=r}const Yi=/[&<>"']/,_o=new RegExp(Yi.source,"g"),Qi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,vo=new RegExp(Qi.source,"g"),yo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},jn=r=>yo[r];function Rt(r,t){if(t){if(Yi.test(r))return r.replace(_o,jn)}else if(Qi.test(r))return r.replace(vo,jn);return r}const wo=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ko(r){return r.replace(wo,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const bo=/(^|[^\[])\^/g;function G(r,t){let e=typeof r=="string"?r:r.source;t=t||"";const s={replace:(n,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(bo,"$1"),e=e.replace(n,o),s},getRegex:()=>new RegExp(e,t)};return s}function Vn(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const Ze={exec:()=>null};function Zn(r,t){const e=r.replace(/\|/g,(n,i,o)=>{let a=!1,l=i;for(;--l>=0&&o[l]==="\\";)a=!a;return a?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function cs(r,t,e){const s=r.length;if(s===0)return"";let n=0;for(;n<s;){const i=r.charAt(s-n-1);if(i!==t||e){if(i===t||!e)break;n++}else n++}return r.slice(0,s-n)}function Yn(r,t,e,s){const n=t.href,i=t.title?Rt(t.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){s.state.inLink=!0;const a={type:"link",raw:e,href:n,title:i,text:o,tokens:s.inlineTokens(o)};return s.state.inLink=!1,a}return{type:"image",raw:e,href:n,title:i,text:Rt(o)}}class Cs{constructor(t){u(this,"options");u(this,"rules");u(this,"lexer");this.options=t||ve}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:cs(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],n=function(i,o){const a=i.match(/^(\s+)(?:```)/);if(a===null)return o;const l=a[1];return o.split(`
`).map(c=>{const d=c.match(/^\s+/);if(d===null)return c;const[p]=d;return p.length>=l.length?c.slice(l.length):c}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const n=cs(s,"#");this.options.pedantic?s=n.trim():n&&!/ $/.test(n)||(s=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=cs(e[0].replace(/^ *>[ \t]?/gm,""),`
`),n=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(s);return this.lexer.state.top=n,{type:"blockquote",raw:e[0],tokens:i,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const n=s.length>1,i={type:"list",raw:"",ordered:n,start:n?+s.slice(0,-1):"",loose:!1,items:[]};s=n?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=n?s:"[*+-]");const o=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",c=!1;for(;t;){let d=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let p=e[2].split(`
`,1)[0].replace(/^\t+/,k=>" ".repeat(3*k.length)),f=t.split(`
`,1)[0],m=0;this.options.pedantic?(m=2,l=p.trimStart()):(m=e[2].search(/[^ ]/),m=m>4?1:m,l=p.slice(m),m+=e[1].length);let _=!1;if(!p&&/^ *$/.test(f)&&(a+=f+`
`,t=t.substring(f.length+1),d=!0),!d){const k=new RegExp(`^ {0,${Math.min(3,m-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),y=new RegExp(`^ {0,${Math.min(3,m-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),M=new RegExp(`^ {0,${Math.min(3,m-1)}}(?:\`\`\`|~~~)`),D=new RegExp(`^ {0,${Math.min(3,m-1)}}#`);for(;t;){const w=t.split(`
`,1)[0];if(f=w,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),M.test(f)||D.test(f)||k.test(f)||y.test(t))break;if(f.search(/[^ ]/)>=m||!f.trim())l+=`
`+f.slice(m);else{if(_||p.search(/[^ ]/)>=4||M.test(p)||D.test(p)||y.test(p))break;l+=`
`+f}_||f.trim()||(_=!0),a+=w+`
`,t=t.substring(w.length+1),p=f.slice(m)}}i.loose||(c?i.loose=!0:/\n *\n *$/.test(a)&&(c=!0));let g,C=null;this.options.gfm&&(C=/^\[[ xX]\] /.exec(l),C&&(g=C[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:a,task:!!C,checked:g,loose:!1,text:l,tokens:[]}),i.raw+=a}i.items[i.items.length-1].raw=a.trimEnd(),i.items[i.items.length-1].text=l.trimEnd(),i.raw=i.raw.trimEnd();for(let d=0;d<i.items.length;d++)if(this.lexer.state.top=!1,i.items[d].tokens=this.lexer.blockTokens(i.items[d].text,[]),!i.loose){const p=i.items[d].tokens.filter(m=>m.type==="space"),f=p.length>0&&p.some(m=>/\n.*\n/.test(m.raw));i.loose=f}if(i.loose)for(let d=0;d<i.items.length;d++)i.items[d].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),n=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:n,title:i}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=Zn(e[1]),n=e[2].replace(/^\||\| *$/g,"").split("|"),i=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===n.length){for(const a of n)/^ *-+: *$/.test(a)?o.align.push("right"):/^ *:-+: *$/.test(a)?o.align.push("center"):/^ *:-+ *$/.test(a)?o.align.push("left"):o.align.push(null);for(const a of s)o.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of i)o.rows.push(Zn(a,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:Rt(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const o=cs(s.slice(0,-1),"\\");if((s.length-o.length)%2==0)return}else{const o=function(a,l){if(a.indexOf(l[1])===-1)return-1;let c=0;for(let d=0;d<a.length;d++)if(a[d]==="\\")d++;else if(a[d]===l[0])c++;else if(a[d]===l[1]&&(c--,c<0))return d;return-1}(e[2],"()");if(o>-1){const a=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let n=e[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);o&&(n=o[1],i=o[3])}else i=e[3]?e[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(s)?n.slice(1):n.slice(1,-1)),Yn(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const n=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!n){const i=s[0].charAt(0);return{type:"text",raw:i,text:i}}return Yn(s,n,s[0],this.lexer)}}emStrong(t,e,s=""){let n=this.rules.inline.emStrongLDelim.exec(t);if(n&&!(n[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(n[1]||n[2])||!s||this.rules.inline.punctuation.exec(s))){const i=[...n[0]].length-1;let o,a,l=i,c=0;const d=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(d.lastIndex=0,e=e.slice(-1*t.length+i);(n=d.exec(e))!=null;){if(o=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!o)continue;if(a=[...o].length,n[3]||n[4]){l+=a;continue}if((n[5]||n[6])&&i%3&&!((i+a)%3)){c+=a;continue}if(l-=a,l>0)continue;a=Math.min(a,a+l+c);const p=[...n[0]][0].length,f=t.slice(0,i+n.index+p+a);if(Math.min(i,a)%2){const _=f.slice(1,-1);return{type:"em",raw:f,text:_,tokens:this.lexer.inlineTokens(_)}}const m=f.slice(2,-2);return{type:"strong",raw:f,text:m,tokens:this.lexer.inlineTokens(m)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const n=/[^ ]/.test(s),i=/^ /.test(s)&&/ $/.test(s);return n&&i&&(s=s.substring(1,s.length-1)),s=Rt(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,n;return e[2]==="@"?(s=Rt(e[1]),n="mailto:"+s):(s=Rt(e[1]),n=s),{type:"link",raw:e[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let n,i;if(e[2]==="@")n=Rt(e[0]),i="mailto:"+n;else{let o;do o=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(o!==e[0]);n=Rt(e[0]),i=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:Rt(e[0]),{type:"text",raw:e[0],text:s}}}}const is=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Ji=/(?:[*+-]|\d{1,9}[.)])/,Ki=G(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Ji).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),Tn=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Sn=/(?!\s*\])(?:\\.|[^\[\]\\])+/,xo=G(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Sn).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Co=G(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Ji).getRegex(),Hs="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",$n=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,To=G("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",$n).replace("tag",Hs).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Qn=G(Tn).replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hs).getRegex(),En={blockquote:G(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Qn).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:xo,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:is,html:To,lheading:Ki,list:Co,newline:/^(?: *(?:\n|$))+/,paragraph:Qn,table:Ze,text:/^[^\n]+/},Jn=G("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hs).getRegex(),So={...En,table:Jn,paragraph:G(Tn).replace("hr",is).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Jn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Hs).getRegex()},$o={...En,html:G(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",$n).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ze,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:G(Tn).replace("hr",is).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ki).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Xi=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,tr=/^( {2,}|\\)\n(?!\s*$)/,rs="\\p{P}\\p{S}",Eo=G(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,rs).getRegex(),Ao=G(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,rs).getRegex(),Mo=G("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,rs).getRegex(),Io=G("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,rs).getRegex(),Ro=G(/\\([punct])/,"gu").replace(/punct/g,rs).getRegex(),Do=G(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Uo=G($n).replace("(?:-->|$)","-->").getRegex(),Lo=G("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Uo).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ts=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Fo=G(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Ts).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Kn=G(/^!?\[(label)\]\[(ref)\]/).replace("label",Ts).replace("ref",Sn).getRegex(),Xn=G(/^!?\[(ref)\](?:\[\])?/).replace("ref",Sn).getRegex(),An={_backpedal:Ze,anyPunctuation:Ro,autolink:Do,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:tr,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Ze,emStrongLDelim:Ao,emStrongRDelimAst:Mo,emStrongRDelimUnd:Io,escape:Xi,link:Fo,nolink:Xn,punctuation:Eo,reflink:Kn,reflinkSearch:G("reflink|nolink(?!\\()","g").replace("reflink",Kn).replace("nolink",Xn).getRegex(),tag:Lo,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Ze},Oo={...An,link:G(/^!?\[(label)\]\((.*?)\)/).replace("label",Ts).getRegex(),reflink:G(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ts).getRegex()},on={...An,escape:G(Xi).replace("])","~|])").getRegex(),url:G(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},No={...on,br:G(tr).replace("{2,}","*").getRegex(),text:G(on.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},hs={normal:En,gfm:So,pedantic:$o},ze={normal:An,gfm:on,breaks:No,pedantic:Oo};class Wt{constructor(t){u(this,"tokens");u(this,"options");u(this,"state");u(this,"tokenizer");u(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||ve,this.options.tokenizer=this.options.tokenizer||new Cs,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:hs.normal,inline:ze.normal};this.options.pedantic?(e.block=hs.pedantic,e.inline=ze.pedantic):this.options.gfm&&(e.block=hs.gfm,this.options.breaks?e.inline=ze.breaks:e.inline=ze.gfm),this.tokenizer.rules=e}static get rules(){return{block:hs,inline:ze}}static lex(t,e){return new Wt(e).lex(t)}static lexInline(t,e){return new Wt(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,n,i,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(a,l,c)=>l+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(s=a.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?e.push(s):(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(n.raw+=`
`+s.raw,n.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(i=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let c;this.options.extensions.startBlock.forEach(d=>{c=d.call({lexer:this},l),typeof c=="number"&&c>=0&&(a=Math.min(a,c))}),a<1/0&&a>=0&&(i=t.substring(0,a+1))}if(this.state.top&&(s=this.tokenizer.paragraph(i)))n=e[e.length-1],o&&n.type==="paragraph"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s),o=i.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&n.type==="text"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,n,i,o,a,l,c=t;if(this.tokens.links){const d=Object.keys(this.tokens.links);if(d.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)d.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,o.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(a||(l=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(d=>!!(s=d.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,c,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(i=t,this.options.extensions&&this.options.extensions.startInline){let d=1/0;const p=t.slice(1);let f;this.options.extensions.startInline.forEach(m=>{f=m.call({lexer:this},p),typeof f=="number"&&f>=0&&(d=Math.min(d,f))}),d<1/0&&d>=0&&(i=t.substring(0,d+1))}if(s=this.tokenizer.inlineText(i))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),a=!0,n=e[e.length-1],n&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}throw new Error(d)}}else t=t.substring(s.raw.length),e.push(s);return e}}class Ss{constructor(t){u(this,"options");this.options=t||ve}code(t,e,s){var i;const n=(i=(e||"").match(/^\S*/))==null?void 0:i[0];return t=t.replace(/\n$/,"")+`
`,n?'<pre><code class="language-'+Rt(n)+'">'+(s?t:Rt(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:Rt(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const n=e?"ol":"ul";return"<"+n+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+n+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const n=Vn(t);if(n===null)return s;let i='<a href="'+(t=n)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+s+"</a>",i}image(t,e,s){const n=Vn(t);if(n===null)return s;let i=`<img src="${t=n}" alt="${s}"`;return e&&(i+=` title="${e}"`),i+=">",i}text(t){return t}}class Mn{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class jt{constructor(t){u(this,"options");u(this,"renderer");u(this,"textRenderer");this.options=t||ve,this.options.renderer=this.options.renderer||new Ss,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Mn}static parse(t,e){return new jt(e).parse(t)}static parseInline(t,e){return new jt(e).parseInline(t)}parse(t,e=!0){let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){s+=a||"";continue}}switch(i.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const o=i;s+=this.renderer.heading(this.parseInline(o.tokens),o.depth,ko(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;s+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let a="",l="";for(let d=0;d<o.header.length;d++)l+=this.renderer.tablecell(this.parseInline(o.header[d].tokens),{header:!0,align:o.align[d]});a+=this.renderer.tablerow(l);let c="";for(let d=0;d<o.rows.length;d++){const p=o.rows[d];l="";for(let f=0;f<p.length;f++)l+=this.renderer.tablecell(this.parseInline(p[f].tokens),{header:!1,align:o.align[f]});c+=this.renderer.tablerow(l)}s+=this.renderer.table(a,c);continue}case"blockquote":{const o=i,a=this.parse(o.tokens);s+=this.renderer.blockquote(a);continue}case"list":{const o=i,a=o.ordered,l=o.start,c=o.loose;let d="";for(let p=0;p<o.items.length;p++){const f=o.items[p],m=f.checked,_=f.task;let g="";if(f.task){const C=this.renderer.checkbox(!!m);c?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=C+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=C+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:C+" "}):g+=C+" "}g+=this.parse(f.tokens,c),d+=this.renderer.listitem(g,_,!!m)}s+=this.renderer.list(d,a,l);continue}case"html":{const o=i;s+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;s+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,a=o.tokens?this.parseInline(o.tokens):o.text;for(;n+1<t.length&&t[n+1].type==="text";)o=t[++n],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);s+=e?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){s+=o||"";continue}}switch(i.type){case"escape":{const o=i;s+=e.text(o.text);break}case"html":{const o=i;s+=e.html(o.text);break}case"link":{const o=i;s+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=i;s+=e.image(o.href,o.title,o.text);break}case"strong":{const o=i;s+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=i;s+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=i;s+=e.codespan(o.text);break}case"br":s+=e.br();break;case"del":{const o=i;s+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=i;s+=e.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}}class Ye{constructor(t){u(this,"options");this.options=t||ve}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}u(Ye,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var _e,an,er,Oi;const ue=new(Oi=class{constructor(...r){q(this,_e);u(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});u(this,"options",this.setOptions);u(this,"parse",T(this,_e,an).call(this,Wt.lex,jt.parse));u(this,"parseInline",T(this,_e,an).call(this,Wt.lexInline,jt.parseInline));u(this,"Parser",jt);u(this,"Renderer",Ss);u(this,"TextRenderer",Mn);u(this,"Lexer",Wt);u(this,"Tokenizer",Cs);u(this,"Hooks",Ye);this.use(...r)}walkTokens(r,t){var s,n;let e=[];for(const i of r)switch(e=e.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const a of o.header)e=e.concat(this.walkTokens(a.tokens,t));for(const a of o.rows)for(const l of a)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const o=i;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=i;(n=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&n[o.type]?this.defaults.extensions.childTokens[o.type].forEach(a=>{const l=o[a].flat(1/0);e=e.concat(this.walkTokens(l,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...r){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const i=t.renderers[n.name];t.renderers[n.name]=i?function(...o){let a=n.renderer.apply(this,o);return a===!1&&(a=i.apply(this,o)),a}:n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[n.level];i?i.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),s.extensions=t),e.renderer){const n=this.defaults.renderer||new Ss(this.defaults);for(const i in e.renderer){if(!(i in n))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.renderer[o],l=n[o];n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d||""}}s.renderer=n}if(e.tokenizer){const n=this.defaults.tokenizer||new Cs(this.defaults);for(const i in e.tokenizer){if(!(i in n))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,a=e.tokenizer[o],l=n[o];n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d}}s.tokenizer=n}if(e.hooks){const n=this.defaults.hooks||new Ye;for(const i in e.hooks){if(!(i in n))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.hooks[o],l=n[o];Ye.passThroughHooks.has(i)?n[o]=c=>{if(this.defaults.async)return Promise.resolve(a.call(n,c)).then(p=>l.call(n,p));const d=a.call(n,c);return l.call(n,d)}:n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d}}s.hooks=n}if(e.walkTokens){const n=this.defaults.walkTokens,i=e.walkTokens;s.walkTokens=function(o){let a=[];return a.push(i.call(this,o)),n&&(a=a.concat(n.call(this,o))),a}}this.defaults={...this.defaults,...s}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,t){return Wt.lex(r,t??this.defaults)}parser(r,t){return jt.parse(r,t??this.defaults)}},_e=new WeakSet,an=function(r,t){return(e,s)=>{const n={...s},i={...this.defaults,...n};this.defaults.async===!0&&n.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=T(this,_e,er).call(this,!!i.silent,!!i.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then(a=>r(a,i)).then(a=>i.hooks?i.hooks.processAllTokens(a):a).then(a=>i.walkTokens?Promise.all(this.walkTokens(a,i.walkTokens)).then(()=>a):a).then(a=>t(a,i)).then(a=>i.hooks?i.hooks.postprocess(a):a).catch(o);try{i.hooks&&(e=i.hooks.preprocess(e));let a=r(e,i);i.hooks&&(a=i.hooks.processAllTokens(a)),i.walkTokens&&this.walkTokens(a,i.walkTokens);let l=t(a,i);return i.hooks&&(l=i.hooks.postprocess(l)),l}catch(a){return o(a)}}},er=function(r,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const s="<p>An error occurred:</p><pre>"+Rt(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},Oi);function B(r,t){return ue.parse(r,t)}B.options=B.setOptions=function(r){return ue.setOptions(r),B.defaults=ue.defaults,Wn(B.defaults),B},B.getDefaults=mo,B.defaults=ve,B.use=function(...r){return ue.use(...r),B.defaults=ue.defaults,Wn(B.defaults),B},B.walkTokens=function(r,t){return ue.walkTokens(r,t)},B.parseInline=ue.parseInline,B.Parser=jt,B.parser=jt.parse,B.Renderer=Ss,B.TextRenderer=Mn,B.Lexer=Wt,B.lexer=Wt.lex,B.Tokenizer=Cs,B.Hooks=Ye,B.parse=B,B.options,B.setOptions,B.use,B.walkTokens,B.parseInline,jt.parse,Wt.lex;const Xc=async(r,t)=>{if(!$e(r)||t.chatItemType!==void 0||!(t!=null&&t.request_message))return;const e=xr.create();e.setFlag(wt.start);try{await qo(r,t,e)}catch(s){e.setFlag(wt.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{e.setFlag(wt.end),r.extensionClient.reportAgentSessionEvent({eventName:Ke.classifyAndDistill,conversationId:r.id,eventData:{classifyAndDistillData:e}})}},qo=async(r,t,e)=>{const s=crypto.randomUUID();e.setRequestId(wt.memoriesRequestId,s);const n=z(r).id;e.setFlag(wt.startSendSilentExchange);const{responseText:i,requestId:o}=await r.sendSilentExchange({model_id:r.selectedModelId??void 0,request_message:t.request_message,disableRetrieval:!0,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(e.setStringStats(wt.sendSilentExchangeResponseStats,i),o?e.setRequestId(wt.sendSilentExchangeRequestId,o):e.setFlag(wt.noRequestId),z(r).id!==n)return void e.setFlag(wt.conversationChanged);let a;try{let c=i;try{const d=B.lexer(i);d.length===1&&d[0].type==="code"&&d[0].text&&(c=d[0].text)}catch(d){console.warn("Markdown lexing failed during response parsing, attempting to parse as raw string:",d)}a=JSON.parse(c)}catch{throw e.setFlag(wt.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof a.explanation!="string"||typeof a.content!="string"||typeof a.worthRemembering!="boolean")throw e.setFlag(wt.invalidResponse),new Error("Invalid response from classify and distill");e.setStringStats(wt.explanationStats,a.explanation),e.setStringStats(wt.contentStats,a.content),e.setFlag(wt.worthRemembering,a.worthRemembering);const l=a.worthRemembering?a.content:void 0;l&&Po(r,l,s,e)},th=r=>{var s;const t=r.chatHistory.at(-1);if(!t||!Tt(t))return ke.notRunning;if(!(t.status===ht.success||t.status===ht.failed||t.status===ht.cancelled))return ke.running;const e=(((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===X.TOOL_USE&&!!n.tool_use))??[]).at(-1);if(!e)return ke.notRunning;switch(r.getToolUseState(t.request_id,e.tool_use.tool_use_id).phase){case le.runnable:return ke.awaitingUserAction;case le.cancelled:return ke.notRunning;default:return ke.running}},ln=r=>Tt(r)&&!!r.request_message,Bs=r=>r.chatHistory.findLast(t=>ln(t)),eh=r=>{const t=Bs(r);if(t!=null&&t.structured_output_nodes){const e=t.structured_output_nodes.find(s=>s.type===X.AGENT_MEMORY);if(e)try{const{memoriesRequestId:s,memory:n}=JSON.parse(e.content);return{memoriesRequestId:s,memory:n}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},sh=r=>zo(r,t=>{var e;return!!((e=t.structured_output_nodes)!=null&&e.some(s=>{var n;return s.type===X.TOOL_USE&&((n=s.tool_use)==null?void 0:n.tool_name)==="remember"}))}).length>0,zo=(r,t)=>{const e=Bs(r);return e!=null&&e.request_id?r.historyFrom(e.request_id,!0).filter(s=>Tt(s)&&(!t||t(s))):[]},nh=r=>{var s;const t=r.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!Tt(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===X.TOOL_USE))??[];for(const n of e)if(n.tool_use&&r.getToolUseState(t.request_id,n.tool_use.tool_use_id).phase===le.runnable)return r.updateToolUseState({requestId:t.request_id,toolUseId:n.tool_use.tool_use_id,phase:le.cancelled}),!0;return!1},Po=(r,t,e,s)=>{const n=JSON.stringify({memoriesRequestId:e,memory:t}),i=Bs(r);i!=null&&i.request_id?(s.setRequestId(wt.lastUserExchangeRequestId,i.request_id),r.updateChatItem(i.request_id,{...i,structured_output_nodes:[...i.structured_output_nodes??[],{id:0,type:X.AGENT_MEMORY,content:n}]})):s.setFlag(wt.noLastUserExchangeRequestId)},ih=(r,t)=>{const e=Bs(r);if(!(e!=null&&e.request_id)||e.request_id!==t)return!1;const s=(e.structured_output_nodes||[]).filter(n=>n.type!==X.AGENT_MEMORY);return s.length!==(e.structured_output_nodes||[]).length&&(r.updateChatItem(t,{...e,structured_output_nodes:s}),!0)};function Ho(r,t){const e=r.customPersonalityPrompts;if(e)switch(t){case Dt.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case Dt.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case Dt.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case Dt.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return Bo[t]}const Bo={[Dt.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Dt.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Dt.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Dt.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function Go(r){var e;if(!r)return Yt.IMAGE_FORMAT_UNSPECIFIED;switch((e=r.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return Yt.JPEG;case"png":return Yt.PNG;default:return Yt.IMAGE_FORMAT_UNSPECIFIED}}function rh(r,t){return r.map(e=>e.type===Hn.ContentText?{type:Zs.CONTENT_TEXT,text_content:e.text_content}:e.type===Hn.ContentImage&&e.image_content&&t?{type:Zs.CONTENT_IMAGE,image_content:{image_data:e.image_content.image_data,format:Go(e.image_content.media_type)}}:{type:Zs.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}function Wo(r=[]){let t;for(const e of r){if(e.type===X.TOOL_USE)return e;e.type===X.TOOL_USE_START&&(t=e)}return t}class Q{constructor(t,e,s,n){u(this,"_state");u(this,"_subscribers",new Set);u(this,"_focusModel",new Yr);u(this,"_onSendExchangeListeners",[]);u(this,"_onNewConversationListeners",[]);u(this,"_onHistoryDeleteListeners",[]);u(this,"_onBeforeChangeConversationListeners",[]);u(this,"_totalCharactersCacheThrottleMs",1e3);u(this,"_totalCharactersStore");u(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));u(this,"setConversation",(t,e=!0,s=!0)=>{const n=t.id!==this._state.id;n&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:l,toolUseId:c}=Bn(o);return l===a.requestId&&c===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Qs(a)),[o,a]}return[o,{...a,...Bn(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&n&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const i=Q.isEmpty(t);if(n&&i){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(Tt)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),n&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});u(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});u(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});u(this,"setName",t=>{this.update({name:t})});u(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});u(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});u(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Qs(t)]:t}})});u(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:le.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Qs({requestId:t,toolUseId:e})]||{phase:le.new});u(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:le.unknown};const e=function(n=[]){let i;for(const o of n){if(o.type===X.TOOL_USE)return o;o.type===X.TOOL_USE_START&&(i=o)}return i}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:le.unknown}});u(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;Tt(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Zi.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});u(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});u(this,"updateExchangeById",(t,e,s=!1)=>{var a;const n=this.exchangeWithRequestId(e);if(n===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(n.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(l=[]){const c=Wo(l);return c&&c.type===X.TOOL_USE?l.filter(d=>d.type!==X.TOOL_USE_START):l}([...n.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==n.stop_reason&&n.stop_reason&&t.stop_reason===Kr.REASON_UNSPECIFIED&&(t.stop_reason=n.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...n.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const i=(a=(t.structured_output_nodes||[]).find(l=>l.type===X.MAIN_TEXT_FINISHED))==null?void 0:a.content;i&&i!==t.response_text&&(t.response_text=i);let o=this._state.isShareable||Se({...n,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:o}),!0});u(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});u(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});u(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),n=s.map(i=>i.request_id).filter(i=>i!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:n}),s.forEach(i=>{this._onHistoryDeleteListeners.forEach(o=>o(i))})});u(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});u(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});u(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});u(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});u(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:ht.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));u(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});u(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);u(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});u(this,"historySummaryVersion",1);u(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:xe.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});u(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));u(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});u(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});u(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(n=>t.has(n.id)||n.recentFile||n.selection||n.sourceFolder),s=this._specialContextInputModel.recentItems.filter(n=>!(t.has(n.id)||n.recentFile||n.selection||n.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});u(this,"saveDraftExchange",(t,e)=>{var o,a,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),n=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!n)return;const i=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:i,status:ht.draft}})});u(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});u(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var o,a;const s=!this.name&&this.chatHistory.length===1&&((o=this.firstExchange)==null?void 0:o.request_id)===this.chatHistory[0].request_id,n=$e(this)&&((a=this._state.extraData)==null?void 0:a.hasAgentOnboarded)&&(i=this.chatHistory,i.filter(l=>ln(l))).length===2;var i;this._chatFlagModel.summaryTitles&&(s||n)&&this.updateConversationTitle()}).finally(()=>{var s;$e(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Dn.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});u(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:ht.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});u(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const n={status:ht.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:xe.unseen,timestamp:new Date().toISOString()};this.addExchange(n);for await(const i of this._extensionClient.sendInstructionMessage(n,e)){if(!this.updateExchangeById(i,s,!0))return;s=i.request_id||s}});u(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});u(this,"sendSummaryExchange",()=>{const t={status:ht.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Ce.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});u(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:ht.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:xe.unseen,chatItemType:Ce.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});u(this,"sendExchange",async(t,e=!1)=>{var o;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,n=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&Q.isNew(this._state)){const a=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,a)}catch(c){console.error("Failed to migrate conversation checkpoints:",c)}this._state={...this._state,id:a},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(a),this._subscribers.forEach(c=>c(this))}t=ei(t);let i={status:ht.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:n,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:xe.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(i),this._loadContextFromExchange(i),this._onSendExchangeListeners.forEach(a=>a(i)),this._chatFlagModel.useHistorySummary&&(this._clearStaleHistorySummaryNodes(),await this.maybeAddHistorySummaryNode()),i=await this._addIdeStateNode(i),this.updateExchangeById({structured_request_nodes:i.structured_request_nodes},s,!1);for await(const a of this.sendUserMessage(s,i,e)){if(((o=this.exchangeWithRequestId(s))==null?void 0:o.status)!==ht.sent||!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});u(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:ht.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Cr.chatUseSuggestedQuestion)});u(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});u(this,"recoverExchange",async t=>{var n;if(!t.request_id||t.status!==ht.sent)return;let e=t.request_id;const s=(n=t.structured_output_nodes)==null?void 0:n.filter(i=>i.type===X.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const i of this.getChatStream(t)){if(!this.updateExchangeById(i,e,!0))return;e=i.request_id||e}});u(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Tt(e)&&this._loadContextFromExchange(e)})});u(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});u(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Tt(e)&&this._unloadContextFromExchange(e)})});u(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});u(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});u(this,"_jsonToStructuredRequest",t=>{const e=[],s=i=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===Bt.TEXT){const l=((a=o.text_node)==null?void 0:a.content)??"",c={...o,text_node:{content:l+i}};e[e.length-1]=c}else e.push({id:e.length,type:Bt.TEXT,text_node:{content:i}})},n=i=>{var o,a,l,c;if(i.type==="doc"||i.type==="paragraph")for(const d of i.content??[])n(d);else if(i.type==="hardBreak")s(`
`);else if(i.type==="text")s(i.text??"");else if(i.type==="image"){if(typeof((o=i.attrs)==null?void 0:o.src)!="string")return void console.error("Image source is not a string: ",(a=i.attrs)==null?void 0:a.src);if(i.attrs.isLoading)return;const d=(l=i.attrs)==null?void 0:l.title,p=this._fileNameToImageFormat(d);e.push({id:e.length,type:Bt.IMAGE_ID,image_id_node:{image_id:i.attrs.src,format:p}})}else if(i.type==="mention"){const d=(c=i.attrs)==null?void 0:c.data;d&&Bi(d)?e.push({id:e.length,type:Bt.TEXT,text_node:{content:Ho(this._chatFlagModel,d.personality.type)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}};return n(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=n,this._state={...Q.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return go(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,n)=>s+n,0))||0)<=4?Dt.PROTOTYPER:Dt.DEFAULT}catch(e){return console.error("Error determining persona type:",e),Dt.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Dt.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(Tt);return e&&e.request_message?Q.toSentenceCase(e.request_message):po(t)?"Autofix Chat":$e(t)?"New Agent":"New Chat"}static isNew(t){return t.id===yt}static isEmpty(t){var e;return!(t.chatHistory.some(Tt)||(e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?Q.lastMessageTimestamp(t):e==="lastInteractedAt"?Q.lastInteractedAt(t):Q.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(Tt))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!Q.isEmpty(t)||Q.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const n of this._onBeforeChangeConversationListeners){const i=n(t,s);i!==void 0&&(s=i)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return Q.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Dt.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return Q.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return Q.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=n=>Array.isArray(n)?n.some(e):!!n&&(n.type==="image"||!(!n.content||!Array.isArray(n.content))&&n.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(Tt)??null}get lastExchange(){return this.chatHistory.findLast(Tt)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>Tt(t)&&t.status===ht.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Se(t)||Be(t)||qe(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=(t=t.filter(n=>!qe(n)||n.summaryVersion===this.historySummaryVersion)).findLastIndex(n=>qe(n));this._chatFlagModel.useHistorySummary&&e>0&&(console.info("Using history summary node found at index %d",e),t=t.slice(e));const s=[];for(const n of t)if(Se(n))s.push(ti(n));else if(Be(n)&&n.fromTimestamp!==void 0&&n.toTimestamp!==void 0){if(n.revertTarget){const i=jo(n,1),o={request_message:"",response_text:"",request_id:n.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};s.push(o)}}else this._chatFlagModel.useHistorySummary&&qe(n)&&s.push(ti(n));return s}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===ht.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,n="";const i=await this._addIdeStateNode(ei({...t,request_id:e,status:ht.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,i,!0))o.response_text&&(n+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:n,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var d;const n=this._specialContextInputModel.chatActiveContext;let i;if(e.chatHistory!==void 0)i=e.chatHistory;else{let p=this.successfulMessages;if(e.chatItemType===Ce.summaryTitle){const f=p.findIndex(m=>m.chatItemType!==Ce.agentOnboarding&&ln(m));f!==-1&&(p=p.slice(f))}i=this._convertHistoryToExchanges(p)}let o=this.personaType;if(e.structured_request_nodes){const p=e.structured_request_nodes.find(f=>f.type===Bt.CHANGE_PERSONALITY);p&&p.change_personality_node&&(o=p.change_personality_node.personality_type)}const a={text:e.request_message,chatHistory:i,silent:s,modelId:e.model_id,context:n,userSpecifiedFiles:n.userSpecifiedFiles,externalSourceIds:(d=n.externalSources)==null?void 0:d.map(p=>p.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:o,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,a,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,a,{flags:this._chatFlagModel});for await(const p of c){let f=p;for(const m of l)f=m.handleChunk(f)??f;yield f}for(const p of l)yield*p.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return Yt.JPEG;case"png":return Yt.PNG;case"gif":return Yt.GIF;case"webp":return Yt.WEBP;default:return Yt.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(n=>n.type!==Bt.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(n){console.error("Failed to add IDE state to exchange:",n)}return e?(s=[...s,{id:sr(s)+1,type:Bt.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}async maybeAddHistorySummaryNode(){var y,M,D;const t=this._chatFlagModel.historySummaryPrompt;if(!t||t.trim()==="")return!1;const e=this._convertHistoryToExchanges(this.chatHistory),[s,n]=Qr(e,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(s.length===0)return!1;const i=JSON.stringify(e).length,o=JSON.stringify(s).length,a=JSON.stringify(n).length,l={totalHistoryCharCount:i,totalHistoryExchangeCount:e.length,headCharCount:o,headExchangeCount:s.length,headLastRequestId:((y=s.at(-1))==null?void 0:y.request_id)??"",tailCharCount:a,tailExchangeCount:n.length,tailLastRequestId:((M=n.at(-1))==null?void 0:M.request_id)??"",summaryCharCount:0,summarizationDurationMs:0};let c=((D=s.at(-1))==null?void 0:D.response_nodes)??[],d=c.filter(w=>w.type===X.TOOL_USE);d.length>0&&(s.at(-1).response_nodes=c.filter(w=>w.type!==X.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",s.length);const p=Date.now(),{responseText:f,requestId:m}=await this.sendSilentExchange({request_message:t,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:s}),_=Date.now();l.summaryCharCount=f.length,l.summarizationDurationMs=_-p,this._extensionClient.reportAgentRequestEvent({eventName:Dn.chatHistorySummarization,conversationId:this.id,requestId:m??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length,eventData:{chatHistorySummarizationData:l}});const g={chatItemType:Ce.historySummary,summaryVersion:this.historySummaryVersion,request_id:m,request_message:t,response_text:f,structured_output_nodes:[{id:d.map(w=>w.id).reduce((w,$)=>Math.max(w,$),-1)+1,type:X.RAW_RESPONSE,content:f},...d],status:ht.success,seen_state:xe.seen,timestamp:new Date().toISOString()},C=this.chatHistory.findIndex(w=>w.request_id===s.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",C);const k=[...this._state.chatHistory];return k.splice(C,0,g),this.update({chatHistory:k}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(t=>!qe(t)||t.summaryVersion===this.historySummaryVersion)})}}function jo(r,t){const e=(Be(r),r.fromTimestamp),s=(Be(r),r.toTimestamp),n=Be(r)&&r.revertTarget!==void 0;return{id:t,type:Bt.CHECKPOINT_REF,checkpoint_ref_node:{request_id:r.request_id||"",from_timestamp:e,to_timestamp:s,source:n?qr.CHECKPOINT_REVERT:void 0}}}function ti(r){const t=(r.structured_output_nodes??[]).filter(e=>e.type===X.RAW_RESPONSE||e.type===X.TOOL_USE||e.type===X.TOOL_USE_START).map(e=>e.type===X.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:X.TOOL_USE}:e);return{request_message:r.request_message,response_text:r.response_text??"",request_id:r.request_id||"",request_nodes:r.structured_request_nodes??[],response_nodes:t}}function sr(r){return r.length>0?Math.max(...r.map(t=>t.id)):0}function ei(r){var t;if(r.request_message.length>0&&!((t=r.structured_request_nodes)!=null&&t.some(e=>e.type===Bt.TEXT))){let e=r.structured_request_nodes??[];return e=[...e,{id:sr(e)+1,type:Bt.TEXT,text_node:{content:r.request_message}}],{...r,structured_request_nodes:e}}return r}class Vo{constructor(t=!0,e=setTimeout){u(this,"_notify",new Set);u(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});u(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});u(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});u(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,n){if(!t&&!n)return()=>{};const i={timeout:t,notify:e,once:s,date:n};return this._notify.add(i),this._schedule(i),()=>{this._clearTimeout(i),this._notify.delete(i)}}}class Zo{constructor(t=0,e=0,s=new Vo,n=st("busy"),i=st(!1)){u(this,"unsubNotify");u(this,"unsubMessage");u(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});u(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=n,this.focusAfterIdle=i,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var gs=(r=>(r.send="send",r.addTask="addTask",r))(gs||{});const oh=[{id:"send",label:"Send to Agent",icon:Jr,description:"Send message to agent"},{id:"addTask",label:"Add Task",icon:uo,description:"Add task with the message content"}];class Yo{constructor(){u(this,"_mode",st(gs.send));u(this,"_currentMode",gs.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(gs).includes(t)&&this._mode.set(t)}}const ds=st("idle");var pt=(r=>(r.manual="manual",r.auto="auto",r))(pt||{});class Qo{constructor(t,e,s,n={}){u(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});u(this,"extensionClient");u(this,"_chatFlagsModel");u(this,"_currConversationModel");u(this,"_chatModeModel");u(this,"_sendModeModel");u(this,"_currentChatMode");u(this,"_flagsLoaded",st(!1));u(this,"subscribers",new Set);u(this,"idleMessageModel",new Zo);u(this,"isPanelCollapsed");u(this,"agentExecutionMode");u(this,"sortConversationsBy");u(this,"displayedAnnouncements");u(this,"onLoaded",async()=>{var s,n;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&(t.enableBackgroundAgents||t.enableNewThreadsList);this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Pr,bigSyncThreshold:t.bigSyncThreshold??Hr,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Br,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Sr.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:(t.enableBackgroundAgents??!1)||(t.enableNewThreadsList??!1),enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryMaxChars:t.historySummaryMaxChars??0,historySummaryLowerChars:t.historySummaryLowerChars??0,historySummaryPrompt:t.historySummaryPrompt??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._currentChatMode=t.currentChatMode,e&&this.onDoUseNewDraftFunctionalityChanged(),this._flagsLoaded.set(!0),(n=(s=this.options).onLoaded)==null||n.call(s),this.notifySubscribers()});u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==js&&this.currentConversationId!==js||(delete this._state.conversations[js],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===yt||Q.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});u(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const n=s.chatHistory.some(i=>Se(i));t[e]={...s,isShareable:n}}this._state.conversations=t});u(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[n,i]of Object.entries(e))i.isPinned&&s.add(n);this.setState(this._state),this.notifySubscribers()});u(this,"saveImmediate",()=>{this._host.setState(this._state)});u(this,"setState",Zr(t=>{this._host.setState({...t,isPanelCollapsed:z(this.isPanelCollapsed),agentExecutionMode:z(this.agentExecutionMode),sortConversationsBy:z(this.sortConversationsBy),displayedAnnouncements:z(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});u(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));u(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[yt];if(this.currentConversationId&&this.currentConversationId!==yt&&this._state.conversations[this.currentConversationId]&&Q.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:yt};this._state.conversations[yt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=yt,this._currConversationModel.setConversation(e)}});u(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Ce.educateFeatures,request_id:crypto.randomUUID(),seen_state:xe.seen})});u(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});u(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let n;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=yt),n=this._state.conversations[t]??Q.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===yt&&(n.id=yt),s!=null&&s.newTaskUuid&&(n.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations($e(this._currConversationModel)?"agent":"chat"),n=Q.create({personaType:await this._currConversationModel.decidePersonaType()})):n=this._state.conversations[t]??Q.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const i=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(n,!i,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});u(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[yt]});u(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});u(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});u(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;ds.set("copying");const s=e==null?void 0:e.chatHistory,n=s.reduce((a,l)=>(Se(l)&&a.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),a),[]);if(n.length===0)throw new Error("No chat history to share");const i=Q.getDisplayName(e),o=await this.extensionClient.saveChat(t,n,i);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});u(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void ds.set("idle");navigator.clipboard.writeText(e),ds.set("copied")}catch{ds.set("failed")}});u(this,"deleteConversations",async(t,e=void 0,s=[],n)=>{const i=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${i>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);this.deleteConversationIds(o)}if(s.length>0&&n)for(const o of s)try{await n.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});u(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});u(this,"deleteConversationIds",async t=>{var s;const e=[];for(const n of t){const i=((s=this._state.conversations[n])==null?void 0:s.requestIds)??[];e.push(...i)}for(const n of Object.values(this._state.conversations))if(t.has(n.id)){for(const o of n.chatHistory)Tt(o)&&this.deleteImagesInExchange(o);const i=n.draftExchange;i&&this.deleteImagesInExchange(i)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([n])=>!t.has(n)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});u(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});u(this,"findImagesInJson",t=>{const e=[],s=n=>{var i;if(n.type==="image"&&((i=n.attrs)!=null&&i.src))e.push(n.attrs.src);else if((n.type==="doc"||n.type==="paragraph")&&n.content)for(const o of n.content)s(o)};return s(t),e});u(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===Bt.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));u(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});u(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});u(this,"smartPaste",(t,e,s,n)=>{const i=this._currConversationModel.historyTo(t,!0).filter(o=>Se(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:i,targetFile:s??void 0,options:n})});u(this,"saveImage",async t=>await this.extensionClient.saveImage(t));u(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));u(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=n,this._chatFlagsModel=new zr(n.initialFlags),this.extensionClient=new $r(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new Q(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Yo,this.initialize(n.initialConversation);const i=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=st(i),this.agentExecutionMode=st(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=st(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=st(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get currentChatMode(){return this._currentChatMode}setCurrentChatMode(t){this._currentChatMode=t,this.extensionClient.setLastUsedChatMode(t)}get flagsLoaded(){return this._flagsLoaded}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const n=t||this._state.sortConversationsBy||"lastMessageTimestamp";let i=Object.values(this._state.conversations);return s&&(i=i.filter(s)),i.sort((o,a)=>{const l=Q.getTime(o,n).getTime(),c=Q.getTime(a,n).getTime();return e==="asc"?l-c:c-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===yt)return!1;const n=!Q.isValid(this.conversations[s]),i=$e(this.conversations[s]);return n&&(t==="agent"&&i||t==="chat"&&!i||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===it.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}u(Qo,"NEW_AGENT_KEY",yt);const be=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,si=new Set,cn=typeof process=="object"&&process?process:{},nr=(r,t,e,s)=>{typeof cn.emitWarning=="function"?cn.emitWarning(r,t,e,s):console.error(`[${e}] ${t}: ${r}`)};let $s=globalThis.AbortController,ni=globalThis.AbortSignal;var Ni;if($s===void 0){ni=class{constructor(){u(this,"onabort");u(this,"_onabort",[]);u(this,"reason");u(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},$s=class{constructor(){u(this,"signal",new ni);t()}abort(e){var s,n;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const i of this.signal._onabort)i(e);(n=(s=this.signal).onabort)==null||n.call(s,e)}}};let r=((Ni=cn.env)==null?void 0:Ni.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{r&&(r=!1,nr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ne=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),ir=r=>ne(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?ms:null:null;class ms extends Array{constructor(t){super(t),this.fill(0)}}var Ee;const pe=class pe{constructor(t,e){u(this,"heap");u(this,"length");if(!h(pe,Ee))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ir(t);if(!e)return[];A(pe,Ee,!0);const s=new pe(t,e);return A(pe,Ee,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};Ee=new WeakMap,q(pe,Ee,!1);let hn=pe;var qi,zi,Lt,Et,Ft,Ot,Ae,Me,ot,Nt,nt,Y,I,xt,At,vt,lt,qt,ct,zt,Pt,Mt,Ht,ae,Ct,b,un,fe,Zt,es,It,rr,ge,Ie,ss,ie,re,pn,_s,vs,Z,fn,Pe,oe,gn;const In=class In{constructor(t){q(this,b);q(this,Lt);q(this,Et);q(this,Ft);q(this,Ot);q(this,Ae);q(this,Me);u(this,"ttl");u(this,"ttlResolution");u(this,"ttlAutopurge");u(this,"updateAgeOnGet");u(this,"updateAgeOnHas");u(this,"allowStale");u(this,"noDisposeOnSet");u(this,"noUpdateTTL");u(this,"maxEntrySize");u(this,"sizeCalculation");u(this,"noDeleteOnFetchRejection");u(this,"noDeleteOnStaleGet");u(this,"allowStaleOnFetchAbort");u(this,"allowStaleOnFetchRejection");u(this,"ignoreFetchAbort");q(this,ot);q(this,Nt);q(this,nt);q(this,Y);q(this,I);q(this,xt);q(this,At);q(this,vt);q(this,lt);q(this,qt);q(this,ct);q(this,zt);q(this,Pt);q(this,Mt);q(this,Ht);q(this,ae);q(this,Ct);q(this,fe,()=>{});q(this,Zt,()=>{});q(this,es,()=>{});q(this,It,()=>!1);q(this,ge,t=>{});q(this,Ie,(t,e,s)=>{});q(this,ss,(t,e,s,n)=>{if(s||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});u(this,qi,"LRUCache");const{max:e=0,ttl:s,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:l,dispose:c,disposeAfter:d,noDisposeOnSet:p,noUpdateTTL:f,maxSize:m=0,maxEntrySize:_=0,sizeCalculation:g,fetchMethod:C,memoMethod:k,noDeleteOnFetchRejection:y,noDeleteOnStaleGet:M,allowStaleOnFetchRejection:D,allowStaleOnFetchAbort:w,ignoreFetchAbort:$}=t;if(e!==0&&!ne(e))throw new TypeError("max option must be a nonnegative integer");const H=e?ir(e):Array;if(!H)throw new Error("invalid max value: "+e);if(A(this,Lt,e),A(this,Et,m),this.maxEntrySize=_||h(this,Et),this.sizeCalculation=g,this.sizeCalculation){if(!h(this,Et)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(k!==void 0&&typeof k!="function")throw new TypeError("memoMethod must be a function if defined");if(A(this,Me,k),C!==void 0&&typeof C!="function")throw new TypeError("fetchMethod must be a function if specified");if(A(this,Ae,C),A(this,ae,!!C),A(this,nt,new Map),A(this,Y,new Array(e).fill(void 0)),A(this,I,new Array(e).fill(void 0)),A(this,xt,new H(e)),A(this,At,new H(e)),A(this,vt,0),A(this,lt,0),A(this,qt,hn.create(e)),A(this,ot,0),A(this,Nt,0),typeof c=="function"&&A(this,Ft,c),typeof d=="function"?(A(this,Ot,d),A(this,ct,[])):(A(this,Ot,void 0),A(this,ct,void 0)),A(this,Ht,!!h(this,Ft)),A(this,Ct,!!h(this,Ot)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!y,this.allowStaleOnFetchRejection=!!D,this.allowStaleOnFetchAbort=!!w,this.ignoreFetchAbort=!!$,this.maxEntrySize!==0){if(h(this,Et)!==0&&!ne(h(this,Et)))throw new TypeError("maxSize must be a positive integer if specified");if(!ne(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");T(this,b,rr).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!M,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ne(n)||n===0?n:1,this.ttlAutopurge=!!i,this.ttl=s||0,this.ttl){if(!ne(this.ttl))throw new TypeError("ttl must be a positive integer if specified");T(this,b,un).call(this)}if(h(this,Lt)===0&&this.ttl===0&&h(this,Et)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,Lt)&&!h(this,Et)){const ye="LRU_CACHE_UNBOUNDED";(os=>!si.has(os))(ye)&&(si.add(ye),nr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",ye,In))}}static unsafeExposeInternals(t){return{starts:h(t,Pt),ttls:h(t,Mt),sizes:h(t,zt),keyMap:h(t,nt),keyList:h(t,Y),valList:h(t,I),next:h(t,xt),prev:h(t,At),get head(){return h(t,vt)},get tail(){return h(t,lt)},free:h(t,qt),isBackgroundFetch:e=>{var s;return T(s=t,b,Z).call(s,e)},backgroundFetch:(e,s,n,i)=>{var o;return T(o=t,b,vs).call(o,e,s,n,i)},moveToTail:e=>{var s;return T(s=t,b,Pe).call(s,e)},indexes:e=>{var s;return T(s=t,b,ie).call(s,e)},rindexes:e=>{var s;return T(s=t,b,re).call(s,e)},isStale:e=>{var s;return h(s=t,It).call(s,e)}}}get max(){return h(this,Lt)}get maxSize(){return h(this,Et)}get calculatedSize(){return h(this,Nt)}get size(){return h(this,ot)}get fetchMethod(){return h(this,Ae)}get memoMethod(){return h(this,Me)}get dispose(){return h(this,Ft)}get disposeAfter(){return h(this,Ot)}getRemainingTTL(t){return h(this,nt).has(t)?1/0:0}*entries(){for(const t of T(this,b,ie).call(this))h(this,I)[t]===void 0||h(this,Y)[t]===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield[h(this,Y)[t],h(this,I)[t]])}*rentries(){for(const t of T(this,b,re).call(this))h(this,I)[t]===void 0||h(this,Y)[t]===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield[h(this,Y)[t],h(this,I)[t]])}*keys(){for(const t of T(this,b,ie).call(this)){const e=h(this,Y)[t];e===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield e)}}*rkeys(){for(const t of T(this,b,re).call(this)){const e=h(this,Y)[t];e===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield e)}}*values(){for(const t of T(this,b,ie).call(this))h(this,I)[t]===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield h(this,I)[t])}*rvalues(){for(const t of T(this,b,re).call(this))h(this,I)[t]===void 0||T(this,b,Z).call(this,h(this,I)[t])||(yield h(this,I)[t])}[(zi=Symbol.iterator,qi=Symbol.toStringTag,zi)](){return this.entries()}find(t,e={}){for(const s of T(this,b,ie).call(this)){const n=h(this,I)[s],i=T(this,b,Z).call(this,n)?n.__staleWhileFetching:n;if(i!==void 0&&t(i,h(this,Y)[s],this))return this.get(h(this,Y)[s],e)}}forEach(t,e=this){for(const s of T(this,b,ie).call(this)){const n=h(this,I)[s],i=T(this,b,Z).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,h(this,Y)[s],this)}}rforEach(t,e=this){for(const s of T(this,b,re).call(this)){const n=h(this,I)[s],i=T(this,b,Z).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,h(this,Y)[s],this)}}purgeStale(){let t=!1;for(const e of T(this,b,re).call(this,{allowStale:!0}))h(this,It).call(this,e)&&(T(this,b,oe).call(this,h(this,Y)[e],"expire"),t=!0);return t}info(t){const e=h(this,nt).get(t);if(e===void 0)return;const s=h(this,I)[e],n=T(this,b,Z).call(this,s)?s.__staleWhileFetching:s;if(n===void 0)return;const i={value:n};if(h(this,Mt)&&h(this,Pt)){const o=h(this,Mt)[e],a=h(this,Pt)[e];if(o&&a){const l=o-(be.now()-a);i.ttl=l,i.start=Date.now()}}return h(this,zt)&&(i.size=h(this,zt)[e]),i}dump(){const t=[];for(const e of T(this,b,ie).call(this,{allowStale:!0})){const s=h(this,Y)[e],n=h(this,I)[e],i=T(this,b,Z).call(this,n)?n.__staleWhileFetching:n;if(i===void 0||s===void 0)continue;const o={value:i};if(h(this,Mt)&&h(this,Pt)){o.ttl=h(this,Mt)[e];const a=be.now()-h(this,Pt)[e];o.start=Math.floor(Date.now()-a)}h(this,zt)&&(o.size=h(this,zt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const n=Date.now()-s.start;s.start=be.now()-n}this.set(e,s.value,s)}}set(t,e,s={}){var f,m,_,g,C;if(e===void 0)return this.delete(t),this;const{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:l}=s;let{noUpdateTTL:c=this.noUpdateTTL}=s;const d=h(this,ss).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&d>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),T(this,b,oe).call(this,t,"set"),this;let p=h(this,ot)===0?void 0:h(this,nt).get(t);if(p===void 0)p=h(this,ot)===0?h(this,lt):h(this,qt).length!==0?h(this,qt).pop():h(this,ot)===h(this,Lt)?T(this,b,_s).call(this,!1):h(this,ot),h(this,Y)[p]=t,h(this,I)[p]=e,h(this,nt).set(t,p),h(this,xt)[h(this,lt)]=p,h(this,At)[p]=h(this,lt),A(this,lt,p),as(this,ot)._++,h(this,Ie).call(this,p,d,l),l&&(l.set="add"),c=!1;else{T(this,b,Pe).call(this,p);const k=h(this,I)[p];if(e!==k){if(h(this,ae)&&T(this,b,Z).call(this,k)){k.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:y}=k;y===void 0||o||(h(this,Ht)&&((f=h(this,Ft))==null||f.call(this,y,t,"set")),h(this,Ct)&&((m=h(this,ct))==null||m.push([y,t,"set"])))}else o||(h(this,Ht)&&((_=h(this,Ft))==null||_.call(this,k,t,"set")),h(this,Ct)&&((g=h(this,ct))==null||g.push([k,t,"set"])));if(h(this,ge).call(this,p),h(this,Ie).call(this,p,d,l),h(this,I)[p]=e,l){l.set="replace";const y=k&&T(this,b,Z).call(this,k)?k.__staleWhileFetching:k;y!==void 0&&(l.oldValue=y)}}else l&&(l.set="update")}if(n===0||h(this,Mt)||T(this,b,un).call(this),h(this,Mt)&&(c||h(this,es).call(this,p,n,i),l&&h(this,Zt).call(this,l,p)),!o&&h(this,Ct)&&h(this,ct)){const k=h(this,ct);let y;for(;y=k==null?void 0:k.shift();)(C=h(this,Ot))==null||C.call(this,...y)}return this}pop(){var t;try{for(;h(this,ot);){const e=h(this,I)[h(this,vt)];if(T(this,b,_s).call(this,!0),T(this,b,Z).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(h(this,Ct)&&h(this,ct)){const e=h(this,ct);let s;for(;s=e==null?void 0:e.shift();)(t=h(this,Ot))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:n}=e,i=h(this,nt).get(t);if(i!==void 0){const o=h(this,I)[i];if(T(this,b,Z).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!h(this,It).call(this,i))return s&&h(this,fe).call(this,i),n&&(n.has="hit",h(this,Zt).call(this,n,i)),!0;n&&(n.has="stale",h(this,Zt).call(this,n,i))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,n=h(this,nt).get(t);if(n===void 0||!s&&h(this,It).call(this,n))return;const i=h(this,I)[n];return T(this,b,Z).call(this,i)?i.__staleWhileFetching:i}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:p=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:m=this.ignoreFetchAbort,allowStaleOnFetchAbort:_=this.allowStaleOnFetchAbort,context:g,forceRefresh:C=!1,status:k,signal:y}=e;if(!h(this,ae))return k&&(k.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:k});const M={allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:l,sizeCalculation:c,noUpdateTTL:d,noDeleteOnFetchRejection:p,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:_,ignoreFetchAbort:m,status:k,signal:y};let D=h(this,nt).get(t);if(D===void 0){k&&(k.fetch="miss");const w=T(this,b,vs).call(this,t,D,M,g);return w.__returned=w}{const w=h(this,I)[D];if(T(this,b,Z).call(this,w)){const os=s&&w.__staleWhileFetching!==void 0;return k&&(k.fetch="inflight",os&&(k.returnedStale=!0)),os?w.__staleWhileFetching:w.__returned=w}const $=h(this,It).call(this,D);if(!C&&!$)return k&&(k.fetch="hit"),T(this,b,Pe).call(this,D),n&&h(this,fe).call(this,D),k&&h(this,Zt).call(this,k,D),w;const H=T(this,b,vs).call(this,t,D,M,g),ye=H.__staleWhileFetching!==void 0&&s;return k&&(k.fetch=$?"stale":"refresh",ye&&$&&(k.returnedStale=!0)),ye?H.__staleWhileFetching:H.__returned=H}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=h(this,Me);if(!s)throw new Error("no memoMethod provided to constructor");const{context:n,forceRefresh:i,...o}=e,a=this.get(t,o);if(!i&&a!==void 0)return a;const l=s(t,a,{options:o,context:n});return this.set(t,l,o),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=e,a=h(this,nt).get(t);if(a!==void 0){const l=h(this,I)[a],c=T(this,b,Z).call(this,l);return o&&h(this,Zt).call(this,o,a),h(this,It).call(this,a)?(o&&(o.get="stale"),c?(o&&s&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?l.__staleWhileFetching:void 0):(i||T(this,b,oe).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?l:void 0)):(o&&(o.get="hit"),c?l.__staleWhileFetching:(T(this,b,Pe).call(this,a),n&&h(this,fe).call(this,a),l))}o&&(o.get="miss")}delete(t){return T(this,b,oe).call(this,t,"delete")}clear(){return T(this,b,gn).call(this,"delete")}};Lt=new WeakMap,Et=new WeakMap,Ft=new WeakMap,Ot=new WeakMap,Ae=new WeakMap,Me=new WeakMap,ot=new WeakMap,Nt=new WeakMap,nt=new WeakMap,Y=new WeakMap,I=new WeakMap,xt=new WeakMap,At=new WeakMap,vt=new WeakMap,lt=new WeakMap,qt=new WeakMap,ct=new WeakMap,zt=new WeakMap,Pt=new WeakMap,Mt=new WeakMap,Ht=new WeakMap,ae=new WeakMap,Ct=new WeakMap,b=new WeakSet,un=function(){const t=new ms(h(this,Lt)),e=new ms(h(this,Lt));A(this,Mt,t),A(this,Pt,e),A(this,es,(i,o,a=be.now())=>{if(e[i]=o!==0?a:0,t[i]=o,o!==0&&this.ttlAutopurge){const l=setTimeout(()=>{h(this,It).call(this,i)&&T(this,b,oe).call(this,h(this,Y)[i],"expire")},o+1);l.unref&&l.unref()}}),A(this,fe,i=>{e[i]=t[i]!==0?be.now():0}),A(this,Zt,(i,o)=>{if(t[o]){const a=t[o],l=e[o];if(!a||!l)return;i.ttl=a,i.start=l,i.now=s||n();const c=i.now-l;i.remainingTTL=a-c}});let s=0;const n=()=>{const i=be.now();if(this.ttlResolution>0){s=i;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return i};this.getRemainingTTL=i=>{const o=h(this,nt).get(i);if(o===void 0)return 0;const a=t[o],l=e[o];return!a||!l?1/0:a-((s||n())-l)},A(this,It,i=>{const o=e[i],a=t[i];return!!a&&!!o&&(s||n())-o>a})},fe=new WeakMap,Zt=new WeakMap,es=new WeakMap,It=new WeakMap,rr=function(){const t=new ms(h(this,Lt));A(this,Nt,0),A(this,zt,t),A(this,ge,e=>{A(this,Nt,h(this,Nt)-t[e]),t[e]=0}),A(this,ss,(e,s,n,i)=>{if(T(this,b,Z).call(this,s))return 0;if(!ne(n)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof i!="function")throw new TypeError("sizeCalculation must be a function");if(n=i(s,e),!ne(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return n}),A(this,Ie,(e,s,n)=>{if(t[e]=s,h(this,Et)){const i=h(this,Et)-t[e];for(;h(this,Nt)>i;)T(this,b,_s).call(this,!0)}A(this,Nt,h(this,Nt)+t[e]),n&&(n.entrySize=s,n.totalCalculatedSize=h(this,Nt))})},ge=new WeakMap,Ie=new WeakMap,ss=new WeakMap,ie=function*({allowStale:t=this.allowStale}={}){if(h(this,ot))for(let e=h(this,lt);T(this,b,pn).call(this,e)&&(!t&&h(this,It).call(this,e)||(yield e),e!==h(this,vt));)e=h(this,At)[e]},re=function*({allowStale:t=this.allowStale}={}){if(h(this,ot))for(let e=h(this,vt);T(this,b,pn).call(this,e)&&(!t&&h(this,It).call(this,e)||(yield e),e!==h(this,lt));)e=h(this,xt)[e]},pn=function(t){return t!==void 0&&h(this,nt).get(h(this,Y)[t])===t},_s=function(t){var i,o;const e=h(this,vt),s=h(this,Y)[e],n=h(this,I)[e];return h(this,ae)&&T(this,b,Z).call(this,n)?n.__abortController.abort(new Error("evicted")):(h(this,Ht)||h(this,Ct))&&(h(this,Ht)&&((i=h(this,Ft))==null||i.call(this,n,s,"evict")),h(this,Ct)&&((o=h(this,ct))==null||o.push([n,s,"evict"]))),h(this,ge).call(this,e),t&&(h(this,Y)[e]=void 0,h(this,I)[e]=void 0,h(this,qt).push(e)),h(this,ot)===1?(A(this,vt,A(this,lt,0)),h(this,qt).length=0):A(this,vt,h(this,xt)[e]),h(this,nt).delete(s),as(this,ot)._--,e},vs=function(t,e,s,n){const i=e===void 0?void 0:h(this,I)[e];if(T(this,b,Z).call(this,i))return i;const o=new $s,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const l={signal:o.signal,options:s,context:n},c=(m,_=!1)=>{const{aborted:g}=o.signal,C=s.ignoreFetchAbort&&m!==void 0;if(s.status&&(g&&!_?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,C&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),g&&!C&&!_)return d(o.signal.reason);const k=p;return h(this,I)[e]===p&&(m===void 0?k.__staleWhileFetching?h(this,I)[e]=k.__staleWhileFetching:T(this,b,oe).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,m,l.options))),m},d=m=>{const{aborted:_}=o.signal,g=_&&s.allowStaleOnFetchAbort,C=g||s.allowStaleOnFetchRejection,k=C||s.noDeleteOnFetchRejection,y=p;if(h(this,I)[e]===p&&(!k||y.__staleWhileFetching===void 0?T(this,b,oe).call(this,t,"fetch"):g||(h(this,I)[e]=y.__staleWhileFetching)),C)return s.status&&y.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),y.__staleWhileFetching;if(y.__returned===y)throw m};s.status&&(s.status.fetchDispatched=!0);const p=new Promise((m,_)=>{var C;const g=(C=h(this,Ae))==null?void 0:C.call(this,t,i,l);g&&g instanceof Promise&&g.then(k=>m(k===void 0?void 0:k),_),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(m(void 0),s.allowStaleOnFetchAbort&&(m=k=>c(k,!0)))})}).then(c,m=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=m),d(m))),f=Object.assign(p,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return e===void 0?(this.set(t,f,{...l.options,status:void 0}),e=h(this,nt).get(t)):h(this,I)[e]=f,f},Z=function(t){if(!h(this,ae))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof $s},fn=function(t,e){h(this,At)[e]=t,h(this,xt)[t]=e},Pe=function(t){t!==h(this,lt)&&(t===h(this,vt)?A(this,vt,h(this,xt)[t]):T(this,b,fn).call(this,h(this,At)[t],h(this,xt)[t]),T(this,b,fn).call(this,h(this,lt),t),A(this,lt,t))},oe=function(t,e){var n,i,o,a;let s=!1;if(h(this,ot)!==0){const l=h(this,nt).get(t);if(l!==void 0)if(s=!0,h(this,ot)===1)T(this,b,gn).call(this,e);else{h(this,ge).call(this,l);const c=h(this,I)[l];if(T(this,b,Z).call(this,c)?c.__abortController.abort(new Error("deleted")):(h(this,Ht)||h(this,Ct))&&(h(this,Ht)&&((n=h(this,Ft))==null||n.call(this,c,t,e)),h(this,Ct)&&((i=h(this,ct))==null||i.push([c,t,e]))),h(this,nt).delete(t),h(this,Y)[l]=void 0,h(this,I)[l]=void 0,l===h(this,lt))A(this,lt,h(this,At)[l]);else if(l===h(this,vt))A(this,vt,h(this,xt)[l]);else{const d=h(this,At)[l];h(this,xt)[d]=h(this,xt)[l];const p=h(this,xt)[l];h(this,At)[p]=h(this,At)[l]}as(this,ot)._--,h(this,qt).push(l)}}if(h(this,Ct)&&((o=h(this,ct))!=null&&o.length)){const l=h(this,ct);let c;for(;c=l==null?void 0:l.shift();)(a=h(this,Ot))==null||a.call(this,...c)}return s},gn=function(t){var e,s,n;for(const i of T(this,b,re).call(this,{allowStale:!0})){const o=h(this,I)[i];if(T(this,b,Z).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=h(this,Y)[i];h(this,Ht)&&((e=h(this,Ft))==null||e.call(this,o,a,t)),h(this,Ct)&&((s=h(this,ct))==null||s.push([o,a,t]))}}if(h(this,nt).clear(),h(this,I).fill(void 0),h(this,Y).fill(void 0),h(this,Mt)&&h(this,Pt)&&(h(this,Mt).fill(0),h(this,Pt).fill(0)),h(this,zt)&&h(this,zt).fill(0),A(this,vt,0),A(this,lt,0),h(this,qt).length=0,A(this,Nt,0),A(this,ot,0),h(this,Ct)&&h(this,ct)){const i=h(this,ct);let o;for(;o=i==null?void 0:i.shift();)(n=h(this,Ot))==null||n.call(this,...o)}};let dn=In;class ah{constructor(){u(this,"_syncStatus",{status:Tr.done,foldersProgress:[]});u(this,"_syncEnabledState",Un.initializing);u(this,"_workspaceGuidelines",[]);u(this,"_openUserGuidelinesInput",!1);u(this,"_userGuidelines");u(this,"_contextStore",new Jo);u(this,"_prevOpenFiles",[]);u(this,"_disableContext",!1);u(this,"_enableAgentMemories",!1);u(this,"subscribers",new Set);u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case it.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case it.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case it.fileRangesSelected:this.updateSelections(e.data);break;case it.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case it.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case it.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});u(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:at.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});u(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});u(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});u(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});u(this,"addFile",t=>{this.addFiles([t])});u(this,"addFiles",t=>{this.updateFiles(t,[])});u(this,"removeFile",t=>{this.removeFiles([t])});u(this,"removeFiles",t=>{this.updateFiles([],t)});u(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});u(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});u(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...Vs(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...Rr(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});u(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});u(this,"setCurrentlyOpenFiles",t=>{const e=t.map(n=>({recentFile:n,...Vs(n)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,n=>n.id),s.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.recentFile&&(i.file=i.recentFile,delete i.recentFile)}),e.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.file&&(i.recentFile=i.file,delete i.file)}),this.notifySubscribers()});u(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});u(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,n=t.overLimit||((e==null?void 0:e.overLimit)??!1),i={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:at.active,referenceCount:1,showWarning:n,rulesAndGuidelinesState:e};this._contextStore.update([i],s,o=>o.id),this.notifySubscribers()});u(this,"onGuidelinesStateUpdate",t=>{var n;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const i=e||{enabled:!1,overLimit:!1,contents:"",lengthLimit:((n=t.rulesAndGuidelines)==null?void 0:n.lengthLimit)??2e3};this.updateUserGuidelines(i,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(i=>i.sourceFolder))});u(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(n=>n.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));u(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});u(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});u(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});u(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Fn),s=t.map(n=>({selection:n,...Vs(n)}));this._contextStore.update([],e,n=>n.id),this._contextStore.update(s,[],n=>n.id),this.notifySubscribers()});u(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});u(this,"markInactive",t=>{this.markItemsInactive([t])});u(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,at.inactive)}),this.notifySubscribers()});u(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});u(this,"markActive",t=>{this.markItemsActive([t])});u(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,at.active)}),this.notifySubscribers()});u(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});u(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});u(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Er(t)&&!Ln(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Ln)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Fn)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Ar)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(On)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Mr)}get userGuidelines(){return this._contextStore.values.filter(Nn)}get agentMemories(){return[{...Ir,status:this._enableAgentMemories?at.active:at.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>qn(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===at.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===at.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===at.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===at.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===at.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===at.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===Un.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(c=>c.progress!==void 0);if(t.length===0)return;const e=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.trackedFiles)??0)},0),s=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.backlogSize)??0)},0),n=Math.max(e,0),i=Math.min(Math.max(s,0),n),o=n-i,a=[];for(const c of t)(l=c==null?void 0:c.progress)!=null&&l.newlyTracked&&a.push(c.folderRoot);return{status:this._syncStatus.status,totalFiles:n,syncedCount:o,backlogSize:i,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(On(t)||Nn(t)||Bi(t)||qn(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===at.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===at.inactive)}get isContextDisabled(){return this._disableContext}}class Jo{constructor(){u(this,"_cache",new dn({max:1e3}));u(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));u(this,"clear",()=>{this._cache.clear()});u(this,"update",(t,e,s)=>{t.forEach(n=>this.addInPlace(n,s)),e.forEach(n=>this.removeInPlace(n,s))});u(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});u(this,"addInPlace",(t,e)=>{const s=e(t),n=t.referenceCount??1,i=this._cache.get(s),o=t.status??(i==null?void 0:i.status)??at.active;i?(i.referenceCount+=n,i.status=o,i.pinned=t.pinned??i.pinned,i.showWarning=t.showWarning??i.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in i&&(i.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in i&&(i.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:n,status:o})});u(this,"removeInPlace",(t,e)=>{const s=e(t),n=this._cache.get(s);n&&(n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(s))});u(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});u(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});u(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});u(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});u(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===at.active?at.inactive:at.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Ko{constructor(t){u(this,"_githubTimeoutMs",3e4);u(this,"_currentRepositoryUrl",st(void 0));this._asyncMsgSender=t}async listUserRepos(){try{const t=await this._asyncMsgSender.send({type:it.listGithubReposForAuthenticatedUserRequest},this._githubTimeoutMs);return{repos:t.data.repos,error:t.data.error,isDevDeploy:t.data.isDevDeploy}}catch(t){return console.error("Failed to list user repos:",t),{repos:[],error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async listRepoBranches(t,e){try{const s=await this._asyncMsgSender.send({type:it.listGithubRepoBranchesRequest,data:{repo:t,page:e}},this._githubTimeoutMs);return{branches:s.data.branches,hasNextPage:s.data.hasNextPage,nextPage:s.data.nextPage,error:s.data.error,isDevDeploy:s.data.isDevDeploy}}catch(s){return console.error("Failed to list repo branches:",s),{branches:[],hasNextPage:!1,nextPage:0,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async getGithubRepo(t){try{const e=await this._asyncMsgSender.send({type:it.getGithubRepoRequest,data:{repo:t}},this._githubTimeoutMs);return{repo:e.data.repo,error:e.data.error,isDevDeploy:e.data.isDevDeploy}}catch(e){return console.error("Failed to get GitHub repo:",e),{repo:t,error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async getCurrentLocalBranch(){try{return(await this._asyncMsgSender.send({type:it.getCurrentLocalBranchRequest},1e4)).data.branch}catch(t){return void console.error("Failed to get current local branch:",t)}}async listBranches(t=""){try{return{branches:(await this._asyncMsgSender.send({type:it.getGitBranchesRequest,data:{prefix:t}},1e4)).data.branches}}catch(e){return console.error("Failed to fetch branches:",e),{branches:[]}}}async getWorkspaceDiff(t){try{return(await this._asyncMsgSender.send({type:it.getWorkspaceDiffRequest,data:{branchName:t}},1e4)).data.diff}catch(e){return console.error("Failed to get workspace diff:",e),""}}get currentRepositoryUrl(){return this._currentRepositoryUrl}async getRemoteUrl(){const t=z(this._currentRepositoryUrl);if(t)return{remoteUrl:t};try{const e=await this._asyncMsgSender.send({type:it.getRemoteUrlRequest},1e4);return e.data.error?this._currentRepositoryUrl.set(void 0):this._currentRepositoryUrl.set(e.data.remoteUrl),{remoteUrl:e.data.remoteUrl}}catch(e){return console.error("Failed to get remote url:",e),{remoteUrl:"",error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async fetch(){try{await this._asyncMsgSender.send({type:it.gitFetchRequest},1e4)}catch(t){console.error("Failed to fetch remote branch:",t)}}async isGitRepository(){try{return(await this._asyncMsgSender.send({type:it.isGitRepositoryRequest},1e4)).data.isGitRepository}catch(t){return console.error("Failed to check if is git repository:",t),!1}}async isGithubAuthenticated(){try{return(await this._asyncMsgSender.send({type:it.isGithubAuthenticatedRequest},this._githubTimeoutMs)).data.isAuthenticated}catch(t){return console.error("Failed to check GitHub authentication status:",t),!1}}async authenticateGithub(){try{const t=await this._asyncMsgSender.send({type:it.authenticateGithubRequest},this._githubTimeoutMs);return{success:t.data.success,message:t.data.message,url:t.data.url}}catch(t){return console.error("Failed to authenticate with GitHub:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}async revokeGithubAccess(){try{const t=await this._asyncMsgSender.send({type:it.revokeGithubAccessRequest},1e4);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to revoke GitHub access:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}}u(Ko,"key","gitReferenceModel");const Xo={doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},ta={doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},lh="selectedTurnIndex";function ch(r){let t=Xo;return r!=null&&r.isActive&&(t=ta,r.isRemoteAgentSshWindow&&(t.doHideAtMentions=!1)),t}var ea=eo,sa=/\s/,na=function(r){for(var t=r.length;t--&&sa.test(r.charAt(t)););return t},ia=/^\s+/,ra=so,oa=no,aa=function(r){return r&&r.slice(0,na(r)+1).replace(ia,"")},ii=xn,la=function(r){return typeof r=="symbol"||oa(r)&&ra(r)=="[object Symbol]"},ca=/^[-+]0x[0-9a-f]+$/i,ha=/^0b[01]+$/i,da=/^0o[0-7]+$/i,ua=parseInt,pa=xn,Js=function(){return ea.Date.now()},ri=function(r){if(typeof r=="number")return r;if(la(r))return NaN;if(ii(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=ii(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=aa(r);var e=ha.test(r);return e||da.test(r)?ua(r.slice(2),e?2:8):ca.test(r)?NaN:+r},fa=Math.max,ga=Math.min,ma=function(r,t,e){var s,n,i,o,a,l,c=0,d=!1,p=!1,f=!0;if(typeof r!="function")throw new TypeError("Expected a function");function m(y){var M=s,D=n;return s=n=void 0,c=y,o=r.apply(D,M)}function _(y){var M=y-l;return l===void 0||M>=t||M<0||p&&y-c>=i}function g(){var y=Js();if(_(y))return C(y);a=setTimeout(g,function(M){var D=t-(M-l);return p?ga(D,i-(M-c)):D}(y))}function C(y){return a=void 0,f&&s?m(y):(s=n=void 0,o)}function k(){var y=Js(),M=_(y);if(s=arguments,n=this,l=y,M){if(a===void 0)return function(D){return c=D,a=setTimeout(g,t),d?m(D):o}(l);if(p)return clearTimeout(a),a=setTimeout(g,t),m(l)}return a===void 0&&(a=setTimeout(g,t)),o}return t=ri(t)||0,pa(e)&&(d=!!e.leading,i=(p="maxWait"in e)?fa(ri(e.maxWait)||0,t):i,f="trailing"in e?!!e.trailing:f),k.cancel=function(){a!==void 0&&clearTimeout(a),c=0,s=l=n=a=void 0},k.flush=function(){return a===void 0?o:C(Js())},k},_a=xn;const va=Gi(function(r,t,e){var s=!0,n=!0;if(typeof r!="function")throw new TypeError("Expected a function");return _a(e)&&(s="leading"in e?!!e.leading:s,n="trailing"in e?!!e.trailing:n),ma(r,t,{leading:s,maxWait:t,trailing:n})});function ya(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M160 368c26.5 0 48 21.5 48 48v16l72.5-54.4c8.3-6.2 18.4-9.6 28.8-9.6H448c8.8 0 16-7.2 16-16V64c0-8.8-7.2-16-16-16H64c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16zm48 124-.2.2-5.1 3.8-17.1 12.8c-4.8 3.6-11.3 4.2-16.8 1.5s-8.8-8.2-8.8-14.3v-80H64c-35.3 0-64-28.7-64-64V64C0 28.7 28.7 0 64 0h384c35.3 0 64 28.7 64 64v288c0 35.3-28.7 64-64 64H309.3z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function wa(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class ka extends W{constructor(t){super(),j(this,t,wa,ya,V,{})}}function ba(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M410.8 134.2c-19.3 8.6-42 3.5-55.9-12.5C332.8 96.1 300.3 80 264 80c-66.3 0-120 53.7-120 120v.2c0 20.4-12.8 38.5-32 45.3-37.4 13.2-64 48.8-64 90.5 0 53 43 96 96 96h363.3c.6-.1 1.3-.1 1.9-.2 46.2-2.7 82.8-41 82.8-87.8 0-36-21.6-67.1-52.8-80.7-20.1-8.8-31.6-30-28.1-51.7.6-3.8.9-7.7.9-11.7 0-39.8-32.2-72-72-72-10.5 0-20.4 2.2-29.2 6.2zM512 479.8v.2H144C64.5 480 0 415.5 0 336c0-62.7 40.1-116 96-135.8v-.2c0-92.8 75.2-168 168-168 50.9 0 96.4 22.6 127.3 58.3C406.2 83.7 422.6 80 440 80c66.3 0 120 53.7 120 120 0 6.6-.5 13-1.5 19.3 48 21 81.5 68.9 81.5 124.7 0 72.4-56.6 131.6-128 135.8"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function xa(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class Ca extends W{constructor(t){super(),j(this,t,xa,ba,V,{})}}function Ta(r){const{isConversationAgentic:t,agentExecutionMode:e,isBackgroundAgent:s}=r;return s?{icon:Ca,type:"remoteAgent",primaryText:"Remote Agent",bannerText:"Running in the cloud"}:t?(e===pt.manual||pt.auto,{icon:Gr,type:"localAgent",primaryText:"Agent",bannerText:"Running locally"}):{icon:ka,type:"chat",primaryText:"Chat",bannerText:"Running locally"}}class Sa{constructor(t,e,s){u(this,"_prevRemoteAgentWindowOpts",{remoteAgentId:void 0,isRemoteAgentSshWindow:!1});this._chatModel=t,this._agentConversationModel=e,this._remoteAgentsModel=s,this._remoteAgentsModel.subscribe(n=>{n.isRemoteAgentSshWindow===this._prevRemoteAgentWindowOpts.isRemoteAgentSshWindow&&n.remoteAgentSshWindowId===this._prevRemoteAgentWindowOpts.remoteAgentId||(this._prevRemoteAgentWindowOpts={remoteAgentId:n.remoteAgentSshWindowId,isRemoteAgentSshWindow:n.isRemoteAgentSshWindow},this.handleisRemoteAgentSshWindow(n))})}handleisRemoteAgentSshWindow(t){t.isRemoteAgentSshWindow&&this.setToRemoteAgent(t.remoteAgentSshWindowId)}getCurrentModeSelectorMode(){const t=z(this._agentConversationModel.isCurrConversationAgentic);return this._remoteAgentsModel.isActive?we.remoteAgent:t?we.agent:we.chat}async reportModeSelectorEvent(t,e){try{const s=e||this.getCurrentModeSelectorMode();await this._remoteAgentsModel.reportChatModeEvent(t,s)}catch(s){console.error("Failed to report chat mode event:",s)}}reportModeSelectorOpen(){this.reportModeSelectorEvent(Ne.open)}reportModeSelectorClose(){this.reportModeSelectorEvent(Ne.close)}async toggleChatAgentMode(t=!1){t?await this.toggleChatAgentModeReverse():await this.toggleChatAgentModeForward()}async toggleChatAgentModeForward(){const t=z(this._agentConversationModel.isCurrConversationAgentic),e=z(this._chatModel.agentExecutionMode),s=this._remoteAgentsModel.isActive,n=this._chatModel.flags.enableBackgroundAgents,i=this._chatModel.flags.enableAgentAutoMode;s?this.handleSetToChat():t?t&&e===pt.manual&&i?await this.handleSetToAgent(pt.auto):t&&e===pt.auto&&n?(this.handleSetToBackgroundAgent(),this._chatModel.agentExecutionMode.set(pt.manual)):this.handleSetToChat():await this.handleSetToAgent(pt.manual)}async toggleChatAgentModeReverse(){const t=z(this._agentConversationModel.isCurrConversationAgentic),e=z(this._chatModel.agentExecutionMode),s=this._remoteAgentsModel.isActive,n=this._chatModel.flags.enableBackgroundAgents,i=this._chatModel.flags.enableAgentAutoMode;s?await this.handleSetToAgent(pt.auto):t?t&&e===pt.auto?await this.handleSetToAgent(pt.manual):(t&&pt.manual,this.handleSetToChat()):n?this.handleSetToBackgroundAgent():i?await this.handleSetToAgent(pt.auto):await this.handleSetToAgent(pt.manual)}get isRemoteAgentSshWindow(){return this._remoteAgentsModel.isRemoteAgentSshWindow}get remoteAgentSshWindowId(){return this._remoteAgentsModel.remoteAgentSshWindowId}get isRemoteAgentActive(){return this._remoteAgentsModel.isActive}setToChat(){this.isRemoteAgentSshWindow||(this._agentConversationModel?(this._remoteAgentsModel.setIsActive(!1),this._agentConversationModel.setToChat(),this._agentConversationModel.refreshAutoModeAcceptance(),this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Ys.chat)):console.error("AgentConversationModel is not initialized"))}async setToAgent(t){this.isRemoteAgentSshWindow||(this._agentConversationModel?(this._remoteAgentsModel.setIsActive(!1),await this._agentConversationModel.setToAgentic(),this._chatModel.agentExecutionMode.set(t),await this._agentConversationModel.refreshAutoModeAcceptance(),this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Ys.agent)):console.error("AgentConversationModel is not initialized"))}async setToRemoteAgent(t){var e;if(this._agentConversationModel){if(this.isRemoteAgentSshWindow&&(t&&this.remoteAgentSshWindowId&&t!==this.remoteAgentSshWindowId&&(t=this.remoteAgentSshWindowId),!t&&this.remoteAgentSshWindowId&&(t=this.remoteAgentSshWindowId)),this._remoteAgentsModel.setIsActive(!0),t)this._remoteAgentsModel.setCurrentAgent(t);else if(t===null)this._remoteAgentsModel.clearCurrentAgent();else if(!((e=this._remoteAgentsModel)!=null&&e.currentAgent)){const s=this._remoteAgentsModel.agentOverviews.length>0?this._remoteAgentsModel.agentOverviews[0]:void 0;s&&this._remoteAgentsModel.setCurrentAgent(s.remote_agent_id)}this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Ys.remoteAgent)}else console.error("AgentConversationModel is not initialized")}async createOrUseThread(t,e){if(t==="chat")await this._chatModel.setCurrentConversation(void 0,!0,{noopIfSameConversation:!0}),this.setToChat();else if(t==="localAgent"){await this._chatModel.setCurrentConversation(void 0,!0,{noopIfSameConversation:!0});const s=e??z(this._chatModel.agentExecutionMode)??pt.manual;await this.setToAgent(s)}else t==="remoteAgent"&&(this._remoteAgentsModel.setIsActive(!0),this.setToRemoteAgent(null))}async createNewChatThread(){await this.createOrUseThread("chat")}async createNewLocalAgentThread(t){await this.createOrUseThread("localAgent",t)}async createNewRemoteAgentThread(){await this.createOrUseThread("remoteAgent")}async createThreadOfCurrentType(){const t=z(this._agentConversationModel.isCurrConversationAgentic),e=z(this._chatModel.agentExecutionMode),s=Ta({isConversationAgentic:t,agentExecutionMode:e,isBackgroundAgent:z(this._remoteAgentsModel).isActive});if(s.type==="remoteAgent")await this.createNewRemoteAgentThread();else if(s.type==="localAgent"){const n=e||pt.manual;await this.createNewLocalAgentThread(n)}else s.type,await this.createNewChatThread()}findLatestChatThread(){const t=this._chatModel.orderedConversations().filter(e=>{var s;return!((s=e.extraData)!=null&&s.isAgentConversation)&&e.id!==yt});return t.length>0?t[0].id:void 0}findLatestLocalAgentThread(){const t=this._chatModel.orderedConversations().filter(e=>{var s,n;return((s=e.extraData)==null?void 0:s.isAgentConversation)===!0&&!((n=e.extraData)!=null&&n.isRemoteAgentConversation)&&e.id!==yt});return t.length>0?t[0].id:void 0}findLatestRemoteAgentThread(){if(this._remoteAgentsModel.currentAgent)return this._remoteAgentsModel.currentAgent.remote_agent_id;const t=this._remoteAgentsModel.agentOverviews||[];return t.length>0?t[0].remote_agent_id:void 0}handleSetToChat(){const t=this.findLatestChatThread();t?this.switchToThread("chat",t):this.createNewChatThread(),this.reportModeSelectorEvent(Ne.select,we.chat)}async handleSetToAgent(t){const e=this.findLatestLocalAgentThread();e?this.switchToThread("localAgent",e,t):this.createNewLocalAgentThread(t),this.reportModeSelectorEvent(Ne.select,we.agent)}handleSetToBackgroundAgent(){if(!this._remoteAgentsModel)return void console.error("No remote agents model found");const t=this.findLatestRemoteAgentThread();t?this.switchToThread("remoteAgent",t):this.createNewRemoteAgentThread(),this.reportModeSelectorEvent(Ne.select,we.remoteAgent)}switchToThread(t,e,s=pt.manual){return(!this.isRemoteAgentSshWindow||!(t!=="remoteAgent"||this.remoteAgentSshWindowId&&e!==this.remoteAgentSshWindowId))&&(t==="remoteAgent"?e===yt?this.setToRemoteAgent(null):this.setToRemoteAgent(e):(this._chatModel.setCurrentConversation(e,!0,{noopIfSameConversation:!0}),t==="chat"?this.setToChat():this.setToAgent(s)),!0)}handleMessageFromExtension(t){const e=t.data;return e.type===it.remoteAgentSelectAgentId&&(this.setToRemoteAgent(e.data.agentId),!0)}}u(Sa,"key","chatModeModel");var N=(r=>(r.NOT_STARTED="NOT_STARTED",r.IN_PROGRESS="IN_PROGRESS",r.CANCELLED="CANCELLED",r.COMPLETE="COMPLETE",r))(N||{}),St=(r=>(r.USER="USER",r.AGENT="AGENT",r))(St||{}),or={},Es={},As={};let us;Object.defineProperty(As,"__esModule",{value:!0}),As.default=function(){if(!us&&(us=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!us))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return us($a)};const $a=new Uint8Array(16);var de={},me={},Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;Ms.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(me,"__esModule",{value:!0}),me.default=void 0;var ps,Ea=(ps=Ms)&&ps.__esModule?ps:{default:ps},Aa=function(r){return typeof r=="string"&&Ea.default.test(r)};me.default=Aa,Object.defineProperty(de,"__esModule",{value:!0}),de.default=void 0,de.unsafeStringify=ar;var Ma=function(r){return r&&r.__esModule?r:{default:r}}(me);const ut=[];for(let r=0;r<256;++r)ut.push((r+256).toString(16).slice(1));function ar(r,t=0){return ut[r[t+0]]+ut[r[t+1]]+ut[r[t+2]]+ut[r[t+3]]+"-"+ut[r[t+4]]+ut[r[t+5]]+"-"+ut[r[t+6]]+ut[r[t+7]]+"-"+ut[r[t+8]]+ut[r[t+9]]+"-"+ut[r[t+10]]+ut[r[t+11]]+ut[r[t+12]]+ut[r[t+13]]+ut[r[t+14]]+ut[r[t+15]]}var Ia=function(r,t=0){const e=ar(r,t);if(!(0,Ma.default)(e))throw TypeError("Stringified UUID is invalid");return e};de.default=Ia,Object.defineProperty(Es,"__esModule",{value:!0}),Es.default=void 0;var Ra=function(r){return r&&r.__esModule?r:{default:r}}(As),Da=de;let oi,Ks,Xs=0,tn=0;var Ua=function(r,t,e){let s=t&&e||0;const n=t||new Array(16);let i=(r=r||{}).node||oi,o=r.clockseq!==void 0?r.clockseq:Ks;if(i==null||o==null){const f=r.random||(r.rng||Ra.default)();i==null&&(i=oi=[1|f[0],f[1],f[2],f[3],f[4],f[5]]),o==null&&(o=Ks=16383&(f[6]<<8|f[7]))}let a=r.msecs!==void 0?r.msecs:Date.now(),l=r.nsecs!==void 0?r.nsecs:tn+1;const c=a-Xs+(l-tn)/1e4;if(c<0&&r.clockseq===void 0&&(o=o+1&16383),(c<0||a>Xs)&&r.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Xs=a,tn=l,Ks=o,a+=122192928e5;const d=(1e4*(268435455&a)+l)%4294967296;n[s++]=d>>>24&255,n[s++]=d>>>16&255,n[s++]=d>>>8&255,n[s++]=255&d;const p=a/4294967296*1e4&268435455;n[s++]=p>>>8&255,n[s++]=255&p,n[s++]=p>>>24&15|16,n[s++]=p>>>16&255,n[s++]=o>>>8|128,n[s++]=255&o;for(let f=0;f<6;++f)n[s+f]=i[f];return t||(0,Da.unsafeStringify)(n)};Es.default=Ua;var Is={},ce={},ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var La=function(r){return r&&r.__esModule?r:{default:r}}(me),Fa=function(r){if(!(0,La.default)(r))throw TypeError("Invalid UUID");let t;const e=new Uint8Array(16);return e[0]=(t=parseInt(r.slice(0,8),16))>>>24,e[1]=t>>>16&255,e[2]=t>>>8&255,e[3]=255&t,e[4]=(t=parseInt(r.slice(9,13),16))>>>8,e[5]=255&t,e[6]=(t=parseInt(r.slice(14,18),16))>>>8,e[7]=255&t,e[8]=(t=parseInt(r.slice(19,23),16))>>>8,e[9]=255&t,e[10]=(t=parseInt(r.slice(24,36),16))/1099511627776&255,e[11]=t/4294967296&255,e[12]=t>>>24&255,e[13]=t>>>16&255,e[14]=t>>>8&255,e[15]=255&t,e};ts.default=Fa,Object.defineProperty(ce,"__esModule",{value:!0}),ce.URL=ce.DNS=void 0,ce.default=function(r,t,e){function s(n,i,o,a){var l;if(typeof n=="string"&&(n=function(d){d=unescape(encodeURIComponent(d));const p=[];for(let f=0;f<d.length;++f)p.push(d.charCodeAt(f));return p}(n)),typeof i=="string"&&(i=(0,Na.default)(i)),((l=i)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let c=new Uint8Array(16+n.length);if(c.set(i),c.set(n,i.length),c=e(c),c[6]=15&c[6]|t,c[8]=63&c[8]|128,o){a=a||0;for(let d=0;d<16;++d)o[a+d]=c[d];return o}return(0,Oa.unsafeStringify)(c)}try{s.name=r}catch{}return s.DNS=lr,s.URL=cr,s};var Oa=de,Na=function(r){return r&&r.__esModule?r:{default:r}}(ts);const lr="6ba7b810-9dad-11d1-80b4-00c04fd430c8";ce.DNS=lr;const cr="6ba7b811-9dad-11d1-80b4-00c04fd430c8";ce.URL=cr;var Rs={};function ai(r){return 14+(r+64>>>9<<4)+1}function he(r,t){const e=(65535&r)+(65535&t);return(r>>16)+(t>>16)+(e>>16)<<16|65535&e}function Gs(r,t,e,s,n,i){return he((o=he(he(t,r),he(s,i)))<<(a=n)|o>>>32-a,e);var o,a}function ft(r,t,e,s,n,i,o){return Gs(t&e|~t&s,r,t,n,i,o)}function gt(r,t,e,s,n,i,o){return Gs(t&s|e&~s,r,t,n,i,o)}function mt(r,t,e,s,n,i,o){return Gs(t^e^s,r,t,n,i,o)}function _t(r,t,e,s,n,i,o){return Gs(e^(t|~s),r,t,n,i,o)}Object.defineProperty(Rs,"__esModule",{value:!0}),Rs.default=void 0;var qa=function(r){if(typeof r=="string"){const t=unescape(encodeURIComponent(r));r=new Uint8Array(t.length);for(let e=0;e<t.length;++e)r[e]=t.charCodeAt(e)}return function(t){const e=[],s=32*t.length,n="0123456789abcdef";for(let i=0;i<s;i+=8){const o=t[i>>5]>>>i%32&255,a=parseInt(n.charAt(o>>>4&15)+n.charAt(15&o),16);e.push(a)}return e}(function(t,e){t[e>>5]|=128<<e%32,t[ai(e)-1]=e;let s=1732584193,n=-271733879,i=-1732584194,o=271733878;for(let a=0;a<t.length;a+=16){const l=s,c=n,d=i,p=o;s=ft(s,n,i,o,t[a],7,-680876936),o=ft(o,s,n,i,t[a+1],12,-389564586),i=ft(i,o,s,n,t[a+2],17,606105819),n=ft(n,i,o,s,t[a+3],22,-1044525330),s=ft(s,n,i,o,t[a+4],7,-176418897),o=ft(o,s,n,i,t[a+5],12,1200080426),i=ft(i,o,s,n,t[a+6],17,-1473231341),n=ft(n,i,o,s,t[a+7],22,-45705983),s=ft(s,n,i,o,t[a+8],7,1770035416),o=ft(o,s,n,i,t[a+9],12,-1958414417),i=ft(i,o,s,n,t[a+10],17,-42063),n=ft(n,i,o,s,t[a+11],22,-1990404162),s=ft(s,n,i,o,t[a+12],7,1804603682),o=ft(o,s,n,i,t[a+13],12,-40341101),i=ft(i,o,s,n,t[a+14],17,-1502002290),n=ft(n,i,o,s,t[a+15],22,1236535329),s=gt(s,n,i,o,t[a+1],5,-165796510),o=gt(o,s,n,i,t[a+6],9,-1069501632),i=gt(i,o,s,n,t[a+11],14,643717713),n=gt(n,i,o,s,t[a],20,-373897302),s=gt(s,n,i,o,t[a+5],5,-701558691),o=gt(o,s,n,i,t[a+10],9,38016083),i=gt(i,o,s,n,t[a+15],14,-660478335),n=gt(n,i,o,s,t[a+4],20,-405537848),s=gt(s,n,i,o,t[a+9],5,568446438),o=gt(o,s,n,i,t[a+14],9,-1019803690),i=gt(i,o,s,n,t[a+3],14,-187363961),n=gt(n,i,o,s,t[a+8],20,1163531501),s=gt(s,n,i,o,t[a+13],5,-1444681467),o=gt(o,s,n,i,t[a+2],9,-51403784),i=gt(i,o,s,n,t[a+7],14,1735328473),n=gt(n,i,o,s,t[a+12],20,-1926607734),s=mt(s,n,i,o,t[a+5],4,-378558),o=mt(o,s,n,i,t[a+8],11,-2022574463),i=mt(i,o,s,n,t[a+11],16,1839030562),n=mt(n,i,o,s,t[a+14],23,-35309556),s=mt(s,n,i,o,t[a+1],4,-1530992060),o=mt(o,s,n,i,t[a+4],11,1272893353),i=mt(i,o,s,n,t[a+7],16,-155497632),n=mt(n,i,o,s,t[a+10],23,-1094730640),s=mt(s,n,i,o,t[a+13],4,681279174),o=mt(o,s,n,i,t[a],11,-358537222),i=mt(i,o,s,n,t[a+3],16,-722521979),n=mt(n,i,o,s,t[a+6],23,76029189),s=mt(s,n,i,o,t[a+9],4,-640364487),o=mt(o,s,n,i,t[a+12],11,-421815835),i=mt(i,o,s,n,t[a+15],16,530742520),n=mt(n,i,o,s,t[a+2],23,-995338651),s=_t(s,n,i,o,t[a],6,-198630844),o=_t(o,s,n,i,t[a+7],10,1126891415),i=_t(i,o,s,n,t[a+14],15,-1416354905),n=_t(n,i,o,s,t[a+5],21,-57434055),s=_t(s,n,i,o,t[a+12],6,1700485571),o=_t(o,s,n,i,t[a+3],10,-1894986606),i=_t(i,o,s,n,t[a+10],15,-1051523),n=_t(n,i,o,s,t[a+1],21,-2054922799),s=_t(s,n,i,o,t[a+8],6,1873313359),o=_t(o,s,n,i,t[a+15],10,-30611744),i=_t(i,o,s,n,t[a+6],15,-1560198380),n=_t(n,i,o,s,t[a+13],21,1309151649),s=_t(s,n,i,o,t[a+4],6,-145523070),o=_t(o,s,n,i,t[a+11],10,-1120210379),i=_t(i,o,s,n,t[a+2],15,718787259),n=_t(n,i,o,s,t[a+9],21,-343485551),s=he(s,l),n=he(n,c),i=he(i,d),o=he(o,p)}return[s,n,i,o]}(function(t){if(t.length===0)return[];const e=8*t.length,s=new Uint32Array(ai(e));for(let n=0;n<e;n+=8)s[n>>5]|=(255&t[n/8])<<n%32;return s}(r),8*r.length))};Rs.default=qa,Object.defineProperty(Is,"__esModule",{value:!0}),Is.default=void 0;var za=hr(ce),Pa=hr(Rs);function hr(r){return r&&r.__esModule?r:{default:r}}var Ha=(0,za.default)("v3",48,Pa.default);Is.default=Ha;var Ds={},Us={};Object.defineProperty(Us,"__esModule",{value:!0}),Us.default=void 0;var Ba={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};Us.default=Ba,Object.defineProperty(Ds,"__esModule",{value:!0}),Ds.default=void 0;var li=dr(Us),Ga=dr(As),Wa=de;function dr(r){return r&&r.__esModule?r:{default:r}}var ja=function(r,t,e){if(li.default.randomUUID&&!t&&!r)return li.default.randomUUID();const s=(r=r||{}).random||(r.rng||Ga.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){e=e||0;for(let n=0;n<16;++n)t[e+n]=s[n];return t}return(0,Wa.unsafeStringify)(s)};Ds.default=ja;var Ls={},Fs={};function Va(r,t,e,s){switch(r){case 0:return t&e^~t&s;case 1:case 3:return t^e^s;case 2:return t&e^t&s^e&s}}function en(r,t){return r<<t|r>>>32-t}Object.defineProperty(Fs,"__esModule",{value:!0}),Fs.default=void 0;var Za=function(r){const t=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof r=="string"){const o=unescape(encodeURIComponent(r));r=[];for(let a=0;a<o.length;++a)r.push(o.charCodeAt(a))}else Array.isArray(r)||(r=Array.prototype.slice.call(r));r.push(128);const s=r.length/4+2,n=Math.ceil(s/16),i=new Array(n);for(let o=0;o<n;++o){const a=new Uint32Array(16);for(let l=0;l<16;++l)a[l]=r[64*o+4*l]<<24|r[64*o+4*l+1]<<16|r[64*o+4*l+2]<<8|r[64*o+4*l+3];i[o]=a}i[n-1][14]=8*(r.length-1)/Math.pow(2,32),i[n-1][14]=Math.floor(i[n-1][14]),i[n-1][15]=8*(r.length-1)&4294967295;for(let o=0;o<n;++o){const a=new Uint32Array(80);for(let m=0;m<16;++m)a[m]=i[o][m];for(let m=16;m<80;++m)a[m]=en(a[m-3]^a[m-8]^a[m-14]^a[m-16],1);let l=e[0],c=e[1],d=e[2],p=e[3],f=e[4];for(let m=0;m<80;++m){const _=Math.floor(m/20),g=en(l,5)+Va(_,c,d,p)+f+t[_]+a[m]>>>0;f=p,p=d,d=en(c,30)>>>0,c=l,l=g}e[0]=e[0]+l>>>0,e[1]=e[1]+c>>>0,e[2]=e[2]+d>>>0,e[3]=e[3]+p>>>0,e[4]=e[4]+f>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,255&e[0],e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,255&e[1],e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,255&e[2],e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,255&e[3],e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,255&e[4]]};Fs.default=Za,Object.defineProperty(Ls,"__esModule",{value:!0}),Ls.default=void 0;var Ya=ur(ce),Qa=ur(Fs);function ur(r){return r&&r.__esModule?r:{default:r}}var Ja=(0,Ya.default)("v5",80,Qa.default);Ls.default=Ja;var Os={};Object.defineProperty(Os,"__esModule",{value:!0}),Os.default=void 0;Os.default="00000000-0000-0000-0000-000000000000";var Ns={};Object.defineProperty(Ns,"__esModule",{value:!0}),Ns.default=void 0;var Ka=function(r){return r&&r.__esModule?r:{default:r}}(me),Xa=function(r){if(!(0,Ka.default)(r))throw TypeError("Invalid UUID");return parseInt(r.slice(14,15),16)};function mn(r,t){if(!(r&&t&&r.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=r,this.dstAlphabet=t}Ns.default=Xa,function(r){Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"NIL",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(r,"parse",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(r,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(r,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(r,"v3",{enumerable:!0,get:function(){return e.default}}),Object.defineProperty(r,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(r,"v5",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(r,"validate",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(r,"version",{enumerable:!0,get:function(){return o.default}});var t=d(Es),e=d(Is),s=d(Ds),n=d(Ls),i=d(Os),o=d(Ns),a=d(me),l=d(de),c=d(ts);function d(p){return p&&p.__esModule?p:{default:p}}}(or),mn.prototype.convert=function(r){var t,e,s,n={},i=this.srcAlphabet.length,o=this.dstAlphabet.length,a=r.length,l=typeof r=="string"?"":[];if(!this.isValid(r))throw new Error('Number "'+r+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return r;for(t=0;t<a;t++)n[t]=this.srcAlphabet.indexOf(r[t]);do{for(e=0,s=0,t=0;t<a;t++)(e=e*i+n[t])>=o?(n[s++]=parseInt(e/o,10),e%=o):s>0&&(n[s++]=0);a=s,l=this.dstAlphabet.slice(e,e+1).concat(l)}while(s!==0);return l},mn.prototype.isValid=function(r){for(var t=0;t<r.length;++t)if(this.srcAlphabet.indexOf(r[t])===-1)return!1;return!0};var tl=mn;function He(r,t){var e=new tl(r,t);return function(s){return e.convert(s)}}He.BIN="01",He.OCT="01234567",He.DEC="0123456789",He.HEX="0123456789abcdef";var el=He;const{v4:sn,validate:sl}=or,fs=el,nn={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},nl={consistentLength:!0};let rn;const ci=(r,t,e)=>{const s=t(r.toLowerCase().replace(/-/g,""));return e&&e.consistentLength?s.padStart(e.shortIdLength,e.paddingChar):s},hi=(r,t)=>{const e=t(r).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[e[1],e[2],e[3],e[4],e[5]].join("-")};var il=(()=>{const r=(t,e)=>{const s=t||nn.flickrBase58,n={...nl,...e};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const i=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const a={shortIdLength:i,consistentLength:n.consistentLength,paddingChar:s[0]},l=fs(fs.HEX,s),c=fs(s,fs.HEX),d=()=>ci(sn(),l,a),p={alphabet:s,fromUUID:f=>ci(f,l,a),maxLength:i,generate:d,new:d,toUUID:f=>hi(f,c),uuid:sn,validate:(f,m=!1)=>{if(!f||typeof f!="string")return!1;const _=n.consistentLength?f.length===i:f.length<=i,g=f.split("").every(C=>s.includes(C));return m===!1?_&&g:_&&g&&sl(hi(f,c))}};return Object.freeze(p),p};return r.constants=nn,r.uuid=sn,r.generate=()=>(rn||(rn=r(nn.flickrBase58).generate),rn()),r})();const rl=Gi(il),pr={[N.NOT_STARTED]:"[ ]",[N.IN_PROGRESS]:"[/]",[N.COMPLETE]:"[x]",[N.CANCELLED]:"[-]"},fr=rl(void 0,{consistentLength:!0});function gr(r,t){if(r.uuid===t)return r;if(r.subTasksData)for(const e of r.subTasksData){const s=gr(e,t);if(s)return s}}function qs(r,t={}){const{shallow:e=!1,excludeUuid:s=!1,shortUuid:n=!0}=t;return mr(r,{shallow:e,excludeUuid:s,shortUuid:n}).join(`
`)}function mr(r,t={}){const{shallow:e=!1,excludeUuid:s=!1,shortUuid:n=!0}=t;let i="";s||(i=`UUID:${n?function(a){try{return fr.fromUUID(a)}catch{return a}}(r.uuid):r.uuid} `);const o=`${pr[r.state]} ${i}NAME:${r.name} DESCRIPTION:${r.description}`;return e||!r.subTasksData||r.subTasksData.length===0?[o]:[o,...(r.subTasksData||[]).map(a=>mr(a,t).map(l=>`-${l}`)).flat()]}function _n(r,t){var s;const e=(s=r.subTasksData)==null?void 0:s.map(n=>_n(n,t));return{...r,uuid:t!=null&&t.keepUuid?r.uuid:crypto.randomUUID(),subTasks:(e==null?void 0:e.map(n=>n.uuid))||[],subTasksData:e}}function di(r,t={}){if(!r.trim())throw new Error("Empty markdown");const e=r.split(`
`);let s=0;for(const c of e)if(c.trim()&&ui(c)===0)try{vn(c,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const n=r.split(`
`);function i(){for(;n.length>0;){const c=n.shift(),d=ui(c);try{return{task:vn(c,t),level:d}}catch{}}}const o=i();if(!o)throw new Error("No root task found");const a=[o.task];let l;for(;l=i();){const c=a[l.level-1];if(!c)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${qs(o.task)}`);c.subTasksData&&c.subTasks||(c.subTasks=[],c.subTasksData=[]),c.subTasksData.push(l.task),c.subTasks.push(l.task.uuid),a[l.level]=l.task,a.splice(l.level+1)}return o.task}function ui(r){let t=0,e=0;for(;e<r.length&&(r[e]===" "||r[e]==="	");)r[e]===" "?t+=.5:r[e]==="	"&&(t+=1),e++;for(;e<r.length&&r[e]==="-";)t+=1,e++;return Math.floor(t)}function vn(r,t={}){const{excludeUuid:e=!1,shortUuid:s=!0}=t;let n=0;for(;n<r.length&&(r[n]===" "||r[n]==="	"||r[n]==="-");)n++;const i=r.substring(n),o=i.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${r} (missing state)`);const a=o[1],l=Object.entries(pr).reduce((m,[_,g])=>(m[g.substring(1,2)]=_,m),{})[a]||N.NOT_STARTED,c=i.substring(o.index+o[0].length).trim();let d,p,f;if(e){const m=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,_=c.match(m);if(!_){const g=/\b(?:name|NAME):/i.test(c),C=/\b(?:description|DESCRIPTION):/i.test(c);throw!g||!C?new Error(`Invalid task line: ${r} (missing required fields)`):c.toLowerCase().indexOf("name:")<c.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${r} (invalid format)`):new Error(`Invalid task line: ${r} (incorrect field order)`)}if(p=_[1].trim(),f=_[2].trim(),!p)throw new Error(`Invalid task line: ${r} (missing required fields)`);d=crypto.randomUUID()}else{const m=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,_=c.match(m);if(!_){const g=/\b(?:uuid|UUID):/i.test(c),C=/\b(?:name|NAME):/i.test(c),k=/\b(?:description|DESCRIPTION):/i.test(c);if(!g||!C||!k)throw new Error(`Invalid task line: ${r} (missing required fields)`);const y=c.toLowerCase().indexOf("uuid:"),M=c.toLowerCase().indexOf("name:"),D=c.toLowerCase().indexOf("description:");throw y<M&&M<D?new Error(`Invalid task line: ${r} (invalid format)`):new Error(`Invalid task line: ${r} (incorrect field order)`)}if(d=_[1].trim(),p=_[2].trim(),f=_[3].trim(),!d||!p)throw new Error(`Invalid task line: ${r} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(g){try{return fr.toUUID(g)}catch{return g}}(d)}catch{}}return{uuid:d,name:p,description:f,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER}}const Oe=r=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:N.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER,...r}),pi=Oe({name:"Task 1.1",description:"This is the first sub task",state:N.IN_PROGRESS}),fi=Oe({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:N.NOT_STARTED}),gi=Oe({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:N.IN_PROGRESS}),mi=Oe({name:"Task 1.2",description:"This is the second sub task",state:N.COMPLETE,subTasks:[fi.uuid,gi.uuid],subTasksData:[fi,gi]}),_i=Oe({name:"Task 1.3",description:"This is the third sub task",state:N.CANCELLED}),ol=qs(Oe({name:"Task 1",description:"This is the first task",state:N.NOT_STARTED,subTasks:[pi.uuid,mi.uuid,_i.uuid],subTasksData:[pi,mi,_i]}));function _r(r){const t=r.split(`
`);let e=null;const s={created:[],updated:[],deleted:[]};for(const n of t){const i=n.trim();if(i!=="## Created Tasks")if(i!=="## Updated Tasks")if(i!=="## Deleted Tasks"){if(e&&(i.startsWith("[ ]")||i.startsWith("[/]")||i.startsWith("[x]")||i.startsWith("[-]")))try{const o=vn(i,{excludeUuid:!1,shortUuid:!0});o&&s[e].push(o)}catch{}}else e="deleted";else e="updated";else e="created"}return s}function hh(r){const t=r.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const e=_r(vr(r));return{created:e.created.length,updated:e.updated.length,deleted:e.deleted.length}}function vr(r){const t=r.indexOf("# Task Changes");if(t===-1)return"";const e=r.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let n=e.length;for(const a of s){const l=e.indexOf(a);l!==-1&&l<n&&(n=l)}const i=e.substring(0,n),o=i.indexOf(`
`);return o===-1?"":i.substring(o+1).trim()}function dh(r){return _r(vr(r))}function al(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256a208 208 0 1 0-416 0 208 208 0 1 0 416 0M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function ll(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class cl extends W{constructor(t){super(),j(this,t,ll,al,V,{})}}function hl(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256c0-114.9-93.1-208-208-208v416c114.9 0 208-93.1 208-208M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function dl(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class ul extends W{constructor(t){super(),j(this,t,dl,hl,V,{})}}function yn(r){switch(r){case N.IN_PROGRESS:return"info";case N.COMPLETE:return"success";case N.CANCELLED:return"error";case N.NOT_STARTED:default:return"neutral"}}function vi(r){switch(r){case N.IN_PROGRESS:return"In Progress";case N.COMPLETE:return"Completed";case N.CANCELLED:return"Cancelled";case N.NOT_STARTED:default:return"Not Started"}}function yr(r){switch(r){case N.IN_PROGRESS:return ul;case N.COMPLETE:return Vi;case N.CANCELLED:return Wr;case N.NOT_STARTED:default:return cl}}function yi(r){if(!r)return;let t,e,s=-1;return function n(i,o){if(o>0&&i.state===N.IN_PROGRESS&&((!i.subTasksData||i.subTasksData.length===0)&&(t=i),o>=s&&(e=i,s=o)),i.subTasksData&&i.subTasksData.length>0)for(const a of i.subTasksData)n(a,o+1)}(r,0),t||e}function Fe(r){if(!r)return{completed:0,total:0};let t=0,e=0;return function s(n,i=0){if(i>0&&(e++,n.state===N.COMPLETE&&t++),n.subTasksData&&n.subTasksData.length>0)for(const o of n.subTasksData)s(o,i+1)}(r),{completed:t,total:e}}const zs=class zs extends Event{constructor(t){super(zs.type),this.task=t}};u(zs,"type","task-added");let Qe=zs;const Je=class Je{constructor(t,e,s,n){u(this,"_eventTarget",new EventTarget);u(this,"_disposables",[]);u(this,"rootTaskUuid");u(this,"_rootTask",st(void 0));u(this,"rootTask",zn(this._rootTask));u(this,"activeTask",Te(this._rootTask,t=>yi(t)));u(this,"taskProgress",Te(this._rootTask,t=>Fe(t)));u(this,"_isImportingExporting",st(!1));u(this,"isImportingExporting",zn(this._isImportingExporting));u(this,"canShowTaskList");u(this,"refreshTasksThrottled");u(this,"_backlinks",st({}));u(this,"uuidToTask",Te(this._rootTask,t=>{const e=new Map;if(!t)return e;const s={},n=t?[t]:[];for(;n.length>0;){const i=n.pop();if(e.set(i.uuid,i),i.subTasksData){n.push(...i.subTasksData);for(const o of i.subTasks)s[o]=i.uuid}}return this._backlinks.set(s),e}));this._chatModel=t,this._extensionClient=e,this._conversationModel=s,this._agentConversationModel=n,this.refreshTasksThrottled=va(()=>{this.refreshTasks().catch(console.error)},Je.REFRESH_THROTTLE_MS),this.rootTaskUuid=Te(this._conversationModel,i=>i.rootTaskUuid),this._disposables.push({dispose:this.rootTaskUuid.subscribe(async i=>{i&&(this._extensionClient.setCurrentRootTaskUuid(i),await this.refreshTasks())})}),this._disposables.push({dispose:this._conversationModel.onNewConversation(async()=>{await this._maybeInitializeConversationRootTask()})}),this.canShowTaskList=Te([this._chatModel.flags,this._agentConversationModel.isCurrConversationAgentic],([i,o])=>i.enableTaskList&&o),this._maybeInitializeConversationRootTask()}static createReadOnlyStore(t){const e=st(t),s=new Map;if(t){const n=[t];for(;n.length>0;){const i=n.pop();s.set(i.uuid,i),i.subTasksData&&n.push(...i.subTasksData)}}return{rootTaskUuid:st(t==null?void 0:t.uuid),rootTask:e,activeTask:st(yi(t)),taskProgress:st(Fe(t)),canShowTaskList:st(!0),uuidToTask:st(s),createTask:()=>Promise.resolve(""),updateTask:()=>Promise.resolve(),getHydratedTask:()=>Promise.resolve(void 0),cloneHydratedTask:()=>Promise.resolve(void 0),refreshTasks:()=>Promise.resolve(),refreshTasksThrottled:()=>{},updateTaskListStatuses:()=>Promise.resolve(),syncTaskListWithConversation:()=>Promise.resolve(),deleteTask:()=>Promise.resolve(),getParentTask:()=>{},addNewTaskAfter:()=>Promise.resolve(void 0),saveHydratedTask:()=>Promise.resolve(),runHydratedTask:()=>Promise.resolve(),dispose:()=>{},handleMessageFromExtension:()=>!1,runAllTasks:()=>Promise.resolve(),exportTask:()=>Promise.resolve(),exportTasksToMarkdown:()=>Promise.resolve(),importTasksFromMarkdown:()=>Promise.resolve(),isImportingExporting:st(!1),onTaskAdded:()=>()=>{}}}dispose(){for(const t of this._disposables)t.dispose();this._disposables=[]}handleMessageFromExtension(t){return!1}async createTask(t,e,s){const n=await this._extensionClient.createTask(t,e,s);await this.refreshTasks();const i=z(this.rootTask);return i&&this._eventTarget.dispatchEvent(new Qe(i)),n}async updateTask(t,e,s){await this._extensionClient.updateTask(t,e,s),await this.refreshTasks()}async getHydratedTask(t){return this._extensionClient.getHydratedTask(t)}async refreshTasks(){const t=z(this.rootTaskUuid);if(t){const e=await this._extensionClient.getHydratedTask(t);this._rootTask.set(e)}else this._rootTask.set(void 0)}async syncTaskListWithConversation(t){await this._updateTaskList(t,"Update the task list to reflect the current state of the conversation. Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. The updated task list should reflect the current state of the conversation. If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, provide more details by adding more information to the description, or add sub-tasks",["You should add new tasks if any tasks are missing.","You should update the details of tasks if their details are outdated.","You should update the status of tasks if their status is outdated.","You should remove any tasks that are no longer relevant by not including them in the task list."])}async updateTaskListStatuses(t){await this._updateTaskList(t,"Update the status of each task in the list to reflect the current state of the conversation.",["You may update the status of tasks if necessary.","Do not add any new tasks.","Do not remove any tasks.","Do not update the details of any tasks."])}async _maybeInitializeConversationRootTask(){const t=z(this.rootTaskUuid);if(t)return t;const e=`Root task for conversation ${z(this._conversationModel).id}`,s=await this._extensionClient.createTask("Current Task List",e);return this._conversationModel.rootTaskUuid=s,this._extensionClient.setCurrentRootTaskUuid(s),s}async _updateTaskList(t,e="",s=[]){try{const n=z(this._rootTask);if(!n)return;const i=gr(n,t);if(!i)return;const o=qs(i),a=e+`
Follow these rules when updating the task list:
`+(s==null?void 0:s.join(`
`))+`
Maintain the hierarchical structure, given by the \`Example task list structure\`, with proper indentation. If a task is new, give it a UUID of "NEW_UUID". Always structure each task with [ ] for not started, [/] for in progress, [x] for completed, and [-] for cancelled. 
Example task list structure: 
`+ol+`

Current working task list - This is ACTUAL working task list to use, read from, and modify:
`+o+`

Only output the updated markdown without any additional explanation. Do not include any sentences before or after the markdown, ONLY the markdown itself. Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. Just plaintext markdown.`,{responseText:l}=await this._conversationModel.sendSilentExchange({request_message:a,disableSelectedCodeDetails:!0});console.log("Updating task list for conversation",z(this._conversationModel).id),console.log({instructions:a,currentTaskListMarkdown:o,enhancedTaskList:l});const c=di(l);c.uuid=i.uuid;const{created:d,updated:p,deleted:f}=await this._extensionClient.updateHydratedTask(c,St.AGENT);console.log("Task tree update results:",{created:d,updated:p,deleted:f})}finally{await this.refreshTasks()}}getParentTask(t){const e=z(this._backlinks)[t];if(e)return z(this.uuidToTask).get(e)}async addNewTaskAfter(t,e){const s=this.getParentTask(t);if(!s)return;const n=s.subTasks.indexOf(t);if(n===-1)return;const i=await this.cloneHydratedTask(e);return i?(s.subTasks.splice(n+1,0,i.uuid),await this.saveHydratedTask(s),i):void 0}async deleteTask(t){var s;const e=z(this._backlinks)[t];if(e){const n=await this.getHydratedTask(e);if(!n)return;n.subTasks=n.subTasks.filter(i=>i!==t),n.subTasksData=(s=n.subTasksData)==null?void 0:s.filter(i=>i.uuid!==t),await this.updateTask(n.uuid,{subTasks:n.subTasks},St.USER)}else{const n=z(this._rootTask);if(!n||!n.subTasks.includes(t))return;const i=n.subTasks.filter(o=>o!==t);await this.updateTask(n.uuid,{subTasks:i},St.USER)}}async saveHydratedTask(t){await this._extensionClient.updateHydratedTask(t,St.USER),await this.refreshTasks()}async cloneHydratedTask(t){const e=_n(t),s=await this.createTask(e.name,e.description);if(s)return e.uuid=s,await this._extensionClient.updateHydratedTask(e,St.USER),await this.getHydratedTask(s)}async runAllTasks(){const t=z(this._rootTask);t&&this.runHydratedTask(t,{message:"Please run all tasks in the current task list to completion",includeTaskMention:!1})}async runHydratedTask(t,e){const s=this._chatModel.currentConversationId;if(await this._agentConversationModel.interruptAgent(),s!==this._chatModel.currentConversationId)return;if(e!=null&&e.newConversation){const a=await this.cloneHydratedTask(t);if(!a)return;const l=await this.createTask(t.name,t.description);if(await this.saveHydratedTask({uuid:l,name:t.name,description:t.description,state:N.NOT_STARTED,subTasks:[a.uuid],subTasksData:[a],lastUpdated:Date.now(),lastUpdatedBy:St.USER}),s!==this._chatModel.currentConversationId)return;await this._chatModel.setCurrentConversation(void 0,!0,{newTaskUuid:l})}const n=[{type:"text",text:(e==null?void 0:e.message)??"Please shift focus to work on the following task, and mark it as completed when you are done: "}],{includeTaskMention:i=!0}=e??{};i&&n.push({type:"mention",attrs:{id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name,data:{...t,id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name}}});const o={type:"doc",content:[{type:"paragraph",content:n}]};await this._chatModel.currentConversationModel.sendExchange({request_message:"",rich_text_json_repr:o,structured_request_nodes:this._chatModel.currentConversationModel.createStructuredRequestNodes(o),status:ht.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0}),await this.refreshTasks()}async exportTask(t,e){try{this._isImportingExporting.set(!0);const s=(e==null?void 0:e.format)||"markdown";let n,i,o;if(s!=="markdown")throw new Error(`Unsupported export format: ${s}`);n=qs(t,{excludeUuid:!0,shallow:e==null?void 0:e.shallow}),i="md",(e==null?void 0:e.fileName)?o=e.fileName:o=`${((e==null?void 0:e.baseName)||t.name||"Task").replace(/[/:*?"<>|\s]/g,"_")}_${new Date().toISOString().replace(/[:.]/g,"-").slice(0,19)}.${i}`,this._extensionClient.createFile(n,o)}catch(s){console.error("Error exporting task:",s)}finally{this._isImportingExporting.set(!1)}}async exportTasksToMarkdown(){const t=z(this._rootTask);if(!t)return;const e=z(this._conversationModel).name||"Tasks";await this.exportTask(t,{baseName:e,format:"markdown"})}async importTasksFromMarkdown(){try{this._isImportingExporting.set(!0);const t=document.createElement("input");t.type="file",t.accept=".md,text/markdown",t.style.display="none";const e=new Promise(n=>{t.onchange=()=>{n(t.files?t.files[0]:null)},t.oncancel=()=>{n(null)}});document.body.appendChild(t),t.click();const s=await e;if(document.body.removeChild(t),s){const n=await s.text();await this._processImportedFileContent(n)}}catch(t){console.error("Error importing tasks from markdown:",t)}finally{this._isImportingExporting.set(!1)}}async _processImportedFileContent(t){try{if(!t.trim())return void console.error("Empty file content");const e=di(t,{excludeUuid:!0});if(!e)return void console.error("Failed to parse task tree from markdown");const s=z(this._rootTask);if(!s)return void console.error("No root task found");const n=(e.subTasksData||[]).map(d=>_n(d,{keepUuid:!1})),i=s.subTasksData||[],o=s.subTasks||[],a=[...i,...n],l=[...o,...n.map(d=>d.uuid)],c={...s,subTasks:l,subTasksData:a};await this._extensionClient.updateHydratedTask(c,St.USER),await this.refreshTasks()}catch(e){console.error("Error processing imported file content:",e)}}onTaskAdded(t){const e=s=>t(s);return this._eventTarget.addEventListener(Qe.type,e),()=>{this._eventTarget.removeEventListener(Qe.type,e)}}};u(Je,"key","currentConversationTaskStore"),u(Je,"REFRESH_THROTTLE_MS",2e3);let wn=Je;function pl(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),v(e,"fill","currentColor"),v(t,"width","15"),v(t,"height","15"),v(t,"viewBox","0 0 15 15"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class uh extends W{constructor(t){super(),j(this,t,null,pl,V,{})}}function fl(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),v(e,"fill","currentColor"),v(t,"width","15"),v(t,"height","15"),v(t,"viewBox","0 0 15 15"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class ph extends W{constructor(t){super(),j(this,t,null,fl,V,{})}}function gl(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M354.9 121.7c13.8 16 36.5 21.1 55.9 12.5 8.9-3.9 18.7-6.2 29.2-6.2 39.8 0 72 32.2 72 72 0 4-.3 7.9-.9 11.7-3.5 21.6 8.1 42.9 28.1 51.7C570.4 276.9 592 308 592 344c0 46.8-36.6 85.2-82.8 87.8-.6 0-1.3.1-1.9.2H144c-53 0-96-43-96-96 0-41.7 26.6-77.3 64-90.5 19.2-6.8 32-24.9 32-45.3v-.2c0-66.3 53.7-120 120-120 36.3 0 68.8 16.1 90.9 41.7M512 480v-.2c71.4-4.1 128-63.3 128-135.8 0-55.7-33.5-103.7-81.5-124.7 1-6.3 1.5-12.8 1.5-19.3 0-66.3-53.7-120-120-120-17.4 0-33.8 3.7-48.7 10.3C360.4 54.6 314.9 32 264 32c-92.8 0-168 75.2-168 168v.2C40.1 220 0 273.3 0 336c0 79.5 64.5 144 144 144h360zM223 255c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 384c0 13.3 10.7 24 24 24s24-10.7 24-24V249.9l39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function ml(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class fh extends W{constructor(t){super(),j(this,t,ml,gl,V,{})}}function _l(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function vl(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class gh extends W{constructor(t){super(),j(this,t,vl,_l,V,{})}}function wi(r){return r.replace(/\.git$/,"")}function mh(r){if(!r)return"";const t=r.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(/\.git$/,"");const e=r.split("/").filter(Boolean);return(e.length>0?e[e.length-1]:"").replace(/\.git$/,"")}function _h(r){if(!r)return"";const t=r.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(/\.git$/,"")}`;const e=r.split("/").filter(Boolean);return e.length>1?`${e[e.length-2]}/${e[e.length-1].replace(/\.git$/,"")}`:e.length>0?e[e.length-1].replace(/\.git$/,""):""}function vh(r){return Te(r,t=>e=>{if(!t)return!0;const s=wi(t),n=wi(function(i){var o,a,l;return((l=(a=(o=i.workspace_setup)==null?void 0:o.starting_files)==null?void 0:a.github_commit_ref)==null?void 0:l.repository_url)||""}(e));return!!n&&!!s&&n!==s})}function ki(r){let t,e;return t=new Wi({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[yl]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};5&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function yl(r){let t,e;return{c(){t=Xe("+"),e=Xe(r[0])},m(s,n){U(s,t,n),U(s,e,n)},p(s,n){1&n&&kn(e,s[0])},d(s){s&&(E(t),E(e))}}}function bi(r){let t,e;return t=new Wi({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[wl]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};6&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function wl(r){let t,e;return{c(){t=Xe("-"),e=Xe(r[1])},m(s,n){U(s,t,n),U(s,e,n)},p(s,n){2&n&&kn(e,s[1])},d(s){s&&(E(t),E(e))}}}function kl(r){let t,e,s,n=r[0]>0&&ki(r),i=r[1]>0&&bi(r);return{c(){t=et("div"),n&&n.c(),e=Gt(),i&&i.c(),v(t,"class","edit-item__changes svelte-1k8sltp")},m(o,a){U(o,t,a),n&&n.m(t,null),tt(t,e),i&&i.m(t,null),s=!0},p(o,[a]){o[0]>0?n?(n.p(o,a),1&a&&x(n,1)):(n=ki(o),n.c(),x(n,1),n.m(t,e)):n&&(kt(),S(n,1,1,()=>{n=null}),bt()),o[1]>0?i?(i.p(o,a),2&a&&x(i,1)):(i=bi(o),i.c(),x(i,1),i.m(t,null)):i&&(kt(),S(i,1,1,()=>{i=null}),bt())},i(o){s||(x(n),x(i),s=!0)},o(o){S(n),S(i),s=!1},d(o){o&&E(t),n&&n.d(),i&&i.d()}}}function bl(r,t,e){let{totalAddedLines:s=0}=t,{totalRemovedLines:n=0}=t;return r.$$set=i=>{"totalAddedLines"in i&&e(0,s=i.totalAddedLines),"totalRemovedLines"in i&&e(1,n=i.totalRemovedLines)},[s,n]}class yh extends W{constructor(t){super(),j(this,t,bl,kl,V,{totalAddedLines:0,totalRemovedLines:1})}}function xl(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),v(e,"fill","currentColor"),v(t,"width","15"),v(t,"height","15"),v(t,"viewBox","0 0 15 15"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class wh extends W{constructor(t){super(),j(this,t,null,xl,V,{})}}function Cl(r){let t,e,s;var n=r[2];function i(o,a){return{props:{width:o[1],height:o[3]}}}return n&&(e=De(n,i(r))),{c(){t=et("span"),e&&L(e.$$.fragment),v(t,"class","c-task-icon svelte-1dmbt8o"),ls(t,"--icon-color","var(--ds-color-"+yn(r[0])+"-9)"),ls(t,"--icon-size",r[1])},m(o,a){U(o,t,a),e&&F(e,t,null),s=!0},p(o,[a]){if(4&a&&n!==(n=o[2])){if(e){kt();const l=e;S(l.$$.fragment,1,0,()=>{O(l,1)}),bt()}n?(e=De(n,i(o)),L(e.$$.fragment),x(e.$$.fragment,1),F(e,t,null)):e=null}else if(n){const l={};2&a&&(l.width=o[1]),8&a&&(l.height=o[3]),e.$set(l)}(!s||1&a)&&ls(t,"--icon-color","var(--ds-color-"+yn(o[0])+"-9)"),(!s||2&a)&&ls(t,"--icon-size",o[1])},i(o){s||(e&&x(e.$$.fragment,o),s=!0)},o(o){e&&S(e.$$.fragment,o),s=!1},d(o){o&&E(t),e&&O(e)}}}function Tl(r,t,e){let s,n,i,{taskState:o}=t,{size:a=1}=t;return r.$$set=l=>{"taskState"in l&&e(0,o=l.taskState),"size"in l&&e(4,a=l.size)},r.$$.update=()=>{16&r.$$.dirty&&e(1,s={1:"14px",2:"16px",3:"18px",4:"20px"}[a]),2&r.$$.dirty&&e(3,n=s),1&r.$$.dirty&&e(2,i=yr(o))},[o,s,i,n,a]}class Sl extends W{constructor(t){super(),j(this,t,Tl,Cl,V,{taskState:0,size:4})}}function $l(r){let t,e,s;const n=r[5].default,i=Ge(n,r,r[4],null);return{c(){t=et("div"),i&&i.c(),v(t,"class",e=Pn(r[1])+" svelte-1uzel5q"),v(t,"role","status"),v(t,"aria-label",r[0])},m(o,a){U(o,t,a),i&&i.m(t,null),s=!0},p(o,[a]){i&&i.p&&(!s||16&a)&&We(i,n,o,o[4],s?Ve(n,o[4],a,null):je(o[4]),null),(!s||2&a&&e!==(e=Pn(o[1])+" svelte-1uzel5q"))&&v(t,"class",e),(!s||1&a)&&v(t,"aria-label",o[0])},i(o){s||(x(i,o),s=!0)},o(o){S(i,o),s=!1},d(o){o&&E(t),i&&i.d(o)}}}function El(r,t,e){let s,{$$slots:n={},$$scope:i}=t,{color:o="neutral"}=t,{size:a=1}=t,{weight:l="medium"}=t;return r.$$set=c=>{"color"in c&&e(0,o=c.color),"size"in c&&e(2,a=c.size),"weight"in c&&e(3,l=c.weight),"$$scope"in c&&e(4,i=c.$$scope)},r.$$.update=()=>{13&r.$$.dirty&&e(1,s=["c-status-badge",`c-status-badge--${o}`,`c-status-badge--size-${a}`,`c-text--size-${a}`,`c-text--weight-${l}`].join(" "))},[o,s,a,l,i,n]}class kh extends W{constructor(t){super(),j(this,t,El,$l,V,{color:0,size:2,weight:3})}}const Al=r=>({item:16&r}),xi=r=>({item:r[4]}),Ml=r=>({item:16&r}),Ci=r=>({item:r[4]}),Il=r=>({item:16&r}),Ti=r=>({item:r[4]}),Rl=r=>({item:16&r}),Si=r=>({item:r[4]});function $i(r){let t,e;const s=r[11].handle,n=Ge(s,r,r[22],Si);return{c(){t=et("div"),n&&n.c(),v(t,"class","c-draggable-list-item__handle svelte-1u76bcr")},m(i,o){U(i,t,o),n&&n.m(t,null),e=!0},p(i,o){n&&n.p&&(!e||4194320&o)&&We(n,s,i,i[22],e?Ve(s,i[22],o,Rl):je(i[22]),Si)},i(i){e||(x(n,i),e=!0)},o(i){S(n,i),e=!1},d(i){i&&E(t),n&&n.d(i)}}}function Ei(r){let t,e;return t=new ns({props:{class:"c-draggable-list-item__expand-collapse-button",size:1,variant:"ghost",color:"neutral","aria-expanded":r[0],"aria-label":r[0]?"Collapse":"Expand",$$slots:{default:[Ll]},$$scope:{ctx:r}}}),t.$on("click",r[9]),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};1&n&&(i["aria-expanded"]=s[0]),1&n&&(i["aria-label"]=s[0]?"Collapse":"Expand"),4194305&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Dl(r){let t,e;return t=new Xr({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Ul(r){let t,e;return t=new jr({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Ll(r){let t,e,s,n;const i=[Ul,Dl],o=[];function a(l,c){return l[0]?0:1}return t=a(r),e=o[t]=i[t](r),{c(){e.c(),s=se()},m(l,c){o[t].m(l,c),U(l,s,c),n=!0},p(l,c){let d=t;t=a(l),t!==d&&(kt(),S(o[d],1,1,()=>{o[d]=null}),bt(),e=o[t],e||(e=o[t]=i[t](l),e.c()),x(e,1),e.m(s.parentNode,s))},i(l){n||(x(e),n=!0)},o(l){S(e),n=!1},d(l){l&&E(s),o[t].d(l)}}}function Fl(r){let t,e,s,n,i,o,a,l,c,d,p,f,m,_=r[10].handle&&$i(r),g=r[6]&&Ei(r);const C=r[11]["header-contents"],k=Ge(C,r,r[22],Ti),y=r[11].actions,M=Ge(y,r,r[22],Ci),D=r[11].contents,w=Ge(D,r,r[22],xi);return{c(){t=et("div"),e=et("div"),_&&_.c(),s=Gt(),g&&g.c(),n=Gt(),i=et("div"),k&&k.c(),o=Gt(),a=et("div"),M&&M.c(),l=Gt(),c=et("div"),w&&w.c(),v(i,"class","c-draggable-list-item__main svelte-1u76bcr"),v(a,"class","c-draggable-list-item__actions"),v(e,"class","c-draggable-list-item__content svelte-1u76bcr"),v(c,"class","c-draggable-list-item__contents"),$t(c,"c-draggable-list-item__show-connectors",r[8]),$t(c,"c-draggable-list-item__has-handle",r[10].handle),v(t,"class",d="c-draggable-list-item "+r[2]+" svelte-1u76bcr"),v(t,"id",r[3]),v(t,"data-item-id",r[3]),v(t,"data-testid","draggable-list-item"),v(t,"tabindex","0"),v(t,"role","button"),$t(t,"is-disabled",r[5]),$t(t,"has-nested-items",r[6]),$t(t,"is-expanded",r[0]),$t(t,"is-selected",r[7])},m($,H){U($,t,H),tt(t,e),_&&_.m(e,null),tt(e,s),g&&g.m(e,null),tt(e,n),tt(e,i),k&&k.m(i,null),tt(e,o),tt(e,a),M&&M.m(a,null),tt(t,l),tt(t,c),w&&w.m(c,null),r[21](t),p=!0,f||(m=[Vt(t,"mousedown",r[12]),Vt(t,"click",r[13]),Vt(t,"keydown",r[14]),Vt(t,"keyup",r[15]),Vt(t,"keypress",r[16]),Vt(t,"focus",r[17]),Vt(t,"blur",r[18]),Vt(t,"focusin",r[19]),Vt(t,"focusout",r[20])],f=!0)},p($,[H]){$[10].handle?_?(_.p($,H),1024&H&&x(_,1)):(_=$i($),_.c(),x(_,1),_.m(e,s)):_&&(kt(),S(_,1,1,()=>{_=null}),bt()),$[6]?g?(g.p($,H),64&H&&x(g,1)):(g=Ei($),g.c(),x(g,1),g.m(e,n)):g&&(kt(),S(g,1,1,()=>{g=null}),bt()),k&&k.p&&(!p||4194320&H)&&We(k,C,$,$[22],p?Ve(C,$[22],H,Il):je($[22]),Ti),M&&M.p&&(!p||4194320&H)&&We(M,y,$,$[22],p?Ve(y,$[22],H,Ml):je($[22]),Ci),w&&w.p&&(!p||4194320&H)&&We(w,D,$,$[22],p?Ve(D,$[22],H,Al):je($[22]),xi),(!p||256&H)&&$t(c,"c-draggable-list-item__show-connectors",$[8]),(!p||1024&H)&&$t(c,"c-draggable-list-item__has-handle",$[10].handle),(!p||4&H&&d!==(d="c-draggable-list-item "+$[2]+" svelte-1u76bcr"))&&v(t,"class",d),(!p||8&H)&&v(t,"id",$[3]),(!p||8&H)&&v(t,"data-item-id",$[3]),(!p||36&H)&&$t(t,"is-disabled",$[5]),(!p||68&H)&&$t(t,"has-nested-items",$[6]),(!p||5&H)&&$t(t,"is-expanded",$[0]),(!p||132&H)&&$t(t,"is-selected",$[7])},i($){p||(x(_),x(g),x(k,$),x(M,$),x(w,$),p=!0)},o($){S(_),S(g),S(k,$),S(M,$),S(w,$),p=!1},d($){$&&E(t),_&&_.d(),g&&g.d(),k&&k.d($),M&&M.d($),w&&w.d($),r[21](null),f=!1,Dr(m)}}}function Ol(r,t,e){let{$$slots:s={},$$scope:n}=t;const i=Ur(s);let{class:o=""}=t,{id:a}=t,{item:l}=t,{disabled:c=!1}=t,{hasNestedItems:d=!1}=t,{expanded:p=!0}=t,{selected:f=!1}=t,{showConnectors:m=!0}=t,{element:_}=t;return r.$$set=g=>{"class"in g&&e(2,o=g.class),"id"in g&&e(3,a=g.id),"item"in g&&e(4,l=g.item),"disabled"in g&&e(5,c=g.disabled),"hasNestedItems"in g&&e(6,d=g.hasNestedItems),"expanded"in g&&e(0,p=g.expanded),"selected"in g&&e(7,f=g.selected),"showConnectors"in g&&e(8,m=g.showConnectors),"element"in g&&e(1,_=g.element),"$$scope"in g&&e(22,n=g.$$scope)},[p,_,o,a,l,c,d,f,m,function(){e(0,p=!p)},i,s,function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){K.call(this,r,g)},function(g){Qt[g?"unshift":"push"](()=>{_=g,e(1,_)})},n]}class Nl extends W{constructor(t){super(),j(this,t,Ol,Fl,V,{class:2,id:3,item:4,disabled:5,hasNestedItems:6,expanded:0,selected:7,showConnectors:8,element:1})}}function ql(r){let t,e,s,n,i,o,a;function l(p){r[7](p)}function c(p){r[8](p)}let d={size:1,variant:"surface",placeholder:"Add task description...",disabled:!r[0],rows:1,resize:"vertical"};return r[2]!==void 0&&(d.textInput=r[2]),r[1]!==void 0&&(d.value=r[1]),n=new io({props:d}),Qt.push(()=>Ue(n,"textInput",l)),Qt.push(()=>Ue(n,"value",c)),n.$on("keydown",r[4]),n.$on("blur",r[3]),{c(){t=et("div"),e=et("div"),s=et("div"),L(n.$$.fragment),v(s,"class","c-task-details__section c-task-details__description-contents svelte-1k3razm"),v(e,"class","c-task-details__content svelte-1k3razm"),v(t,"class","c-task-details svelte-1k3razm")},m(p,f){U(p,t,f),tt(t,e),tt(e,s),F(n,s,null),a=!0},p(p,[f]){const m={};1&f&&(m.disabled=!p[0]),!i&&4&f&&(i=!0,m.textInput=p[2],Le(()=>i=!1)),!o&&2&f&&(o=!0,m.value=p[1],Le(()=>o=!1)),n.$set(m)},i(p){a||(x(n.$$.fragment,p),a=!0)},o(p){S(n.$$.fragment,p),a=!1},d(p){p&&E(t),O(n)}}}function zl(r,t,e){let s,{task:n}=t,{taskStore:i}=t,{editable:o=!0}=t,a=n.description;function l(){a.trim()!==n.description&&i.updateTask(n.uuid,{description:a.trim()},St.USER),s==null||s.blur()}return r.$$set=c=>{"task"in c&&e(5,n=c.task),"taskStore"in c&&e(6,i=c.taskStore),"editable"in c&&e(0,o=c.editable)},[o,a,s,l,function(c){c.key!=="Enter"||c.shiftKey||c.ctrlKey||c.metaKey?c.key==="Escape"&&(c.preventDefault(),c.stopPropagation(),e(1,a=n.description),s==null||s.blur()):(c.preventDefault(),c.stopPropagation(),l())},n,i,function(c){s=c,e(2,s)},function(c){a=c,e(1,a)}]}class Pl extends W{constructor(t){super(),j(this,t,zl,ql,V,{task:5,taskStore:6,editable:0})}}function Ai(r,t,e){const s=r.slice();return s[26]=t[e],s}function Hl(r){let t,e;const s=[{size:r[1]},{variant:"ghost"},{color:r[9]},r[12]];let n={$$slots:{default:[Gl]},$$scope:{ctx:r}};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return t=new ns({props:n}),t.$on("click",r[16]),t.$on("keyup",r[17]),t.$on("keydown",r[18]),t.$on("mousedown",r[19]),t.$on("mouseover",r[20]),t.$on("focus",r[21]),t.$on("mouseleave",r[22]),t.$on("blur",r[23]),t.$on("contextmenu",r[24]),{c(){L(t.$$.fragment)},m(i,o){F(t,i,o),e=!0},p(i,o){const a=4610&o?Ut(s,[2&o&&{size:i[1]},s[1],512&o&&{color:i[9]},4096&o&&bn(i[12])]):{};536871938&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a)},i(i){e||(x(t.$$.fragment,i),e=!0)},o(i){S(t.$$.fragment,i),e=!1},d(i){O(t,i)}}}function Bl(r){let t,e;return t=new xs.Root({props:{triggerOn:[Ps.Click],open:r[6],onOpenChange:r[15],$$slots:{default:[Jl]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};64&n&&(i.open=s[6]),64&n&&(i.onOpenChange=s[15]),536877043&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Gl(r){let t,e,s;var n=r[10];function i(o,a){return{props:{size:o[1]}}}return n&&(t=De(n,i(r))),{c(){t&&L(t.$$.fragment),e=se()},m(o,a){t&&F(t,o,a),U(o,e,a),s=!0},p(o,a){if(1024&a&&n!==(n=o[10])){if(t){kt();const l=t;S(l.$$.fragment,1,0,()=>{O(l,1)}),bt()}n?(t=De(n,i(o)),L(t.$$.fragment),x(t.$$.fragment,1),F(t,e.parentNode,e)):t=null}else if(n){const l={};2&a&&(l.size=o[1]),t.$set(l)}},i(o){s||(t&&x(t.$$.fragment,o),s=!0)},o(o){t&&S(t.$$.fragment,o),s=!1},d(o){o&&E(e),t&&O(t,o)}}}function Wl(r){let t,e,s;var n=r[10];function i(o,a){return{props:{size:o[1]}}}return n&&(t=De(n,i(r))),{c(){t&&L(t.$$.fragment),e=se()},m(o,a){t&&F(t,o,a),U(o,e,a),s=!0},p(o,a){if(1024&a&&n!==(n=o[10])){if(t){kt();const l=t;S(l.$$.fragment,1,0,()=>{O(l,1)}),bt()}n?(t=De(n,i(o)),L(t.$$.fragment),x(t.$$.fragment,1),F(t,e.parentNode,e)):t=null}else if(n){const l={};2&a&&(l.size=o[1]),t.$set(l)}},i(o){s||(t&&x(t.$$.fragment,o),s=!0)},o(o){t&&S(t.$$.fragment,o),s=!1},d(o){o&&E(e),t&&O(t,o)}}}function jl(r){let t,e;const s=[{size:r[1]},{variant:"ghost"},{color:r[9]},{disabled:r[5]},r[12]];let n={$$slots:{default:[Wl]},$$scope:{ctx:r}};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return t=new ns({props:n}),t.$on("click",r[13]),{c(){L(t.$$.fragment)},m(i,o){F(t,i,o),e=!0},p(i,o){const a=4642&o?Ut(s,[2&o&&{size:i[1]},s[1],512&o&&{color:i[9]},32&o&&{disabled:i[5]},4096&o&&bn(i[12])]):{};536871938&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a)},i(i){e||(x(t.$$.fragment,i),e=!0)},o(i){S(t.$$.fragment,i),e=!1},d(i){O(t,i)}}}function Vl(r){let t,e;return t=new Cn({props:{content:r[8],triggerOn:[Ps.Hover],$$slots:{default:[jl]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};256&n&&(i.content=s[8]),536876642&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Zl(r){let t,e,s=vi(r[26])+"";return{c(){t=Xe(s),e=Gt()},m(n,i){U(n,t,i),U(n,e,i)},p(n,i){128&i&&s!==(s=vi(n[26])+"")&&kn(t,s)},d(n){n&&(E(t),E(e))}}}function Yl(r){let t,e;return t=new Sl({props:{slot:"iconLeft",taskState:r[26],size:1}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};128&n&&(i.taskState=s[26]),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Mi(r){let t,e;function s(){return r[14](r[26])}return t=new xs.Item({props:{onSelect:s,highlight:r[26]===r[0],disabled:!r[4]||r[26]===r[0],$$slots:{iconLeft:[Yl],default:[Zl]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,i){F(t,n,i),e=!0},p(n,i){r=n;const o={};128&i&&(o.onSelect=s),129&i&&(o.highlight=r[26]===r[0]),145&i&&(o.disabled=!r[4]||r[26]===r[0]),536871040&i&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(n){e||(x(t.$$.fragment,n),e=!0)},o(n){S(t.$$.fragment,n),e=!1},d(n){O(t,n)}}}function Ql(r){let t,e,s=Re(r[7]),n=[];for(let o=0;o<s.length;o+=1)n[o]=Mi(Ai(r,s,o));const i=o=>S(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=se()},m(o,a){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,a);U(o,t,a),e=!0},p(o,a){if(2193&a){let l;for(s=Re(o[7]),l=0;l<s.length;l+=1){const c=Ai(o,s,l);n[l]?(n[l].p(c,a),x(n[l],1)):(n[l]=Mi(c),n[l].c(),x(n[l],1),n[l].m(t.parentNode,t))}for(kt(),l=s.length;l<n.length;l+=1)i(l);bt()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)x(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)S(n[a]);e=!1},d(o){o&&E(t),Lr(n,o)}}}function Jl(r){let t,e,s,n;return t=new xs.Trigger({props:{$$slots:{default:[Vl]},$$scope:{ctx:r}}}),s=new xs.Content({props:{size:1,side:"bottom",align:"start",$$slots:{default:[Ql]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=Gt(),L(s.$$.fragment)},m(i,o){F(t,i,o),U(i,e,o),F(s,i,o),n=!0},p(i,o){const a={};536876898&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a);const l={};536871057&o&&(l.$$scope={dirty:o,ctx:i}),s.$set(l)},i(i){n||(x(t.$$.fragment,i),x(s.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),S(s.$$.fragment,i),n=!1},d(i){i&&E(e),O(t,i),O(s,i)}}}function Kl(r){let t,e,s,n;const i=[Bl,Hl],o=[];function a(l,c){return l[4]&&l[2]&&l[3]&&!l[5]?0:1}return t=a(r),e=o[t]=i[t](r),{c(){e.c(),s=se()},m(l,c){o[t].m(l,c),U(l,s,c),n=!0},p(l,[c]){let d=t;t=a(l),t===d?o[t].p(l,c):(kt(),S(o[d],1,1,()=>{o[d]=null}),bt(),e=o[t],e?e.p(l,c):(e=o[t]=i[t](l),e.c()),x(e,1),e.m(s.parentNode,s))},i(l){n||(x(e),n=!0)},o(l){S(e),n=!1},d(l){l&&E(s),o[t].d(l)}}}function Xl(r,t,e){let s,n,i,o;const a=["taskState","size","taskUuid","taskStore","interactive","disabled"];let l=ks(t,a),{taskState:c}=t,{size:d=1}=t,{taskUuid:p}=t,{taskStore:f}=t,{interactive:m=!0}=t,{disabled:_=!1}=t;const g=bs("chatModel");let C=!1;async function k(y){if(!p||!f||_||y===c)return;const M=Fe(z(f.rootTask)),D={action:ys.updateTaskStatus,totalTasksCount:M.total,triggeredBy:ws.user},w=g.currentConversationModel.chatHistory,$=w.length>0&&w[w.length-1].request_id||g.currentConversationModel.id;g.currentConversationModel.extensionClient.reportAgentSessionEvent({eventName:Ke.taskListUsage,conversationId:$,eventData:{taskListUsageData:D}}),await f.updateTask(p,{state:y},St.USER),e(6,C=!1)}return r.$$set=y=>{t=P(P({},t),rt(y)),e(12,l=ks(t,a)),"taskState"in y&&e(0,c=y.taskState),"size"in y&&e(1,d=y.size),"taskUuid"in y&&e(2,p=y.taskUuid),"taskStore"in y&&e(3,f=y.taskStore),"interactive"in y&&e(4,m=y.interactive),"disabled"in y&&e(5,_=y.disabled)},r.$$.update=()=>{1&r.$$.dirty&&e(10,s=yr(c)),1&r.$$.dirty&&e(9,n=yn(c))},e(8,i="Change Status"),e(7,o=Object.values(N)),[c,d,p,f,m,_,C,o,"Change Status",n,s,k,l,()=>e(6,C=!C),y=>k(y),y=>e(6,C=y),function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)},function(y){K.call(this,r,y)}]}class tc extends W{constructor(t){super(),j(this,t,Xl,Kl,V,{taskState:0,size:1,taskUuid:2,taskStore:3,interactive:4,disabled:5})}}function ec(r){let t,e,s,n,i,o;const a=[{size:r[2]},{variant:r[1]},{color:r[3]},{placeholder:r[0]},r[11]];function l(p){r[18](p)}function c(p){r[19](p)}let d={};for(let p=0;p<a.length;p+=1)d=P(d,a[p]);return r[7]!==void 0&&(d.textInput=r[7]),r[6]!==void 0&&(d.value=r[6]),e=new to({props:d}),Qt.push(()=>Ue(e,"textInput",l)),Qt.push(()=>Ue(e,"value",c)),e.$on("keydown",r[10]),e.$on("focus",r[9]),e.$on("blur",r[20]),e.$on("keydown",r[21]),e.$on("click",r[22]),e.$on("blur",r[23]),e.$on("focus",r[24]),{c(){t=et("div"),L(e.$$.fragment),v(t,"class",i="c-editable-text "+r[4]+" svelte-jooyia")},m(p,f){U(p,t,f),F(e,t,null),r[25](t),o=!0},p(p,[f]){const m=2063&f?Ut(a,[4&f&&{size:p[2]},2&f&&{variant:p[1]},8&f&&{color:p[3]},1&f&&{placeholder:p[0]},2048&f&&bn(p[11])]):{};!s&&128&f&&(s=!0,m.textInput=p[7],Le(()=>s=!1)),!n&&64&f&&(n=!0,m.value=p[6],Le(()=>n=!1)),e.$set(m),(!o||16&f&&i!==(i="c-editable-text "+p[4]+" svelte-jooyia"))&&v(t,"class",i)},i(p){o||(x(e.$$.fragment,p),o=!0)},o(p){S(e.$$.fragment,p),o=!1},d(p){p&&E(t),O(e),r[25](null)}}}function sc(r,t,e){const s=["value","disabled","placeholder","clickToEdit","variant","size","color","class","editing","startEdit","acceptEdit","cancelEdit"];let n=ks(t,s);const i=Fr();let o,a,{value:l=""}=t,{disabled:c=!1}=t,{placeholder:d=""}=t,{clickToEdit:p=!1}=t,{variant:f="surface"}=t,{size:m=2}=t,{color:_}=t,{class:g=""}=t,{editing:C=!1}=t,k=l;async function y(w){c||!o||C||(e(6,k=l),i("startEdit",{value:l}),o.focus(),w!=null&&w.selectAll&&(await ji(),o==null||o.select()),e(13,C=!0))}function M(){const w=l,$=k.trim();w!==$?(e(12,l=$),i("acceptEdit",{oldValue:w,newValue:$}),document.activeElement===o&&(o==null||o.blur()),e(13,C=!1)):D()}function D(){e(6,k=l),i("cancelEdit",{value:l}),document.activeElement===o&&(o==null||o.blur()),e(13,C=!1)}return r.$$set=w=>{t=P(P({},t),rt(w)),e(11,n=ks(t,s)),"value"in w&&e(12,l=w.value),"disabled"in w&&e(14,c=w.disabled),"placeholder"in w&&e(0,d=w.placeholder),"clickToEdit"in w&&e(15,p=w.clickToEdit),"variant"in w&&e(1,f=w.variant),"size"in w&&e(2,m=w.size),"color"in w&&e(3,_=w.color),"class"in w&&e(4,g=w.class),"editing"in w&&e(13,C=w.editing)},[d,f,m,_,g,M,k,o,a,function(){!p||c||C||y()},function(w){w.key==="Enter"?M():w.key==="Escape"&&D()},n,l,C,c,p,y,D,function(w){o=w,e(7,o)},function(w){k=w,e(6,k)},()=>M(),function(w){K.call(this,r,w)},function(w){K.call(this,r,w)},function(w){K.call(this,r,w)},function(w){K.call(this,r,w)},function(w){Qt[w?"unshift":"push"](()=>{a=w,e(8,a)})}]}class nc extends W{constructor(t){super(),j(this,t,sc,ec,V,{value:12,disabled:14,placeholder:0,clickToEdit:15,variant:1,size:2,color:3,class:4,editing:13,startEdit:16,acceptEdit:5,cancelEdit:17})}get startEdit(){return this.$$.ctx[16]}get acceptEdit(){return this.$$.ctx[5]}get cancelEdit(){return this.$$.ctx[17]}}function ic(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m48 432 288-176L48 80zM24.5 38.1C39.7 29.6 58.2 30 73 39l288 176c14.3 8.7 23 24.2 23 41s-8.7 32.2-23 41L73 473c-14.8 9.1-33.4 9.4-48.5.9S0 449.4 0 432V80c0-17.4 9.4-33.4 24.5-41.9"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function rc(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class oc extends W{constructor(t){super(),j(this,t,rc,ic,V,{})}}function Ii(r){let t,e;return t=new Cn({props:{content:"Run Task",triggerOn:[Ps.Hover],$$slots:{default:[lc]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};1025&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function ac(r){let t,e;return t=new oc({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function lc(r){let t,e;return t=new ns({props:{size:1,variant:"ghost",color:"neutral",disabled:!r[0],class:"c-task-action-button c-task-action-button--play",$$slots:{default:[ac]},$$scope:{ctx:r}}}),t.$on("click",r[4]),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.disabled=!s[0]),1024&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function cc(r){let t,e;return t=new Vr({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function hc(r){let t,e;return t=new ns({props:{size:1,variant:"ghost",color:"error",disabled:!r[0],class:"c-task-action-button c-task-action-button--delete",$$slots:{default:[cc]},$$scope:{ctx:r}}}),t.$on("click",r[3]),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.disabled=!s[0]),1024&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function dc(r){let t,e,s,n,i=r[1]&&Ii(r);return s=new Cn({props:{content:"Delete Task",triggerOn:[Ps.Hover],$$slots:{default:[hc]},$$scope:{ctx:r}}}),{c(){t=et("div"),i&&i.c(),e=Gt(),L(s.$$.fragment),v(t,"class","c-task-action-buttons svelte-55fcbs")},m(o,a){U(o,t,a),i&&i.m(t,null),tt(t,e),F(s,t,null),n=!0},p(o,[a]){o[1]?i?(i.p(o,a),2&a&&x(i,1)):(i=Ii(o),i.c(),x(i,1),i.m(t,e)):i&&(kt(),S(i,1,1,()=>{i=null}),bt());const l={};1025&a&&(l.$$scope={dirty:a,ctx:o}),s.$set(l)},i(o){n||(x(i),x(s.$$.fragment,o),n=!0)},o(o){S(i),S(s.$$.fragment,o),n=!1},d(o){o&&E(t),i&&i.d(),O(s)}}}function uc(r,t,e){let s,n,i,o,a=R;r.$$.on_destroy.push(()=>a());let{taskUuid:l}=t,{taskStore:c}=t,{editable:d=!0}=t;const p=bs("chatModel");return r.$$set=f=>{"taskUuid"in f&&e(5,l=f.taskUuid),"taskStore"in f&&e(6,c=f.taskStore),"editable"in f&&e(0,d=f.editable)},r.$$.update=()=>{64&r.$$.dirty&&(e(2,s=c.uuidToTask),a(),a=Or(s,f=>e(8,o=f))),288&r.$$.dirty&&e(7,n=o.get(l)),128&r.$$.dirty&&e(1,i=(n==null?void 0:n.state)===N.NOT_STARTED)},[d,i,s,async function(){if(!d)return;const f=Fe(z(c.rootTask)),m={action:ys.deleteTask,totalTasksCount:f.total,triggeredBy:ws.user},_=p.currentConversationModel.chatHistory,g=_.length>0&&_[_.length-1].request_id||p.currentConversationModel.id;p.currentConversationModel.extensionClient.reportAgentSessionEvent({eventName:Ke.taskListUsage,conversationId:g,eventData:{taskListUsageData:m}}),await c.deleteTask(l)},async function(){if(!n||!d||n.state!==N.NOT_STARTED)return;const f=Fe(z(c.rootTask)),m={action:ys.runSingleTask,totalTasksCount:f.total,triggeredBy:ws.user},_=p.currentConversationModel.chatHistory,g=_.length>0&&_[_.length-1].request_id||p.currentConversationModel.id;p.currentConversationModel.extensionClient.reportAgentSessionEvent({eventName:Ke.taskListUsage,conversationId:g,eventData:{taskListUsageData:m}}),await c.updateTask(l,{state:N.IN_PROGRESS},St.USER),await c.runHydratedTask(n)},l,c,n,o]}class pc extends W{constructor(t){super(),j(this,t,uc,dc,V,{taskUuid:5,taskStore:6,editable:0})}}function fc(r,t=new Set(Object.values(N))){const e={...r,isVisible:!1,subTasksData:[]};let s=!1;if(r.subTasksData&&r.subTasksData.length>0){const i=[];for(const o of r.subTasksData){const a=fc(o,t);i.push(a),a.isVisible&&(s=!0)}e.subTasksData=i}const n=function(i,o){return o.size!==0&&o.has(i.state)}(r,t);return e.isVisible=n||s,e}function Ri(r,t,e){const s=r.slice();return s[16]=t[e],s}function Di(r,t,e){const s=r.slice();return s[16]=t[e],s}function gc(r){let t,e,s,n;function i(l){r[12](l)}function o(l){r[13](l)}let a={class:"c-task-tree-item",item:r[0],id:`task-${r[0].uuid}`,hasNestedItems:!!r[0].subTasksData&&r[0].subTasksData.length>0,disabled:!r[2],$$slots:{contents:[yc],actions:[vc],"header-contents":[_c]},$$scope:{ctx:r}};return r[5]!==void 0&&(a.element=r[5]),r[6]!==void 0&&(a.expanded=r[6]),t=new Nl({props:a}),Qt.push(()=>Ue(t,"element",i)),Qt.push(()=>Ue(t,"expanded",o)),{c(){L(t.$$.fragment)},m(l,c){F(t,l,c),n=!0},p(l,c){const d={};1&c&&(d.item=l[0]),1&c&&(d.id=`task-${l[0].uuid}`),1&c&&(d.hasNestedItems=!!l[0].subTasksData&&l[0].subTasksData.length>0),4&c&&(d.disabled=!l[2]),2097623&c&&(d.$$scope={dirty:c,ctx:l}),!e&&32&c&&(e=!0,d.element=l[5],Le(()=>e=!1)),!s&&64&c&&(s=!0,d.expanded=l[6],Le(()=>s=!1)),t.$set(d)},i(l){n||(x(t.$$.fragment,l),n=!0)},o(l){S(t.$$.fragment,l),n=!1},d(l){O(t,l)}}}function mc(r){let t,e,s=[],n=new Map,i=Re(r[7]);const o=a=>a[16].uuid;for(let a=0;a<i.length;a+=1){let l=Di(r,i,a),c=o(l);n.set(c,s[a]=Fi(c,l))}return{c(){t=et("div");for(let a=0;a<s.length;a+=1)s[a].c();v(t,"class","c-task-tree-root-children svelte-10aflq0")},m(a,l){U(a,t,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(t,null);e=!0},p(a,l){134&l&&(i=Re(a[7]),kt(),s=Pi(s,l,o,1,a,i,n,t,Hi,Fi,null,Di),bt())},i(a){if(!e){for(let l=0;l<i.length;l+=1)x(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)S(s[l]);e=!1},d(a){a&&E(t);for(let l=0;l<s.length;l+=1)s[l].d()}}}function _c(r){let t,e,s,n,i,o,a;s=new tc({props:{taskState:r[0].state,taskUuid:r[0].uuid,taskStore:r[1],disabled:!r[2],size:1}});let l={class:"c-task-tree-item__name-editable",value:r[0].name,placeholder:"Name this task...",size:1,disabled:!r[2],clickToEdit:!0};return o=new nc({props:l}),r[11](o),o.$on("keydown",r[10]),o.$on("blur",r[9]),{c(){t=et("div"),e=et("div"),L(s.$$.fragment),n=Gt(),i=et("div"),L(o.$$.fragment),v(e,"class","c-task-tree-item__status-cell svelte-10aflq0"),v(i,"class","c-task-tree-item__name svelte-10aflq0"),$t(i,"c-task-tree-item__text--cancelled",r[8]),v(t,"slot","header-contents"),v(t,"class","c-task-tree-item__header svelte-10aflq0")},m(c,d){U(c,t,d),tt(t,e),F(s,e,null),tt(t,n),tt(t,i),F(o,i,null),a=!0},p(c,d){const p={};1&d&&(p.taskState=c[0].state),1&d&&(p.taskUuid=c[0].uuid),2&d&&(p.taskStore=c[1]),4&d&&(p.disabled=!c[2]),s.$set(p);const f={};1&d&&(f.value=c[0].name),4&d&&(f.disabled=!c[2]),o.$set(f),(!a||256&d)&&$t(i,"c-task-tree-item__text--cancelled",c[8])},i(c){a||(x(s.$$.fragment,c),x(o.$$.fragment,c),a=!0)},o(c){S(s.$$.fragment,c),S(o.$$.fragment,c),a=!1},d(c){c&&E(t),O(s),r[11](null),O(o)}}}function vc(r){let t,e,s;return e=new pc({props:{taskUuid:r[0].uuid,taskStore:r[1],editable:r[2]}}),{c(){t=et("div"),L(e.$$.fragment),v(t,"class","c-task-tree-item__action-buttons svelte-10aflq0"),v(t,"slot","actions")},m(n,i){U(n,t,i),F(e,t,null),s=!0},p(n,i){const o={};1&i&&(o.taskUuid=n[0].uuid),2&i&&(o.taskStore=n[1]),4&i&&(o.editable=n[2]),e.$set(o)},i(n){s||(x(e.$$.fragment,n),s=!0)},o(n){S(e.$$.fragment,n),s=!1},d(n){n&&E(t),O(e)}}}function Ui(r){let t,e,s=[],n=new Map,i=Re(r[7]);const o=a=>a[16].uuid;for(let a=0;a<i.length;a+=1){let l=Ri(r,i,a),c=o(l);n.set(c,s[a]=Li(c,l))}return{c(){t=et("div");for(let a=0;a<s.length;a+=1)s[a].c();v(t,"class","c-task-tree-item__subtasks")},m(a,l){U(a,t,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(t,null);e=!0},p(a,l){134&l&&(i=Re(a[7]),kt(),s=Pi(s,l,o,1,a,i,n,t,Hi,Li,null,Ri),bt())},i(a){if(!e){for(let l=0;l<i.length;l+=1)x(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)S(s[l]);e=!1},d(a){a&&E(t);for(let l=0;l<s.length;l+=1)s[l].d()}}}function Li(r,t){let e,s,n;return s=new wr({props:{taskStore:t[1],task:t[16],editable:t[2],isRootTask:!1}}),{key:r,first:null,c(){e=se(),L(s.$$.fragment),this.first=e},m(i,o){U(i,e,o),F(s,i,o),n=!0},p(i,o){t=i;const a={};2&o&&(a.taskStore=t[1]),128&o&&(a.task=t[16]),4&o&&(a.editable=t[2]),s.$set(a)},i(i){n||(x(s.$$.fragment,i),n=!0)},o(i){S(s.$$.fragment,i),n=!1},d(i){i&&E(e),O(s,i)}}}function yc(r){let t,e,s,n,i;e=new Pl({props:{task:r[0],taskStore:r[1],editable:r[2]}});let o=r[7]&&r[7].length>0&&r[6]&&Ui(r);return{c(){t=et("div"),L(e.$$.fragment),s=Gt(),o&&o.c(),n=se(),v(t,"class","c-task-tree-item__details")},m(a,l){U(a,t,l),F(e,t,null),U(a,s,l),o&&o.m(a,l),U(a,n,l),i=!0},p(a,l){const c={};1&l&&(c.task=a[0]),2&l&&(c.taskStore=a[1]),4&l&&(c.editable=a[2]),e.$set(c),a[7]&&a[7].length>0&&a[6]?o?(o.p(a,l),192&l&&x(o,1)):(o=Ui(a),o.c(),x(o,1),o.m(n.parentNode,n)):o&&(kt(),S(o,1,1,()=>{o=null}),bt())},i(a){i||(x(e.$$.fragment,a),x(o),i=!0)},o(a){S(e.$$.fragment,a),S(o),i=!1},d(a){a&&(E(t),E(s),E(n)),O(e),o&&o.d(a)}}}function Fi(r,t){let e,s,n;return s=new wr({props:{taskStore:t[1],task:t[16],editable:t[2],isRootTask:!1}}),{key:r,first:null,c(){e=se(),L(s.$$.fragment),this.first=e},m(i,o){U(i,e,o),F(s,i,o),n=!0},p(i,o){t=i;const a={};2&o&&(a.taskStore=t[1]),128&o&&(a.task=t[16]),4&o&&(a.editable=t[2]),s.$set(a)},i(i){n||(x(s.$$.fragment,i),n=!0)},o(i){S(s.$$.fragment,i),n=!1},d(i){i&&E(e),O(s,i)}}}function wc(r){let t,e,s,n;const i=[mc,gc],o=[];function a(l,c){return l[3]?0:l[0].isVisible?1:-1}return~(t=a(r))&&(e=o[t]=i[t](r)),{c(){e&&e.c(),s=se()},m(l,c){~t&&o[t].m(l,c),U(l,s,c),n=!0},p(l,[c]){let d=t;t=a(l),t===d?~t&&o[t].p(l,c):(e&&(kt(),S(o[d],1,1,()=>{o[d]=null}),bt()),~t?(e=o[t],e?e.p(l,c):(e=o[t]=i[t](l),e.c()),x(e,1),e.m(s.parentNode,s)):e=null)},i(l){n||(x(e),n=!0)},o(l){S(e),n=!1},d(l){l&&E(s),~t&&o[t].d(l)}}}function kc(r,t,e){let s,n,{task:i}=t,{taskStore:o=bs(wn.key)}=t,{editable:a=!0}=t,{isRootTask:l=!0}=t;const c=bs("chatModel");let d,p,f;async function m(_){const g=_.trim();if(g!==i.name&&g){const C=Fe(z(o.rootTask)),k={action:ys.updateTaskName,totalTasksCount:C.total,triggeredBy:ws.user},y=c.currentConversationModel.chatHistory,M=y.length>0&&y[y.length-1].request_id||c.currentConversationModel.id;c.currentConversationModel.extensionClient.reportAgentSessionEvent({eventName:Ke.taskListUsage,conversationId:M,eventData:{taskListUsageData:k}}),await o.updateTask(i.uuid,{name:g},St.USER)}}return r.$$set=_=>{"task"in _&&e(0,i=_.task),"taskStore"in _&&e(1,o=_.taskStore),"editable"in _&&e(2,a=_.editable),"isRootTask"in _&&e(3,l=_.isRootTask)},r.$$.update=()=>{1&r.$$.dirty&&e(8,s=i.state===N.CANCELLED),1&r.$$.dirty&&e(7,n=function(_){var g;return((g=_.subTasksData)==null?void 0:g.filter(C=>C.isVisible))||[]}(i))},[i,o,a,l,d,p,f,n,s,async function(_){var C;const g=(C=_.target)==null?void 0:C.value;await m(g||"")},async function(_){var g,C;if(!(_.shiftKey||_.ctrlKey||_.metaKey))switch(_.key){case"Enter":{const k=(g=_.target)==null?void 0:g.value;await m(k||"");const y=await o.addNewTaskAfter(i.uuid,{uuid:"new-task",name:"",description:"",state:N.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER});if(!y)return;await ji();const M=document.getElementById(`task-${y.uuid}`);if(!M)return;const D=M.querySelector(".c-task-tree-item__name-editable");if(!D)return;const w=D.querySelector("input");if(!w)return;w.focus(),w.select();break}case"Tab":{const k=(C=_.target)==null?void 0:C.value;await m(k||"");break}}},function(_){Qt[_?"unshift":"push"](()=>{d=_,e(4,d)})},function(_){p=_,e(5,p)},function(_){f=_,e(6,f)}]}class wr extends W{constructor(t){super(),j(this,t,kc,wc,V,{task:0,taskStore:1,editable:2,isRootTask:3})}}function bc(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=P(n,s[i]);return{c(){t=J("svg"),e=new Jt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Xt(t);e=te(o,!0),o.forEach(E),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M153.8 72.1c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 79c-9.4-9.3-24.6-9.3-34 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zm0 160c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zM216 120h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24m-24 136c0 13.3 10.7 24 24 24h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24m-32 160c0 13.3 10.7 24 24 24h304c13.3 0 24-10.7 24-24s-10.7-24-24-24H184c-13.3 0-24 10.7-24 24m-64 0a32 32 0 1 0-64 0 32 32 0 1 0 64 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:R,o:R,d(i){i&&E(t)}}}function xc(r,t,e){return r.$$set=s=>{e(0,t=P(P({},t),rt(s)))},[t=rt(t)]}class bh extends W{constructor(t){super(),j(this,t,xc,bc,V,{})}}class xh{static generateDiff(t,e,s,n){return ro(t,e,s,n)}static generateDiffs(t){return oo(t)}static getDiffStats(t){return Gn(t)}static getDiffObjectStats(t){return Gn(t.diff)}static isNewFile(t){return ao(t)}static isDeletedFile(t){return lo(t)}}function Cc(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),v(e,"fill","currentColor"),v(t,"width","17"),v(t,"height","17"),v(t,"viewBox","0 0 17 17"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class Tc extends W{constructor(t){super(),j(this,t,null,Cc,V,{})}}function Sc(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),v(e,"fill","currentColor"),v(t,"width","15"),v(t,"height","15"),v(t,"viewBox","0 0 15 15"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class $c extends W{constructor(t){super(),j(this,t,null,Sc,V,{})}}function Ec(r){let t,e;return t=new $c({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Ac(r){let t,e;return t=new Tc({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Mc(r){let t,e;return t=new Vi({}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Ic(r){let t,e;return t=new Nr({props:{size:1}}),{c(){L(t.$$.fragment)},m(s,n){F(t,s,n),e=!0},i(s){e||(x(t.$$.fragment,s),e=!0)},o(s){S(t.$$.fragment,s),e=!1},d(s){O(t,s)}}}function Rc(r){let t,e,s,n,i;const o=[Ic,Mc,Ac,Ec],a=[];function l(c,d){return c[0]==="loading"?0:c[0]==="success"?1:c[0]==="error"?2:c[0]==="skipped"?3:-1}return~(e=l(r))&&(s=a[e]=o[e](r)),{c(){t=et("div"),s&&s.c(),v(t,"class",n="c-setup-script-command-status c-setup-script-command-status--"+r[0]+" svelte-1azgu93")},m(c,d){U(c,t,d),~e&&a[e].m(t,null),i=!0},p(c,[d]){let p=e;e=l(c),e!==p&&(s&&(kt(),S(a[p],1,1,()=>{a[p]=null}),bt()),~e?(s=a[e],s||(s=a[e]=o[e](c),s.c()),x(s,1),s.m(t,null)):s=null),(!i||1&d&&n!==(n="c-setup-script-command-status c-setup-script-command-status--"+c[0]+" svelte-1azgu93"))&&v(t,"class",n)},i(c){i||(x(s),i=!0)},o(c){S(s),i=!1},d(c){c&&E(t),~e&&a[e].d()}}}function Dc(r,t,e){let{commandResult:s}=t;return r.$$set=n=>{"commandResult"in n&&e(0,s=n.commandResult)},[s]}class Ch extends W{constructor(t){super(),j(this,t,Dc,Rc,V,{commandResult:0})}}function Uc(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"fill-rule","evenodd"),v(e,"clip-rule","evenodd"),v(e,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),v(e,"fill","currentColor"),v(t,"width","15"),v(t,"height","15"),v(t,"viewBox","0 0 15 15"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class Th extends W{constructor(t){super(),j(this,t,null,Uc,V,{})}}function Lc(r){let t,e;return{c(){t=J("svg"),e=J("path"),v(e,"d","M14.5 3H7.70996L6.85999 2.15002L6.51001 2H1.51001L1.01001 2.5V6.5V13.5L1.51001 14H14.51L15.01 13.5V9V3.5L14.5 3ZM13.99 11.49V13H1.98999V11.49V7.48999V7H6.47998L6.82996 6.84998L7.68994 5.98999H14V7.48999L13.99 11.49ZM13.99 5H7.48999L7.14001 5.15002L6.28003 6.01001H2V3.01001H6.29004L7.14001 3.85999L7.5 4.01001H14L13.99 5Z"),v(e,"fill","#C5C5C5"),v(t,"width","16"),v(t,"height","16"),v(t,"viewBox","0 0 16 16"),v(t,"fill","none"),v(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){U(s,t,n),tt(t,e)},p:R,i:R,o:R,d(s){s&&E(t)}}}class Sh extends W{constructor(t){super(),j(this,t,null,Lc,V,{})}}export{dh as $,pt as A,fh as B,Qo as C,oh as D,yh as E,wh as F,Ko as G,Fe as H,N as I,vi as J,kh as K,fc as L,ka as M,yt as N,wr as O,bh as P,xh as Q,lh as R,ah as S,Sl as T,vh as U,Ch as V,Th as W,fo as X,Sh as Y,Zi as Z,Tc as _,ph as a,St as a0,hh as a1,di as a2,uo as a3,$c as b,th as c,Q as d,Xc as e,Bs as f,eh as g,sh as h,$e as i,ti as j,Sa as k,Wo as l,rh as m,nh as n,gs as o,Ta as p,uh as q,ih as r,at as s,va as t,wn as u,ch as v,po as w,gh as x,mh as y,_h as z};
