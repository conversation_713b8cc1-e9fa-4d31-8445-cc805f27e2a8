var U=Object.defineProperty;var q=(g,e,t)=>e in g?U(g,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):g[e]=t;var d=(g,e,t)=>q(g,typeof e!="symbol"?e+"":e,t);import{b as E,A as y,c as w,a as p,R}from"./types-DDm27S8B.js";import{W as l,R as H}from"./BaseButton-ESlFPUk1.js";import{a as I}from"./chat-types-DOHETl9Q.js";import{k as x,l as B}from"./types-DwxhLPcD.js";import{t as N}from"./index-C4SxYn1J.js";import{N as W,al as O,ag as $,an as G,S as z,i as J,s as V,a as C,b as X,H as Y,w as j,x as K,y as Z,h as M,d as P,z as Q,g as ee,n as k,j as T}from"./SpinnerAugment-CQKp6jSN.js";import{m as te,S as se,E as F}from"./arrow-up-right-from-square-ChzPb9WB.js";import{a as ne}from"./utils-C8gPzElB.js";import{R as ae}from"./ra-diff-ops-model-CRIDwIDf.js";import{S as re,a as ie}from"./types-CGlLNakm.js";var v=(g=>(g[g.unknown=0]="unknown",g[g.new=1]="new",g[g.checkingSafety=2]="checkingSafety",g[g.runnable=3]="runnable",g[g.running=4]="running",g[g.completed=5]="completed",g[g.error=6]="error",g[g.cancelling=7]="cancelling",g[g.cancelled=8]="cancelled",g))(v||{});function Ee(g){return g.requestId+";"+g.toolUseId}function Ie(g){const[e,t]=g.split(";");return{requestId:e,toolUseId:t}}function xe(g,e){return g==null?e:typeof g=="string"?g:e}function A(g){return g.sort((e,t)=>{const s=new Date(e.updated_at||e.started_at);return new Date(t.updated_at||t.started_at).getTime()-s.getTime()})}class S extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class D{constructor(e,t,s,n,a=5,r=4e3,i=2,o){d(this,"_isCancelled",!1);d(this,"_isExhausted",!1);d(this,"streamId");this.agentId=e,this.lastProcessedValue=t,this.startStreamFn=s,this.cancelStreamFn=n,this.maxRetries=a,this.baseDelay=r,this.attemptErrorThreshold=i,this.unhandledErrorMessage=o,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}get isExhausted(){return this._isExhausted}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;)try{const t=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedValue);for await(const s of t){if(this._isCancelled)return;e=0,yield s}return}catch(t){const s=t instanceof Error?t.message:String(t);if(s===re&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw this._isExhausted=!0,new S(`Failed after ${this.maxRetries} attempts: ${s}`);let n=this.baseDelay*2**(e-1);s===ie?n=0:e>this.attemptErrorThreshold&&(yield{errorMessage:this.unhandledErrorMessage??s,retryAt:new Date(Date.now()+n)}),console.warn(`Retrying remote agent stream in ${n/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(a=>setTimeout(a,n));continue}}}class oe extends D{constructor(e,t,s,n,a=5,r=4e3){super(e,t,s,n,a,r,1,"There was an error connecting to the remote agent.")}}class ge extends D{constructor(e,t,s,n=5,a=4e3){super("overviews",e,t,s,n,a,2,void 0)}}class L{constructor(e){d(this,"_msgBroker");d(this,"_activeRetryStreams",new Map);d(this,"_activeOverviewsStream");this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}hasActiveOverviewsStream(){return this._activeOverviewsStream!==void 0&&!this._activeOverviewsStream.isCancelled}getActiveOverviewsStream(){return this._activeOverviewsStream}async sshToRemoteAgent(e){const t=await this._msgBroker.send({type:l.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e,t=!1){return(await this._msgBroker.send({type:l.deleteRemoteAgentRequest,data:{agentId:e,doSkipConfirmation:t}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:l.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:l.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:l.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._msgBroker.send({type:l.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:l.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:l.remoteAgentNotifyReady,data:e})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:l.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:l.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._msgBroker.send({type:l.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=9e4){return this._msgBroker.send({type:l.remoteAgentChatRequest,data:{agentId:e,requestDetails:t,timeoutMs:s}},s)}async interruptRemoteAgent(e,t=1e4){return await this._msgBroker.send({type:l.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,n,a,r,i=1e4){return await this._msgBroker.send({type:l.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:n,modelId:a,remoteAgentCreationMetrics:r}},i)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:l.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:l.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:l.saveSetupScriptRequest,data:{name:e,content:t,location:s}},n)}async deleteSetupScript(e,t,s=5e3){return await this._msgBroker.send({type:l.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:l.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},n)}async getRemoteAgentWorkspaceLogs(e,t,s,n=1e4){return await this._msgBroker.send({type:l.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:t,lastProcessedSequenceId:s}},n)}async saveLastRemoteAgentSetup(e,t,s){return await this._msgBroker.send({type:l.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:l.getLastRemoteAgentSetupRequest})}async*startRemoteAgentOverviewsStream(e,t,s=6e4,n=3e5){const a={type:l.remoteAgentOverviewsStreamRequest,data:{streamId:e,lastUpdateTimestamp:t}},r=this._msgBroker.stream(a,s,n);for await(const i of r){if(i.data.error)throw new Error(i.data.error);yield i.data.response}}async*startRemoteAgentHistoryStream(e,t,s,n=6e4,a=3e5){const r={type:l.remoteAgentHistoryStreamRequest,data:{streamId:t,agentId:e,lastProcessedSequenceId:s}},i=this._msgBroker.stream(r,n,a);for await(const o of i)yield o.data}async*startRemoteAgentsListStreamWithRetry(e,t=5,s=4e3){var a;const n=new ge(e,(r,i,o)=>this.startRemoteAgentOverviewsStream(i,o),r=>this._closeRemoteAgentsStream(r),t,s);(a=this._activeOverviewsStream)==null||a.cancel(),this._activeOverviewsStream=n;try{yield*n.getStream()}finally{n.isCancelled||n.isExhausted||(this._activeOverviewsStream=void 0)}}async*startRemoteAgentHistoryStreamWithRetry(e,t,s=5,n=4e3){var r;const a=new oe(e,t,(i,o,c)=>this.startRemoteAgentHistoryStream(i,o,c),i=>this._closeRemoteAgentsStream(i),s,n);(r=this._activeRetryStreams.get(e))==null||r.cancel(),this._activeRetryStreams.set(e,a);try{yield*a.getStream()}finally{a.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentOverviewsStream(){this._activeOverviewsStream&&(this._activeOverviewsStream.cancel(),this._activeOverviewsStream=void 0)}cancelRemoteAgentHistoryStream(e){const t=this._activeRetryStreams.get(e);t&&(t.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentsStream(e){await this._msgBroker.send({type:l.cancelRemoteAgentsStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelRemoteAgentOverviewsStream(),this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:l.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,t){try{await this._msgBroker.send({type:l.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:t}})}catch(s){console.error("Failed to save pinned agent to store:",s)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:l.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(t){console.error("Failed to delete pinned agent from store:",t)}}async openDiffInBuffer(e,t,s){return await this._msgBroker.send({type:l.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:l.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:l.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async resumeHintRemoteAgent(e,t=H.viewingAgent){return await this._msgBroker.send({type:l.remoteAgentResumeHintRequest,data:{agentId:e,hintReason:t}},1e4)}async updateRemoteAgentTitle(e,t,s=1e4){return await this._msgBroker.send({type:l.updateRemoteAgentRequest,data:{agentId:e,newTitle:t}},s)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:l.reportRemoteAgentEvent,data:e})}async getRemoteAgentStatus(){return await this._msgBroker.send({type:l.getRemoteAgentStatus})}async focusAugmentPanel(){await this._msgBroker.send({type:l.showAugmentPanel})}}d(L,"key","remoteAgentsClient");function ce(g,e){if(g.length===0)return e;if(e.length===0)return g;const t=[];let s=0,n=0;for(;s<g.length&&n<e.length;){const a=g[s].sequence_id,r=e[n].sequence_id;r!==void 0?a!==void 0?a<r?(t.push(g[s]),s++):a>r?(t.push(e[n]),n++):(t.push(e[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<g.length;)t.push(g[s]),s++;for(;n<e.length;)t.push(e[n]),n++;return t}class he{constructor(e){d(this,"_pollingTimers",new Map);d(this,"_pollingInterval");d(this,"_failedAttempts",0);d(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class de{constructor(e,t){d(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});d(this,"_loggingMaxRetries",8);d(this,"_logsPollingManager");d(this,"_isInitialOverviewFetch",!0);d(this,"_lastOverviewUpdateTimestamp");d(this,"_stateUpdateSubscribers",new Set);d(this,"_pendingStateUpdateOpts");this._flagsModel=e,this._remoteAgentsClient=t,this._logsPollingManager=new he({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(i=>i.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const a=this.state.agentLogs.get(n),r=a==null?void 0:a.steps.at(-1);return(r==null?void 0:r.step_description)==="Indexing"&&r.status===E.success}}),this._flagsModel.subscribe(s=>{const n=this._remoteAgentsClient.hasActiveOverviewsStream()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),a=s.enableBackgroundAgents;a&&!n?this.startStateUpdates(this._pendingStateUpdateOpts):!a&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;if(!this._flagsModel.enableBackgroundAgents)return this._pendingStateUpdateOpts={...this._pendingStateUpdateOpts,...e},void(e||(this._pendingStateUpdateOpts.overviews=!0));e?(e.overviews&&this.startOverviewsStream(),(t=e.conversation)!=null&&t.agentId&&this.startConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this.startOverviewsStream()}_removePendingStateUpdates(e){var t,s,n,a;this._pendingStateUpdateOpts&&(e?(e.overviews&&(this._pendingStateUpdateOpts.overviews=!1),((t=e.conversation)==null?void 0:t.agentId)===((s=this._pendingStateUpdateOpts.conversation)==null?void 0:s.agentId)&&(this._pendingStateUpdateOpts.conversation=void 0),((n=e.logs)==null?void 0:n.agentId)===((a=this._pendingStateUpdateOpts.logs)==null?void 0:a.agentId)&&(this._pendingStateUpdateOpts.logs=void 0)):this._pendingStateUpdateOpts=void 0)}stopStateUpdates(e){var t,s;if(this._removePendingStateUpdates(e),!e)return this.stopOverviewsStream(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();e.overviews&&this.stopOverviewsStream(),(t=e.conversation)!=null&&t.agentId&&this.stopConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){this.startConversationStream(e)}async refreshAgentOverviews(){return this.startOverviewsStream(),this._state.agentOverviews}async refreshAgentLogs(e){try{const t=this.state.agentLogs.get(e);let s,n;const a=t==null?void 0:t.steps.at(-1);a?(s=a.step_number,n=a.step_number===0?0:a.sequence_id+1):(s=0,n=0);const r=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,s,n);if(!r.data.workspaceSetupStatus)return;const i=r.data.workspaceSetupStatus;if(i.steps.length===0)return t;const o=function(h,u){return{steps:[...h.steps,...u.steps].sort((_,m)=>_.step_number!==m.step_number?_.step_number-m.step_number:_.sequence_id-m.sequence_id)}}(t??{steps:[]},i),c={steps:o.steps.reduce((h,u)=>{const _=h[h.length-1];return _&&_.step_number===u.step_number?(_.status!==E.success&&(_.status=u.status),_.step_number===0?_.logs=u.logs:_.sequence_id<u.sequence_id&&(_.logs+=`
${u.logs}`,_.sequence_id=u.sequence_id)):h.push(u),h},[])};return this._state.agentLogs.set(e,c),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:c}),c}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(e)}}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}async startConversationStream(e){this._remoteAgentsClient.hasActiveHistoryStream(e)&&this.stopConversationStream(e);const t=this._state.agentConversations.get(e)||[];let s=0;t.length>0&&(s=Math.max(...t.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(e,s);(async()=>{var a;try{for await(const r of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(e)||(a=this._remoteAgentsClient.getActiveHistoryStream(e))!=null&&a.isCancelled)break;this.processHistoryStreamUpdate(e,r)}}catch(r){if(this._remoteAgentsClient.hasActiveHistoryStream(e)){let i;r instanceof S?(i=`Failed to connect: ${r.message}`,console.error(`Stream retry exhausted for agent ${e}: ${r.message}`)):(i=r instanceof Error?r.message:String(r),console.error(`Stream error for agent ${e}: ${i}`)),this._state.conversationError={errorMessage:i},this._state.isConversationLoading=!1;const o=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:o,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let a;a=n instanceof S?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:a},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError})}}stopConversationStream(e){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(e)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(e,t){var s;if((n=>n!=null&&n.updates!==void 0)(t)){this._state.conversationError=void 0;for(const n of t.updates){const a=this._state.agentConversations.get(e)||[];switch(n.type){case y.AGENT_HISTORY_EXCHANGE:if(n.exchange){const r=ce(a,[n.exchange]);this._state.agentConversations.set(e,r)}break;case y.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const r=n.exchange_update.sequence_id,i=a.findIndex(o=>o.sequence_id===r);if(i>=0){const o=a[i],c=((s=o.exchange)==null?void 0:s.response_text)||"";o.exchange.response_text=c+n.exchange_update.appended_text;const h=n.exchange_update.appended_nodes;if(h&&h.length>0){const _=o.exchange.response_nodes??[];o.exchange.response_nodes=[..._,...h]}const u=n.exchange_update.appended_changed_files;if(u&&u.length>0){const _=o.changed_files??[];o.changed_files=[..._,...u]}}}break;case y.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const r=this._state.agentOverviews.findIndex(i=>i.remote_agent_id===e);r>=0?this._state.agentOverviews[r]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=A(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:this._state.conversationError})}else{this.state.conversationError=t;const n=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:n,error:this._state.conversationError})}}async startOverviewsStream(){this._state.isOverviewsLoading=!0,this._state.overviewError=void 0,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError});try{const e=this._remoteAgentsClient.startRemoteAgentsListStreamWithRetry(this._lastOverviewUpdateTimestamp);(async()=>{try{for await(const t of e){if(!this._remoteAgentsClient.hasActiveOverviewsStream())break;this.processOverviewsStreamUpdate(t)}}catch(t){if(this._remoteAgentsClient.hasActiveOverviewsStream()){let s;t instanceof S?(s=`Failed to connect: ${t.message}`,console.error(`Overview stream retry exhausted: ${t.message}`)):(s=t instanceof Error?t.message:String(t),console.error(`Overview stream error: ${s}`)),this._state.overviewError={errorMessage:s},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}finally{this._state.isOverviewsLoading=!1}})()}catch(e){let t;t=e instanceof S?`Failed to connect: ${e.message}`:e instanceof Error?e.message:String(e),this._state.overviewError={errorMessage:t},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}stopOverviewsStream(){this._remoteAgentsClient.cancelRemoteAgentOverviewsStream()}processOverviewsStreamUpdate(e){if(!(t=>t!=null&&t.updates!==void 0)(e))return this._state.overviewError=e,void this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError});this._state.overviewError=void 0;for(const t of e.updates)switch(t.update_timestamp&&(this._lastOverviewUpdateTimestamp=t.update_timestamp),t.max_agents!==void 0&&(this._state.maxRemoteAgents=t.max_agents),t.max_active_agents!==void 0&&(this._state.maxActiveRemoteAgents=t.max_active_agents),t.type){case w.AGENT_LIST_ALL_AGENTS:t.all_agents&&(this._state.agentOverviews=A(t.all_agents));break;case w.AGENT_LIST_AGENT_ADDED:t.agent&&(this._state.agentOverviews.push(t.agent),this._state.agentOverviews=A(this._state.agentOverviews));break;case w.AGENT_LIST_AGENT_UPDATED:if(t.agent){const s=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===t.agent.remote_agent_id);s>=0&&(this._state.agentOverviews[s]=t.agent,this._state.agentOverviews=A(this._state.agentOverviews))}break;case w.AGENT_LIST_AGENT_DELETED:t.deleted_agent_id&&(this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==t.deleted_agent_id))}this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:structuredClone(this._state.agentOverviews),error:this._state.overviewError})}}var f=(g=>(g.chatRequestFailed="chat_request_failed",g.messageTimeout="message_timeout",g.agentFailed="agent_failed",g))(f||{});const b="This agent is in a failed state and can no longer accept messages";class le{constructor({msgBroker:e,isActive:t,flagsModel:s,host:n,stateModel:a}){d(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),setPinnedAgents:this.setPinnedAgents.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this),isRemoteAgentSshWindow:!1,remoteAgentSshWindowId:void 0});d(this,"_agentConversations",new Map);d(this,"_initialPrompts",new Map);d(this,"_agentSetupLogsCache",new Map);d(this,"_creationMetrics");d(this,"_preloadedDiffExplanations",new Map);d(this,"maxCacheEntries",10);d(this,"maxCacheSizeBytes",10485760);d(this,"_diffOpsModel");d(this,"subscribers",new Set);d(this,"agentSetupLogs");d(this,"_remoteAgentsClient");d(this,"_stateModel");d(this,"_extensionClient");d(this,"_flagsModel");d(this,"_cachedUrls",new Map);d(this,"_pendingMessageTracking",new Map);d(this,"_agentSendMessageErrors",new Map);d(this,"sendMessageTimeoutMs",9e4);d(this,"_hasEverUsedRemoteAgent",W(void 0));d(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this._cachedUrls.clear(),this._pendingMessageTracking.forEach(e=>{e.forEach(t=>clearTimeout(t.timeout))}),this._pendingMessageTracking.clear(),this._agentSendMessageErrors.clear(),this.subscribers.clear()});d(this,"setHasEverUsedRemoteAgent",e=>{this._extensionClient.setHasEverUsedRemoteAgent(e),this._hasEverUsedRemoteAgent.set(e)});d(this,"refreshHasEverUsedRemoteAgent",async()=>{if(O(this.hasEverUsedRemoteAgent)!==void 0)return;const e=await this._extensionClient.checkHasEverUsedRemoteAgent();O(this.hasEverUsedRemoteAgent)===void 0&&this._hasEverUsedRemoteAgent.set(e)});d(this,"throttledGetDiffExplanation",N(async e=>await this._diffOpsModel.getDiffExplanation(e,void 0,6e4),1e3));this._state.isActive=t,this._flagsModel=s,this._diffOpsModel=new ae(e),this._remoteAgentsClient=new L(e),this._extensionClient=new te(n,e,s),this._stateModel=a||new de(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),this._flagsModel.subscribe(r=>{r.enableBackgroundAgents?this._stateModel.startStateUpdates():(this.setIsActive(!1),this._stateModel.stopStateUpdates())}),this.loadPinnedAgentsFromStore(),this.refreshHasEverUsedRemoteAgent(),this.checkRemoteAgentStatus()}async checkRemoteAgentStatus(){const e=await this._remoteAgentsClient.getRemoteAgentStatus();this._state.isRemoteAgentSshWindow=e.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=e.data.remoteAgentId,this.notifySubscribers()}handleOverviewsUpdate(e){var r,i;const t=e.data,s=this._state.agentOverviews,n=t;if(this.isActive&&n.forEach(o=>{o.remote_agent_id===this._state.currentAgentId&&(o.has_updates=!1)}),this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(o=>o.remote_agent_id===this._state.currentAgentId)),this._state.currentAgentId){const o=this._state.currentAgentId,c=o?(r=s.find(u=>u.remote_agent_id===o))==null?void 0:r.status:void 0,h=o?(i=n.find(u=>u.remote_agent_id===o))==null?void 0:i.status:void 0;if(h!==c)if(h===p.agentFailed){const u={type:f.agentFailed,errorMessage:b,canRetry:!1};this._agentSendMessageErrors.set(o,u)}else this._agentSendMessageErrors.delete(o)}this.maybeSendNotifications(n,s);const a=A(n);this._state.agentOverviews=a,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),a.findIndex(o=>o.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){var t;if(e.agentId===this._state.currentAgentId){const s={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,s),this._state.currentConversation=s,this._state.agentChatHistoryError=e.error;const n=this._agentSendMessageErrors.get(e.agentId);if(n!=null&&n.failedExchangeId){const a=n.failedExchangeId;((t=this._state.currentConversation)==null?void 0:t.exchanges.some(i=>i.exchange.request_id===a))||this._agentSendMessageErrors.delete(e.agentId)}this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.agentChatHistoryError=e.data.conversationError,this._state.agentLogsError=e.data.logsError}this.currentAgentId&&this.checkForHistoryErrors(this.currentAgentId),this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(this.reportRemoteAgentEvent({eventName:x.diffPanel,remoteAgentId:t??"",eventData:{diffPanelData:{applied:!1,loadingTimeMs:0}}}),t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation,remoteAgentId:t}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,remoteAgentId:t}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},a)=>{const r=n.request_id.startsWith("pending-");return{seen_state:se.seen,structured_request_nodes:n.request_nodes??[],status:r||a===t.exchanges.length-1&&s?F.sent:F.success,request_message:n.request_message,response_text:r?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${a}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var i,o,c;const e=new Map,t=new Set,s=new Map;(i=this.currentConversation)==null||i.exchanges.forEach(h=>{var u,_;(u=h.exchange.response_nodes)==null||u.forEach(m=>{m.tool_use&&t.add(m.tool_use.tool_use_id)}),(_=h.exchange.request_nodes)==null||_.forEach(m=>{m.type===I.TOOL_RESULT&&m.tool_result_node&&s.set(m.tool_result_node.tool_use_id,m.tool_result_node)})});const n=(o=this.currentConversation)==null?void 0:o.exchanges[this.currentConversation.exchanges.length-1];let a=0,r=null;return(c=n==null?void 0:n.exchange.response_nodes)==null||c.forEach(h=>{var u;h.id>a&&(a=h.id,r=(u=h.tool_use)!=null&&u.tool_use_id?h.tool_use.tool_use_id:null)}),t.forEach(h=>{const u=s.get(h);if(u)e.set(h,{phase:u.is_error?v.error:v.completed,result:{isError:u.is_error,text:u.content},requestId:"",toolUseId:h});else{const _=this.isCurrentAgentRunning;h===r?e.set(h,{phase:_?v.running:v.cancelled,requestId:"",toolUseId:h}):e.set(h,{phase:v.cancelled,requestId:"",toolUseId:h})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:v.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:v.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){if(this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,e&&this.checkForHistoryErrors(e),this.notifySubscribers(),e){this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}});try{const t=this._state.agentOverviews.find(s=>s.remote_agent_id===e);!t||t.workspace_status!==R.workspacePaused&&t.workspace_status!==R.workspacePausing||await this._remoteAgentsClient.resumeHintRemoteAgent(e)}catch(t){console.warn("Failed to send resume hint to remote agent:",t)}this.preloadDiffExplanation(e)}}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=ne(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let a=0;if(s.forEach(r=>{var i,o;a+=(((i=r.old_contents)==null?void 0:i.length)||0)+(((o=r.new_contents)==null?void 0:o.length)||0)}),!(a>512e3))try{const r=await this.throttledGetDiffExplanation(s);if(r&&r.length>0){const i=this.generateChangedFilesHash(s),o=`${e}-${i}`;this._preloadedDiffExplanations.set(o,{explanation:r,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:i,turnIdx:-1}),this.manageCacheSize()}}catch(r){console.error("Failed to preload diff explanation:",r)}}async getDiffDescriptions(e,t){return this._diffOpsModel.getDescriptions(e,t)}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,a;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((a=s.new_contents)==null?void 0:a.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,a=s.value.changedFiles.reduce((r,i)=>{var o,c;return r+(((o=i.old_contents)==null?void 0:o.length)||0)+(((c=i.new_contents)==null?void 0:c.length)||0)},0);t+=n+a});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,a=s.value.changedFiles.reduce((r,i)=>{var o,c;return r+(((o=i.old_contents)==null?void 0:o.length)||0)+(((c=i.new_contents)==null?void 0:c.length)||0)},0);t-=n+a}}}async sendMessage(e,t){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;const n=this._state.agentOverviews.find(r=>r.remote_agent_id===s);if((n==null?void 0:n.status)===p.agentFailed){const r={type:f.agentFailed,errorMessage:b,canRetry:!1};return this._agentSendMessageErrors.set(s,r),this.notifySubscribers(),!1}let a;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const r=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},i=(this.getfinalSequenceId(s)||0)+1;a="pending-"+Date.now();const o={exchange:{request_message:e,response_text:"",request_id:a,response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:i};r.exchanges.push(o),this._agentConversations.set(s,r),this._state.currentConversation=r,this.notifySubscribers(),this.setupMessageTimeout(s,a);const c={request_nodes:[{id:1,type:I.TEXT,text_node:{content:e}}],model_id:t},h=await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,c,this.sendMessageTimeoutMs);if(h.data.error)throw new Error(h.data.error);return this.clearMessageTimeout(s,a),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),this.preloadDiffExplanation(s),!0}catch(r){a&&this.clearMessageTimeout(s,a);const i={type:f.chatRequestFailed,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${s}. ${r}`,canRetry:!0,failedExchangeId:a};return this._agentSendMessageErrors.set(s,i),console.error("Failed to send message:",r),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}setupMessageTimeout(e,t){const s=setTimeout(()=>{this.handleMessageTimeout(e,t)},this.sendMessageTimeoutMs);this._pendingMessageTracking.has(e)||this._pendingMessageTracking.set(e,new Map),this._pendingMessageTracking.get(e).set(t,{timeout:s,timestamp:Date.now()})}clearMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);if(s){const n=s.get(t);n&&(clearTimeout(n.timeout),s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e))}}async handleMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);s&&(s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e));const n=this._state.agentOverviews.find(i=>i.remote_agent_id===e);if((n==null?void 0:n.status)===p.agentRunning)return;const a=this._agentConversations.get(e);if(!a||!a.exchanges.find(i=>i.exchange.request_id===t))return;const r={type:f.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:t};this._agentSendMessageErrors.set(e,r);try{await this._remoteAgentsClient.interruptRemoteAgent(e)}catch(i){console.error("Failed to interrupt agent after timeout:",i)}this.notifySubscribers()}removeOptimisticExchange(e,t){const s=this._agentConversations.get(e);s&&(s.exchanges=s.exchanges.filter(n=>n.exchange.request_id!==t),this._agentConversations.set(e,s),e===this._state.currentAgentId&&(this._state.currentConversation=s))}async retryFailedMessage(e,t){const s=this._agentConversations.get(e);if(!s)return!1;const n=s.exchanges.find(a=>a.exchange.request_id===t);return!!n&&(this.removeOptimisticExchange(e,t),this._agentSendMessageErrors.delete(e),this.notifySubscribers(),this.sendMessage(n.exchange.request_message))}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e)}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n,a){var r;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const i=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n,a,this._creationMetrics);if(i.data.error)throw new Error(i.data.error);if(i.data.agentId&&i.data.success)return this._initialPrompts.set(i.data.agentId,e),await this.setNotificationEnabled(i.data.agentId,((r=this.newAgentDraft)==null?void 0:r.enableNotification)??!0),await this.setCurrentAgent(i.data.agentId),i.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(i){throw this._state.error=i instanceof Error?i.message:String(i),this.notifySubscribers(),i}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e,t){var a,r;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");if(!s.commitRef||!s.selectedBranch)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const i=s.isSetupScriptAgent||((a=s.setupScript)==null?void 0:a.isGenerateOption)===!0;let o=i||(r=s.setupScript)==null?void 0:r.content;if(s.setupScript&&!i){const c=(await this.listSetupScripts()).find(h=>h.path===s.setupScript.path);c&&(o=c.content)}try{return await this.createRemoteAgent(e,n,o,i,t)}catch(c){let h="Failed to create remote agent. Please try again.";return c instanceof Error&&(c.message.includes("too large")||c.message.includes("413")?h="Repository or selected files are too large. Please select a smaller repository or branch.":c.message.includes("timeout")||c.message.includes("504")?h="Request timed out. The repository might be too large or the server is busy.":c.message.includes("rate limit")||c.message.includes("429")?h="Rate limit exceeded. Please try again later.":c.message.includes("unauthorized")||c.message.includes("401")?h="Authentication failed. Please check your GitHub credentials.":c.message.includes("not found")||c.message.includes("404")?h="Repository or branch not found. Please check your selection.":c.message.includes("bad request")||c.message.includes("400")?h="Invalid request. Please check your workspace setup and try again.":c.message.length>0&&(h=`Failed to create remote agent: ${c.message}`)),void this.setRemoteAgentCreationError(h)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e,t=!1){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e,t))return this._state.error="Failed to delete remote agent",this.notifySubscribers(),!1;this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}return!this._state.error}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return e.workspace_status!==R.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e.remote_agent_id),await new Promise(t=>setTimeout(t,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(a=>[a.remote_agent_id,a])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(a=>a.remote_agent_id));e.forEach(a=>{const r=s.get(a.remote_agent_id),i=n[a.remote_agent_id],o=(r==null?void 0:r.status)===p.agentRunning,c=a.status===p.agentIdle||a.status===p.agentFailed,h=a.remote_agent_id!==this._state.currentAgentId,u=this._state.isPanelFocused;i&&o&&c&&(h||!u)&&this._remoteAgentsClient.notifyRemoteAgentReady(a)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){switch(e.data.type){case l.diffViewFileFocus:return this.setFocusedFilePath(e.data.data.filePath.replace(/^\/+/,"")),!0;case l.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case l.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;case l.remoteAgentStatusChanged:{const t=e.data;return this._state.isRemoteAgentSshWindow=t.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=t.data.remoteAgentId,this.notifySubscribers(),!0}default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||p.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}clearSendMessageError(){this._state.currentAgentId&&(this._agentSendMessageErrors.delete(this._state.currentAgentId),this.notifySubscribers())}get sendMessageError(){return this._agentSendMessageErrors.get(this._state.currentAgentId??"")??void 0}checkForHistoryErrors(e){var a;const t=this._getAgentExchanges(e);if(((a=this._state.agentOverviews.find(r=>r.remote_agent_id===e))==null?void 0:a.status)===p.agentFailed){const r={type:f.agentFailed,errorMessage:b,canRetry:!1};return this._agentSendMessageErrors.set(e,r),void this.notifySubscribers()}const s=t.length>0&&t[t.length-1].exchange.request_id.startsWith("pending-"),n=this._agentSendMessageErrors.get(e);if(s&&!n){const r=t[t.length-1].exchange.request_id,i=this._pendingMessageTracking.get(e),o=i==null?void 0:i.get(r);if(o&&Date.now()-o.timestamp>this.sendMessageTimeoutMs){const c={type:f.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:r};this._agentSendMessageErrors.set(e,c),this.clearMessageTimeout(e,r),this.notifySubscribers()}}}isAgentRunning(e){const t=this._state.agentOverviews.find(i=>i.remote_agent_id===e),s=!(!t||t.status!==p.agentRunning&&t.status!==p.agentStarting),n=this._getAgentExchanges(e),a=this._agentSendMessageErrors.get(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-")&&!a;return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e;const t=this._state.currentAgentId,s={conversation:t?{agentId:t}:void 0,logs:t?{agentId:t}:void 0};e?this._stateModel.startStateUpdates(s):this._stateModel.stopStateUpdates(s),this.notifySubscribers()}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}optimisticallyClearAgentUpdates(e){var s;const t=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===e);if(t!==-1&&this._state.agentOverviews[t].has_updates){const n=[...this._state.agentOverviews];n[t]={...n[t],has_updates:!1},this._state.agentOverviews=n,((s=this._state.currentAgent)==null?void 0:s.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,has_updates:!1}),this.notifySubscribers()}}async updateRemoteAgentTitle(e,t){var a,r,i;const s=this._state.agentOverviews.findIndex(o=>o.remote_agent_id===e);if(s===-1)return void console.warn(`Agent with ID ${e} not found in overviews`);const n=this._state.agentOverviews[s].title;try{const o=[...this._state.agentOverviews];o[s]={...o[s],title:t},this._state.agentOverviews=o,((a=this._state.currentAgent)==null?void 0:a.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,title:t}),this.notifySubscribers();const c=await this._remoteAgentsClient.updateRemoteAgentTitle(e,t);if(!c.data.success||!c.data.agent)throw console.error("Failed to update remote agent title:",c.data.error),this._state.error=c.data.error||"Failed to update remote agent title",new Error(this._state.error);{const h=[...this._state.agentOverviews];h[s]=c.data.agent,this._state.agentOverviews=h,((r=this._state.currentAgent)==null?void 0:r.remote_agent_id)===e&&(this._state.currentAgent=c.data.agent),this.notifySubscribers()}}catch(o){const c=[...this._state.agentOverviews];c[s]={...c[s],title:n},this._state.agentOverviews=c,((i=this._state.currentAgent)==null?void 0:i.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,title:n});const h=o instanceof Error?o.message:String(o);throw this._state.error=h,this.notifySubscribers(),o}}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}setCreationMetrics(e){this._creationMetrics=e}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(e){this._stateModel.refreshCurrentAgent(e)}get newAgentDraft(){return this._state.newAgentDraft}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}get isRemoteAgentSshWindow(){return this._state.isRemoteAgentSshWindow}get remoteAgentSshWindowId(){return this._state.remoteAgentSshWindowId}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const e=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=e,this.notifySubscribers()}catch(e){console.error("Failed to load pinned agents from store:",e)}}async toggleAgentPinned(e,t){if(!e)return this._state.pinnedAgents;t=t??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[e]:!t},t){const{[e]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(e)}else await this._remoteAgentsClient.savePinnedAgentToStore(e,!0);return this.notifySubscribers(),await this._remoteAgentsClient.getPinnedAgentsFromStore()}catch(s){return console.error("Failed to toggle pinned status for remote agent:",s),this._state.pinnedAgents}}async getConversationUrl(e){var o;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const a=this._getChatHistory(e).map(c=>({...c,request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}));if(a.length===0)throw new Error("No chat history to share");const r=await this._extensionClient.saveChat(e,a,`Remote Agent ${e}`);if(!r.data)throw new Error("Failed to create URL");const i=(o=r.data)==null?void 0:o.url;return i&&this._cachedUrls.set(e,[n,i]),i}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}async pauseRemoteAgentWorkspace(e){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(e)}async resumeRemoteAgentWorkspace(e){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e)}async reportRemoteAgentEvent(e){await this._remoteAgentsClient.reportRemoteAgentEvent(e)}getSourceControlType(){return B.unknownSourceControl}async reportChatModeEvent(e,t){try{await this.reportRemoteAgentEvent({eventName:x.modeSelector,remoteAgentId:"",eventData:{modeSelectorData:{action:e,mode:t,sourceControl:this.getSourceControlType()}}})}catch(s){console.error("Failed to report chat mode event:",s)}}setPinnedAgents(e){this._state.pinnedAgents={...e},this.notifySubscribers()}get hasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}}d(le,"key","remoteAgentsModel");function Oe(){const g=$("chatModel");return g||console.warn("ChatModel not found in context"),g}function Me(g){return G("chatModel",g),g}function ue(g){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},g[0]],n={};for(let a=0;a<s.length;a+=1)n=C(n,s[a]);return{c(){e=X("svg"),t=new Y(!0),this.h()},l(a){e=j(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=K(e);t=Z(r,!0),r.forEach(M),this.h()},h(){t.a=null,P(e,n)},m(a,r){Q(a,e,r),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M441 103c9.4 9.4 9.4 24.6 0 33.9L177 401c-9.4 9.4-24.6 9.4-33.9 0L7 265c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l119 119L407 103c9.4-9.4 24.6-9.4 33.9 0z"/>',e)},p(a,[r]){P(e,n=ee(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&r&&a[0]]))},i:k,o:k,d(a){a&&M(e)}}}function _e(g,e,t){return g.$$set=s=>{t(0,e=C(C({},e),T(s)))},[e=T(e)]}class Pe extends z{constructor(e){super(),J(this,e,_e,ue,V,{})}}export{Pe as C,le as R,f as S,v as T,Ie as a,Ee as b,xe as c,Oe as g,Me as s};
