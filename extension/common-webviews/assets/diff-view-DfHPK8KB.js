var ia=Object.defineProperty;var Xi=r=>{throw TypeError(r)};var ra=(r,t,e)=>t in r?ia(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var u=(r,t,e)=>ra(r,typeof t!="symbol"?t+"":t,e),On=(r,t,e)=>t.has(r)||Xi("Cannot "+e);var p=(r,t,e)=>(On(r,t,"read from private field"),e?e.call(r):t.get(r)),G=(r,t,e)=>t.has(r)?Xi("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),D=(r,t,e,s)=>(On(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),T=(r,t,e)=>(On(r,t,"access private method"),e);var Xs=(r,t,e,s)=>({set _(n){D(r,t,n,e)},get _(){return p(r,t,s)}});import{O as Vt,P as di,N as ut,al as ht,aD as Ji,am as Gn,S as Ve,i as Be,s as Ze,ad as me,W as et,J as K,E as I,c as Y,Y as Wt,ac as rs,e as O,f as st,F as E,a9 as Yr,a8 as Ee,u as $,q as $t,t as v,r as xt,h as R,I as A,aa as fi,ah as We,K as gt,C as os,D as oa,G as aa,A as ce,n as tt,B as ge,aj as Kr,M as ne,ak as Rn,aA as fs,T as ln,ag as la,a5 as pi,L as Xr,an as ca,a6 as Jr,ai as ha,U as ua}from"./SpinnerAugment-CQKp6jSN.js";import"./design-system-init-D-hN7xfd.js";import{g as da}from"./globals-D0QH3NT1.js";import{S as fa,W as ct,h as Ms,D as Ln,e as cn}from"./BaseButton-ESlFPUk1.js";import{A as pa,I as ga}from"./IconButtonAugment-D-fvrWAT.js";import{i as Gt,a as as,b as Ft,c as Is,d as vs,S as ss,e as ns,f as xn,C as ma,E as _a,D as ya,g as ba,h as $a,s as Nn,j as gi,k as hn,l as un,m as mi,n as dn,o as _i,p as fn,A as xa,q as pn,r as Dn,t as va,u as wa,v as Ts,w as to,U as eo,x as so,y as no,z as ka}from"./chat-flags-model-BhsWla-l.js";import{F as io,s as Ca,C as Sa,a as Ma,b as ro,P as Ia}from"./folder-opened-Bb7oiaOR.js";import{m as tr,C as Vn,n as Ea,o as er,b as sr,d as nr}from"./types-DwxhLPcD.js";import{P as Ct,C as Mt,a as se,I as ws,E as Aa}from"./chat-types-DOHETl9Q.js";import{C as Ta}from"./types-CGlLNakm.js";import{M as oo,T as Fa}from"./TextTooltipAugment--NM_J2iY.js";import{K as Qe,A as ir,R as Oa,B as Ra,P as La,T as Na,a as ao,b as Da,C as za,c as qa,G as Pa,d as Ua,M as Bn,e as lo,f as ja,g as Ha}from"./Keybindings-CmEJIsef.js";import{B as Ae}from"./ButtonAugment-CAn8LxGl.js";import{T as Wa}from"./Content-D7Q35t53.js";import{F as Ot}from"./Filespan-BI7GEaM4.js";import{D as zn}from"./index-DvKVcjj3.js";import{M as ps}from"./MaterialIcon-CprIOK2c.js";import{a as Ga,M as Va,b as Ba,C as Za}from"./index-csTQmXPq.js";import"./index-C4SxYn1J.js";import"./file-paths-BcSg4gks.js";import"./CalloutAugment-ZPisEIAt.js";import"./exclamation-triangle-CEPjk4z2.js";import"./CardAugment-KjDsYzQv.js";import"./pen-to-square-CIi2Dx1U.js";import"./augment-logo-DhUYorpN.js";var rr=NaN,Qa="[object Symbol]",Ya=/^\s+|\s+$/g,Ka=/^[-+]0x[0-9a-f]+$/i,Xa=/^0b[01]+$/i,Ja=/^0o[0-7]+$/i,tl=parseInt,el=typeof Vt=="object"&&Vt&&Vt.Object===Object&&Vt,sl=typeof self=="object"&&self&&self.Object===Object&&self,nl=el||sl||Function("return this")(),il=Object.prototype.toString,rl=Math.max,ol=Math.min,qn=function(){return nl.Date.now()};function Zn(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function or(r){if(typeof r=="number")return r;if(function(s){return typeof s=="symbol"||function(n){return!!n&&typeof n=="object"}(s)&&il.call(s)==Qa}(r))return rr;if(Zn(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=Zn(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=r.replace(Ya,"");var e=Xa.test(r);return e||Ja.test(r)?tl(r.slice(2),e?2:8):Ka.test(r)?rr:+r}const Qn=di(function(r,t,e){var s,n,i,o,a,l,h=0,c=!1,d=!1,f=!0;if(typeof r!="function")throw new TypeError("Expected a function");function g(y){var C=s,F=n;return s=n=void 0,h=y,o=r.apply(F,C)}function x(y){var C=y-l;return l===void 0||C>=t||C<0||d&&y-h>=i}function w(){var y=qn();if(x(y))return k(y);a=setTimeout(w,function(C){var F=t-(C-l);return d?ol(F,i-(C-h)):F}(y))}function k(y){return a=void 0,f&&s?g(y):(s=n=void 0,o)}function _(){var y=qn(),C=x(y);if(s=arguments,n=this,l=y,C){if(a===void 0)return function(F){return h=F,a=setTimeout(w,t),c?g(F):o}(l);if(d)return a=setTimeout(w,t),g(l)}return a===void 0&&(a=setTimeout(w,t)),o}return t=or(t)||0,Zn(e)&&(c=!!e.leading,i=(d="maxWait"in e)?rl(or(e.maxWait)||0,t):i,f="trailing"in e?!!e.trailing:f),_.cancel=function(){a!==void 0&&clearTimeout(a),h=0,s=l=n=a=void 0},_.flush=function(){return a===void 0?o:k(qn())},_});var Yn={exports:{}};(function(r,t){var e="__lodash_hash_undefined__",s=1,n=2,i=9007199254740991,o="[object Arguments]",a="[object Array]",l="[object AsyncFunction]",h="[object Boolean]",c="[object Date]",d="[object Error]",f="[object Function]",g="[object GeneratorFunction]",x="[object Map]",w="[object Number]",k="[object Null]",_="[object Object]",y="[object Promise]",C="[object Proxy]",F="[object RegExp]",N="[object Set]",ot="[object String]",j="[object Symbol]",V="[object Undefined]",U="[object WeakMap]",vt="[object ArrayBuffer]",dt="[object DataView]",It=/^\[object .+?Constructor\]$/,B=/^(?:0|[1-9]\d*)$/,H={};H["[object Float32Array]"]=H["[object Float64Array]"]=H["[object Int8Array]"]=H["[object Int16Array]"]=H["[object Int32Array]"]=H["[object Uint8Array]"]=H["[object Uint8ClampedArray]"]=H["[object Uint16Array]"]=H["[object Uint32Array]"]=!0,H[o]=H[a]=H[vt]=H[h]=H[dt]=H[c]=H[d]=H[f]=H[x]=H[w]=H[_]=H[F]=H[N]=H[ot]=H[U]=!1;var jt=typeof Vt=="object"&&Vt&&Vt.Object===Object&&Vt,Fe=typeof self=="object"&&self&&self.Object===Object&&self,Rt=jt||Fe||Function("return this")(),q=t&&!t.nodeType&&t,Oe=q&&r&&!r.nodeType&&r,Ke=Oe&&Oe.exports===q,gs=Ke&&jt.process,Ns=function(){try{return gs&&gs.binding&&gs.binding("util")}catch{}}(),Ii=Ns&&Ns.isTypedArray;function Lo(m,b){for(var S=-1,L=m==null?0:m.length;++S<L;)if(b(m[S],S,m))return!0;return!1}function No(m){var b=-1,S=Array(m.size);return m.forEach(function(L,nt){S[++b]=[nt,L]}),S}function Do(m){var b=-1,S=Array(m.size);return m.forEach(function(L){S[++b]=L}),S}var Ei,Ai,Ti,zo=Array.prototype,qo=Function.prototype,Ds=Object.prototype,wn=Rt["__core-js_shared__"],Fi=qo.toString,ie=Ds.hasOwnProperty,Oi=(Ei=/[^.]+$/.exec(wn&&wn.keys&&wn.keys.IE_PROTO||""))?"Symbol(src)_1."+Ei:"",Ri=Ds.toString,Po=RegExp("^"+Fi.call(ie).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Li=Ke?Rt.Buffer:void 0,zs=Rt.Symbol,Ni=Rt.Uint8Array,Di=Ds.propertyIsEnumerable,Uo=zo.splice,Re=zs?zs.toStringTag:void 0,zi=Object.getOwnPropertySymbols,jo=Li?Li.isBuffer:void 0,Ho=(Ai=Object.keys,Ti=Object,function(m){return Ai(Ti(m))}),kn=Xe(Rt,"DataView"),ms=Xe(Rt,"Map"),Cn=Xe(Rt,"Promise"),Sn=Xe(Rt,"Set"),Mn=Xe(Rt,"WeakMap"),_s=Xe(Object,"create"),Wo=De(kn),Go=De(ms),Vo=De(Cn),Bo=De(Sn),Zo=De(Mn),qi=zs?zs.prototype:void 0,In=qi?qi.valueOf:void 0;function Le(m){var b=-1,S=m==null?0:m.length;for(this.clear();++b<S;){var L=m[b];this.set(L[0],L[1])}}function he(m){var b=-1,S=m==null?0:m.length;for(this.clear();++b<S;){var L=m[b];this.set(L[0],L[1])}}function Ne(m){var b=-1,S=m==null?0:m.length;for(this.clear();++b<S;){var L=m[b];this.set(L[0],L[1])}}function qs(m){var b=-1,S=m==null?0:m.length;for(this.__data__=new Ne;++b<S;)this.add(m[b])}function ye(m){var b=this.__data__=new he(m);this.size=b.size}function Qo(m,b){var S=js(m),L=!S&&ta(m),nt=!S&&!L&&En(m),W=!S&&!L&&!nt&&Zi(m),lt=S||L||nt||W,ft=lt?function(_t,re){for(var ue=-1,Et=Array(_t);++ue<_t;)Et[ue]=re(ue);return Et}(m.length,String):[],Bt=ft.length;for(var mt in m)!ie.call(m,mt)||lt&&(mt=="length"||nt&&(mt=="offset"||mt=="parent")||W&&(mt=="buffer"||mt=="byteLength"||mt=="byteOffset")||Jo(mt,Bt))||ft.push(mt);return ft}function Ps(m,b){for(var S=m.length;S--;)if(Wi(m[S][0],b))return S;return-1}function ys(m){return m==null?m===void 0?V:k:Re&&Re in Object(m)?function(b){var S=ie.call(b,Re),L=b[Re];try{b[Re]=void 0;var nt=!0}catch{}var W=Ri.call(b);return nt&&(S?b[Re]=L:delete b[Re]),W}(m):function(b){return Ri.call(b)}(m)}function Pi(m){return bs(m)&&ys(m)==o}function Ui(m,b,S,L,nt){return m===b||(m==null||b==null||!bs(m)&&!bs(b)?m!=m&&b!=b:function(W,lt,ft,Bt,mt,_t){var re=js(W),ue=js(lt),Et=re?a:be(W),de=ue?a:be(lt),Je=(Et=Et==o?_:Et)==_,Hs=(de=de==o?_:de)==_,ts=Et==de;if(ts&&En(W)){if(!En(lt))return!1;re=!0,Je=!1}if(ts&&!Je)return _t||(_t=new ye),re||Zi(W)?ji(W,lt,ft,Bt,mt,_t):function(J,Z,Ws,$e,An,Ht,fe){switch(Ws){case dt:if(J.byteLength!=Z.byteLength||J.byteOffset!=Z.byteOffset)return!1;J=J.buffer,Z=Z.buffer;case vt:return!(J.byteLength!=Z.byteLength||!Ht(new Ni(J),new Ni(Z)));case h:case c:case w:return Wi(+J,+Z);case d:return J.name==Z.name&&J.message==Z.message;case F:case ot:return J==Z+"";case x:var xe=No;case N:var xs=$e&s;if(xe||(xe=Do),J.size!=Z.size&&!xs)return!1;var Gs=fe.get(J);if(Gs)return Gs==Z;$e|=n,fe.set(J,Z);var Tn=ji(xe(J),xe(Z),$e,An,Ht,fe);return fe.delete(J),Tn;case j:if(In)return In.call(J)==In.call(Z)}return!1}(W,lt,Et,ft,Bt,mt,_t);if(!(ft&s)){var $s=Je&&ie.call(W,"__wrapped__"),Qi=Hs&&ie.call(lt,"__wrapped__");if($s||Qi){var sa=$s?W.value():W,na=Qi?lt.value():lt;return _t||(_t=new ye),mt(sa,na,ft,Bt,_t)}}return ts?(_t||(_t=new ye),function(J,Z,Ws,$e,An,Ht){var fe=Ws&s,xe=Hi(J),xs=xe.length,Gs=Hi(Z),Tn=Gs.length;if(xs!=Tn&&!fe)return!1;for(var Vs=xs;Vs--;){var ze=xe[Vs];if(!(fe?ze in Z:ie.call(Z,ze)))return!1}var Yi=Ht.get(J);if(Yi&&Ht.get(Z))return Yi==Z;var Bs=!0;Ht.set(J,Z),Ht.set(Z,J);for(var Fn=fe;++Vs<xs;){var Zs=J[ze=xe[Vs]],Qs=Z[ze];if($e)var Ki=fe?$e(Qs,Zs,ze,Z,J,Ht):$e(Zs,Qs,ze,J,Z,Ht);if(!(Ki===void 0?Zs===Qs||An(Zs,Qs,Ws,$e,Ht):Ki)){Bs=!1;break}Fn||(Fn=ze=="constructor")}if(Bs&&!Fn){var Ys=J.constructor,Ks=Z.constructor;Ys==Ks||!("constructor"in J)||!("constructor"in Z)||typeof Ys=="function"&&Ys instanceof Ys&&typeof Ks=="function"&&Ks instanceof Ks||(Bs=!1)}return Ht.delete(J),Ht.delete(Z),Bs}(W,lt,ft,Bt,mt,_t)):!1}(m,b,S,L,Ui,nt))}function Yo(m){return!(!Bi(m)||function(b){return!!Oi&&Oi in b}(m))&&(Gi(m)?Po:It).test(De(m))}function Ko(m){if(S=(b=m)&&b.constructor,L=typeof S=="function"&&S.prototype||Ds,b!==L)return Ho(m);var b,S,L,nt=[];for(var W in Object(m))ie.call(m,W)&&W!="constructor"&&nt.push(W);return nt}function ji(m,b,S,L,nt,W){var lt=S&s,ft=m.length,Bt=b.length;if(ft!=Bt&&!(lt&&Bt>ft))return!1;var mt=W.get(m);if(mt&&W.get(b))return mt==b;var _t=-1,re=!0,ue=S&n?new qs:void 0;for(W.set(m,b),W.set(b,m);++_t<ft;){var Et=m[_t],de=b[_t];if(L)var Je=lt?L(de,Et,_t,b,m,W):L(Et,de,_t,m,b,W);if(Je!==void 0){if(Je)continue;re=!1;break}if(ue){if(!Lo(b,function(Hs,ts){if($s=ts,!ue.has($s)&&(Et===Hs||nt(Et,Hs,S,L,W)))return ue.push(ts);var $s})){re=!1;break}}else if(Et!==de&&!nt(Et,de,S,L,W)){re=!1;break}}return W.delete(m),W.delete(b),re}function Hi(m){return function(b,S,L){var nt=S(b);return js(b)?nt:function(W,lt){for(var ft=-1,Bt=lt.length,mt=W.length;++ft<Bt;)W[mt+ft]=lt[ft];return W}(nt,L(b))}(m,ea,Xo)}function Us(m,b){var S,L,nt=m.__data__;return((L=typeof(S=b))=="string"||L=="number"||L=="symbol"||L=="boolean"?S!=="__proto__":S===null)?nt[typeof b=="string"?"string":"hash"]:nt.map}function Xe(m,b){var S=function(L,nt){return L==null?void 0:L[nt]}(m,b);return Yo(S)?S:void 0}Le.prototype.clear=function(){this.__data__=_s?_s(null):{},this.size=0},Le.prototype.delete=function(m){var b=this.has(m)&&delete this.__data__[m];return this.size-=b?1:0,b},Le.prototype.get=function(m){var b=this.__data__;if(_s){var S=b[m];return S===e?void 0:S}return ie.call(b,m)?b[m]:void 0},Le.prototype.has=function(m){var b=this.__data__;return _s?b[m]!==void 0:ie.call(b,m)},Le.prototype.set=function(m,b){var S=this.__data__;return this.size+=this.has(m)?0:1,S[m]=_s&&b===void 0?e:b,this},he.prototype.clear=function(){this.__data__=[],this.size=0},he.prototype.delete=function(m){var b=this.__data__,S=Ps(b,m);return!(S<0)&&(S==b.length-1?b.pop():Uo.call(b,S,1),--this.size,!0)},he.prototype.get=function(m){var b=this.__data__,S=Ps(b,m);return S<0?void 0:b[S][1]},he.prototype.has=function(m){return Ps(this.__data__,m)>-1},he.prototype.set=function(m,b){var S=this.__data__,L=Ps(S,m);return L<0?(++this.size,S.push([m,b])):S[L][1]=b,this},Ne.prototype.clear=function(){this.size=0,this.__data__={hash:new Le,map:new(ms||he),string:new Le}},Ne.prototype.delete=function(m){var b=Us(this,m).delete(m);return this.size-=b?1:0,b},Ne.prototype.get=function(m){return Us(this,m).get(m)},Ne.prototype.has=function(m){return Us(this,m).has(m)},Ne.prototype.set=function(m,b){var S=Us(this,m),L=S.size;return S.set(m,b),this.size+=S.size==L?0:1,this},qs.prototype.add=qs.prototype.push=function(m){return this.__data__.set(m,e),this},qs.prototype.has=function(m){return this.__data__.has(m)},ye.prototype.clear=function(){this.__data__=new he,this.size=0},ye.prototype.delete=function(m){var b=this.__data__,S=b.delete(m);return this.size=b.size,S},ye.prototype.get=function(m){return this.__data__.get(m)},ye.prototype.has=function(m){return this.__data__.has(m)},ye.prototype.set=function(m,b){var S=this.__data__;if(S instanceof he){var L=S.__data__;if(!ms||L.length<199)return L.push([m,b]),this.size=++S.size,this;S=this.__data__=new Ne(L)}return S.set(m,b),this.size=S.size,this};var Xo=zi?function(m){return m==null?[]:(m=Object(m),function(b,S){for(var L=-1,nt=b==null?0:b.length,W=0,lt=[];++L<nt;){var ft=b[L];S(ft,L,b)&&(lt[W++]=ft)}return lt}(zi(m),function(b){return Di.call(m,b)}))}:function(){return[]},be=ys;function Jo(m,b){return!!(b=b??i)&&(typeof m=="number"||B.test(m))&&m>-1&&m%1==0&&m<b}function De(m){if(m!=null){try{return Fi.call(m)}catch{}try{return m+""}catch{}}return""}function Wi(m,b){return m===b||m!=m&&b!=b}(kn&&be(new kn(new ArrayBuffer(1)))!=dt||ms&&be(new ms)!=x||Cn&&be(Cn.resolve())!=y||Sn&&be(new Sn)!=N||Mn&&be(new Mn)!=U)&&(be=function(m){var b=ys(m),S=b==_?m.constructor:void 0,L=S?De(S):"";if(L)switch(L){case Wo:return dt;case Go:return x;case Vo:return y;case Bo:return N;case Zo:return U}return b});var ta=Pi(function(){return arguments}())?Pi:function(m){return bs(m)&&ie.call(m,"callee")&&!Di.call(m,"callee")},js=Array.isArray,En=jo||function(){return!1};function Gi(m){if(!Bi(m))return!1;var b=ys(m);return b==f||b==g||b==l||b==C}function Vi(m){return typeof m=="number"&&m>-1&&m%1==0&&m<=i}function Bi(m){var b=typeof m;return m!=null&&(b=="object"||b=="function")}function bs(m){return m!=null&&typeof m=="object"}var Zi=Ii?function(m){return function(b){return m(b)}}(Ii):function(m){return bs(m)&&Vi(m.length)&&!!H[ys(m)]};function ea(m){return(b=m)!=null&&Vi(b.length)&&!Gi(b)?Qo(m):Ko(m);var b}r.exports=function(m,b){return Ui(m,b)}})(Yn,Yn.exports);const al=di(Yn.exports);function co(r){return function(t){return"unitOfCodeWork"in t&&!function(e){return e.children.length>0&&"childIds"in e}(t)}(r)?[r]:r.children.flatMap(co)}function ls(r){var t;return((t=r.extraData)==null?void 0:t.isAgentConversation)===!0}var bt=(r=>(r[r.active=0]="active",r[r.inactive=1]="inactive",r))(bt||{});function ll(r,t,e=1e3){let s=null,n=0;const i=ut(t),o=()=>{const a=(()=>{const l=Date.now();if(s!==null&&l-n<e)return s;const h=r();return s=h,n=l,h})();i.set(a)};return{subscribe:i.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var ho=(r=>(r[r.unset=0]="unset",r[r.positive=1]="positive",r[r.negative=2]="negative",r))(ho||{}),Cs=(r=>(r[r.unknown=0]="unknown",r[r.new=1]="new",r[r.checkingSafety=2]="checkingSafety",r[r.runnable=3]="runnable",r[r.running=4]="running",r[r.completed=5]="completed",r[r.error=6]="error",r[r.cancelling=7]="cancelling",r[r.cancelled=8]="cancelled",r))(Cs||{});function Pn(r){return r.requestId+";"+r.toolUseId}function ar(r){const[t,e]=r.split(";");return{requestId:t,toolUseId:e}}const Lt="__NEW_AGENT__";function cl(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Ye={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function lr(r){Ye=r}const uo=/[&<>"']/,hl=new RegExp(uo.source,"g"),fo=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ul=new RegExp(fo.source,"g"),dl={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},cr=r=>dl[r];function Pt(r,t){if(t){if(uo.test(r))return r.replace(hl,cr)}else if(fo.test(r))return r.replace(ul,cr);return r}const fl=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function pl(r){return r.replace(fl,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const gl=/(^|[^\[])\^/g;function X(r,t){let e=typeof r=="string"?r:r.source;t=t||"";const s={replace:(n,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(gl,"$1"),e=e.replace(n,o),s},getRegex:()=>new RegExp(e,t)};return s}function hr(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const Es={exec:()=>null};function ur(r,t){const e=r.replace(/\|/g,(n,i,o)=>{let a=!1,l=i;for(;--l>=0&&o[l]==="\\";)a=!a;return a?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function Js(r,t,e){const s=r.length;if(s===0)return"";let n=0;for(;n<s;){const i=r.charAt(s-n-1);if(i!==t||e){if(i===t||!e)break;n++}else n++}return r.slice(0,s-n)}function dr(r,t,e,s){const n=t.href,i=t.title?Pt(t.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){s.state.inLink=!0;const a={type:"link",raw:e,href:n,title:i,text:o,tokens:s.inlineTokens(o)};return s.state.inLink=!1,a}return{type:"image",raw:e,href:n,title:i,text:Pt(o)}}class gn{constructor(t){u(this,"options");u(this,"rules");u(this,"lexer");this.options=t||Ye}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:Js(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],n=function(i,o){const a=i.match(/^(\s+)(?:```)/);if(a===null)return o;const l=a[1];return o.split(`
`).map(h=>{const c=h.match(/^\s+/);if(c===null)return h;const[d]=c;return d.length>=l.length?h.slice(l.length):h}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const n=Js(s,"#");this.options.pedantic?s=n.trim():n&&!/ $/.test(n)||(s=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=Js(e[0].replace(/^ *>[ \t]?/gm,""),`
`),n=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(s);return this.lexer.state.top=n,{type:"blockquote",raw:e[0],tokens:i,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const n=s.length>1,i={type:"list",raw:"",ordered:n,start:n?+s.slice(0,-1):"",loose:!1,items:[]};s=n?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=n?s:"[*+-]");const o=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",h=!1;for(;t;){let c=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let d=e[2].split(`
`,1)[0].replace(/^\t+/,_=>" ".repeat(3*_.length)),f=t.split(`
`,1)[0],g=0;this.options.pedantic?(g=2,l=d.trimStart()):(g=e[2].search(/[^ ]/),g=g>4?1:g,l=d.slice(g),g+=e[1].length);let x=!1;if(!d&&/^ *$/.test(f)&&(a+=f+`
`,t=t.substring(f.length+1),c=!0),!c){const _=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),y=new RegExp(`^ {0,${Math.min(3,g-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),C=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:\`\`\`|~~~)`),F=new RegExp(`^ {0,${Math.min(3,g-1)}}#`);for(;t;){const N=t.split(`
`,1)[0];if(f=N,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),C.test(f)||F.test(f)||_.test(f)||y.test(t))break;if(f.search(/[^ ]/)>=g||!f.trim())l+=`
`+f.slice(g);else{if(x||d.search(/[^ ]/)>=4||C.test(d)||F.test(d)||y.test(d))break;l+=`
`+f}x||f.trim()||(x=!0),a+=N+`
`,t=t.substring(N.length+1),d=f.slice(g)}}i.loose||(h?i.loose=!0:/\n *\n *$/.test(a)&&(h=!0));let w,k=null;this.options.gfm&&(k=/^\[[ xX]\] /.exec(l),k&&(w=k[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:a,task:!!k,checked:w,loose:!1,text:l,tokens:[]}),i.raw+=a}i.items[i.items.length-1].raw=a.trimEnd(),i.items[i.items.length-1].text=l.trimEnd(),i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){const d=i.items[c].tokens.filter(g=>g.type==="space"),f=d.length>0&&d.some(g=>/\n.*\n/.test(g.raw));i.loose=f}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),n=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:n,title:i}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=ur(e[1]),n=e[2].replace(/^\||\| *$/g,"").split("|"),i=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===n.length){for(const a of n)/^ *-+: *$/.test(a)?o.align.push("right"):/^ *:-+: *$/.test(a)?o.align.push("center"):/^ *:-+ *$/.test(a)?o.align.push("left"):o.align.push(null);for(const a of s)o.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of i)o.rows.push(ur(a,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:Pt(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const o=Js(s.slice(0,-1),"\\");if((s.length-o.length)%2==0)return}else{const o=function(a,l){if(a.indexOf(l[1])===-1)return-1;let h=0;for(let c=0;c<a.length;c++)if(a[c]==="\\")c++;else if(a[c]===l[0])h++;else if(a[c]===l[1]&&(h--,h<0))return c;return-1}(e[2],"()");if(o>-1){const a=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let n=e[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);o&&(n=o[1],i=o[3])}else i=e[3]?e[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(s)?n.slice(1):n.slice(1,-1)),dr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const n=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!n){const i=s[0].charAt(0);return{type:"text",raw:i,text:i}}return dr(s,n,s[0],this.lexer)}}emStrong(t,e,s=""){let n=this.rules.inline.emStrongLDelim.exec(t);if(n&&!(n[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(n[1]||n[2])||!s||this.rules.inline.punctuation.exec(s))){const i=[...n[0]].length-1;let o,a,l=i,h=0;const c=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,e=e.slice(-1*t.length+i);(n=c.exec(e))!=null;){if(o=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!o)continue;if(a=[...o].length,n[3]||n[4]){l+=a;continue}if((n[5]||n[6])&&i%3&&!((i+a)%3)){h+=a;continue}if(l-=a,l>0)continue;a=Math.min(a,a+l+h);const d=[...n[0]][0].length,f=t.slice(0,i+n.index+d+a);if(Math.min(i,a)%2){const x=f.slice(1,-1);return{type:"em",raw:f,text:x,tokens:this.lexer.inlineTokens(x)}}const g=f.slice(2,-2);return{type:"strong",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const n=/[^ ]/.test(s),i=/^ /.test(s)&&/ $/.test(s);return n&&i&&(s=s.substring(1,s.length-1)),s=Pt(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,n;return e[2]==="@"?(s=Pt(e[1]),n="mailto:"+s):(s=Pt(e[1]),n=s),{type:"link",raw:e[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let n,i;if(e[2]==="@")n=Pt(e[0]),i="mailto:"+n;else{let o;do o=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(o!==e[0]);n=Pt(e[0]),i=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:Pt(e[0]),{type:"text",raw:e[0],text:s}}}}const Rs=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,po=/(?:[*+-]|\d{1,9}[.)])/,go=X(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,po).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),yi=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,bi=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ml=X(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",bi).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),_l=X(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,po).getRegex(),vn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",$i=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,yl=X("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",$i).replace("tag",vn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),fr=X(yi).replace("hr",Rs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vn).getRegex(),xi={blockquote:X(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",fr).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:ml,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:Rs,html:yl,lheading:go,list:_l,newline:/^(?: *(?:\n|$))+/,paragraph:fr,table:Es,text:/^[^\n]+/},pr=X("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Rs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vn).getRegex(),bl={...xi,table:pr,paragraph:X(yi).replace("hr",Rs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",pr).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vn).getRegex()},$l={...xi,html:X(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",$i).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Es,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:X(yi).replace("hr",Rs).replace("heading",` *#{1,6} *[^
]`).replace("lheading",go).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},mo=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,_o=/^( {2,}|\\)\n(?!\s*$)/,Ls="\\p{P}\\p{S}",xl=X(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Ls).getRegex(),vl=X(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Ls).getRegex(),wl=X("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Ls).getRegex(),kl=X("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Ls).getRegex(),Cl=X(/\\([punct])/,"gu").replace(/punct/g,Ls).getRegex(),Sl=X(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ml=X($i).replace("(?:-->|$)","-->").getRegex(),Il=X("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ml).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),mn=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,El=X(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",mn).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),gr=X(/^!?\[(label)\]\[(ref)\]/).replace("label",mn).replace("ref",bi).getRegex(),mr=X(/^!?\[(ref)\](?:\[\])?/).replace("ref",bi).getRegex(),vi={_backpedal:Es,anyPunctuation:Cl,autolink:Sl,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:_o,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Es,emStrongLDelim:vl,emStrongRDelimAst:wl,emStrongRDelimUnd:kl,escape:mo,link:El,nolink:mr,punctuation:xl,reflink:gr,reflinkSearch:X("reflink|nolink(?!\\()","g").replace("reflink",gr).replace("nolink",mr).getRegex(),tag:Il,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Es},Al={...vi,link:X(/^!?\[(label)\]\((.*?)\)/).replace("label",mn).getRegex(),reflink:X(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",mn).getRegex()},Kn={...vi,escape:X(mo).replace("])","~|])").getRegex(),url:X(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Tl={...Kn,br:X(_o).replace("{2,}","*").getRegex(),text:X(Kn.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},tn={normal:xi,gfm:bl,pedantic:$l},ks={normal:vi,gfm:Kn,breaks:Tl,pedantic:Al};class oe{constructor(t){u(this,"tokens");u(this,"options");u(this,"state");u(this,"tokenizer");u(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Ye,this.options.tokenizer=this.options.tokenizer||new gn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:tn.normal,inline:ks.normal};this.options.pedantic?(e.block=tn.pedantic,e.inline=ks.pedantic):this.options.gfm&&(e.block=tn.gfm,this.options.breaks?e.inline=ks.breaks:e.inline=ks.gfm),this.tokenizer.rules=e}static get rules(){return{block:tn,inline:ks}}static lex(t,e){return new oe(e).lex(t)}static lexInline(t,e){return new oe(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,n,i,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(a,l,h)=>l+"    ".repeat(h.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(s=a.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?e.push(s):(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(n.raw+=`
`+s.raw,n.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(i=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let h;this.options.extensions.startBlock.forEach(c=>{h=c.call({lexer:this},l),typeof h=="number"&&h>=0&&(a=Math.min(a,h))}),a<1/0&&a>=0&&(i=t.substring(0,a+1))}if(this.state.top&&(s=this.tokenizer.paragraph(i)))n=e[e.length-1],o&&n.type==="paragraph"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s),o=i.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&n.type==="text"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,n,i,o,a,l,h=t;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(h))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(h=h.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+h.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(h))!=null;)h=h.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+h.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(h))!=null;)h=h.slice(0,o.index)+"++"+h.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(a||(l=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(s=c.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,h,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(i=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=t.slice(1);let f;this.options.extensions.startInline.forEach(g=>{f=g.call({lexer:this},d),typeof f=="number"&&f>=0&&(c=Math.min(c,f))}),c<1/0&&c>=0&&(i=t.substring(0,c+1))}if(s=this.tokenizer.inlineText(i))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),a=!0,n=e[e.length-1],n&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else t=t.substring(s.raw.length),e.push(s);return e}}class _n{constructor(t){u(this,"options");this.options=t||Ye}code(t,e,s){var i;const n=(i=(e||"").match(/^\S*/))==null?void 0:i[0];return t=t.replace(/\n$/,"")+`
`,n?'<pre><code class="language-'+Pt(n)+'">'+(s?t:Pt(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:Pt(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const n=e?"ol":"ul";return"<"+n+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+n+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const n=hr(t);if(n===null)return s;let i='<a href="'+(t=n)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+s+"</a>",i}image(t,e,s){const n=hr(t);if(n===null)return s;let i=`<img src="${t=n}" alt="${s}"`;return e&&(i+=` title="${e}"`),i+=">",i}text(t){return t}}class wi{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class ae{constructor(t){u(this,"options");u(this,"renderer");u(this,"textRenderer");this.options=t||Ye,this.options.renderer=this.options.renderer||new _n,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new wi}static parse(t,e){return new ae(e).parse(t)}static parseInline(t,e){return new ae(e).parseInline(t)}parse(t,e=!0){let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){s+=a||"";continue}}switch(i.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const o=i;s+=this.renderer.heading(this.parseInline(o.tokens),o.depth,pl(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;s+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let a="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});a+=this.renderer.tablerow(l);let h="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];l="";for(let f=0;f<d.length;f++)l+=this.renderer.tablecell(this.parseInline(d[f].tokens),{header:!1,align:o.align[f]});h+=this.renderer.tablerow(l)}s+=this.renderer.table(a,h);continue}case"blockquote":{const o=i,a=this.parse(o.tokens);s+=this.renderer.blockquote(a);continue}case"list":{const o=i,a=o.ordered,l=o.start,h=o.loose;let c="";for(let d=0;d<o.items.length;d++){const f=o.items[d],g=f.checked,x=f.task;let w="";if(f.task){const k=this.renderer.checkbox(!!g);h?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=k+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=k+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:k+" "}):w+=k+" "}w+=this.parse(f.tokens,h),c+=this.renderer.listitem(w,x,!!g)}s+=this.renderer.list(c,a,l);continue}case"html":{const o=i;s+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;s+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,a=o.tokens?this.parseInline(o.tokens):o.text;for(;n+1<t.length&&t[n+1].type==="text";)o=t[++n],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);s+=e?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){s+=o||"";continue}}switch(i.type){case"escape":{const o=i;s+=e.text(o.text);break}case"html":{const o=i;s+=e.html(o.text);break}case"link":{const o=i;s+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=i;s+=e.image(o.href,o.title,o.text);break}case"strong":{const o=i;s+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=i;s+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=i;s+=e.codespan(o.text);break}case"br":s+=e.br();break;case"del":{const o=i;s+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=i;s+=e.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}}class As{constructor(t){u(this,"options");this.options=t||Ye}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}u(As,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Ge,Xn,yo,Vr;const qe=new(Vr=class{constructor(...r){G(this,Ge);u(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});u(this,"options",this.setOptions);u(this,"parse",T(this,Ge,Xn).call(this,oe.lex,ae.parse));u(this,"parseInline",T(this,Ge,Xn).call(this,oe.lexInline,ae.parseInline));u(this,"Parser",ae);u(this,"Renderer",_n);u(this,"TextRenderer",wi);u(this,"Lexer",oe);u(this,"Tokenizer",gn);u(this,"Hooks",As);this.use(...r)}walkTokens(r,t){var s,n;let e=[];for(const i of r)switch(e=e.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const a of o.header)e=e.concat(this.walkTokens(a.tokens,t));for(const a of o.rows)for(const l of a)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const o=i;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=i;(n=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&n[o.type]?this.defaults.extensions.childTokens[o.type].forEach(a=>{const l=o[a].flat(1/0);e=e.concat(this.walkTokens(l,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...r){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const i=t.renderers[n.name];t.renderers[n.name]=i?function(...o){let a=n.renderer.apply(this,o);return a===!1&&(a=i.apply(this,o)),a}:n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[n.level];i?i.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),s.extensions=t),e.renderer){const n=this.defaults.renderer||new _n(this.defaults);for(const i in e.renderer){if(!(i in n))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.renderer[o],l=n[o];n[o]=(...h)=>{let c=a.apply(n,h);return c===!1&&(c=l.apply(n,h)),c||""}}s.renderer=n}if(e.tokenizer){const n=this.defaults.tokenizer||new gn(this.defaults);for(const i in e.tokenizer){if(!(i in n))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,a=e.tokenizer[o],l=n[o];n[o]=(...h)=>{let c=a.apply(n,h);return c===!1&&(c=l.apply(n,h)),c}}s.tokenizer=n}if(e.hooks){const n=this.defaults.hooks||new As;for(const i in e.hooks){if(!(i in n))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.hooks[o],l=n[o];As.passThroughHooks.has(i)?n[o]=h=>{if(this.defaults.async)return Promise.resolve(a.call(n,h)).then(d=>l.call(n,d));const c=a.call(n,h);return l.call(n,c)}:n[o]=(...h)=>{let c=a.apply(n,h);return c===!1&&(c=l.apply(n,h)),c}}s.hooks=n}if(e.walkTokens){const n=this.defaults.walkTokens,i=e.walkTokens;s.walkTokens=function(o){let a=[];return a.push(i.call(this,o)),n&&(a=a.concat(n.call(this,o))),a}}this.defaults={...this.defaults,...s}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,t){return oe.lex(r,t??this.defaults)}parser(r,t){return ae.parse(r,t??this.defaults)}},Ge=new WeakSet,Xn=function(r,t){return(e,s)=>{const n={...s},i={...this.defaults,...n};this.defaults.async===!0&&n.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=T(this,Ge,yo).call(this,!!i.silent,!!i.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then(a=>r(a,i)).then(a=>i.hooks?i.hooks.processAllTokens(a):a).then(a=>i.walkTokens?Promise.all(this.walkTokens(a,i.walkTokens)).then(()=>a):a).then(a=>t(a,i)).then(a=>i.hooks?i.hooks.postprocess(a):a).catch(o);try{i.hooks&&(e=i.hooks.preprocess(e));let a=r(e,i);i.hooks&&(a=i.hooks.processAllTokens(a)),i.walkTokens&&this.walkTokens(a,i.walkTokens);let l=t(a,i);return i.hooks&&(l=i.hooks.postprocess(l)),l}catch(a){return o(a)}}},yo=function(r,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const s="<p>An error occurred:</p><pre>"+Pt(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},Vr);function Q(r,t){return qe.parse(r,t)}Q.options=Q.setOptions=function(r){return qe.setOptions(r),Q.defaults=qe.defaults,lr(Q.defaults),Q},Q.getDefaults=cl,Q.defaults=Ye,Q.use=function(...r){return qe.use(...r),Q.defaults=qe.defaults,lr(Q.defaults),Q},Q.walkTokens=function(r,t){return qe.walkTokens(r,t)},Q.parseInline=qe.parseInline,Q.Parser=ae,Q.parser=ae.parse,Q.Renderer=_n,Q.TextRenderer=wi,Q.Lexer=oe,Q.lexer=oe.lex,Q.Tokenizer=gn,Q.Hooks=As,Q.parse=Q,Q.options,Q.setOptions,Q.use,Q.walkTokens,Q.parseInline,ae.parse,oe.lex;const _r=r=>Gt(r)&&!!r.request_message;function Fl(r,t){const e=r.customPersonalityPrompts;if(e)switch(t){case Ct.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case Ct.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case Ct.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case Ct.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return Ol[t]}const Ol={[Ct.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Ct.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Ct.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Ct.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function Rl(r=[]){const t=function(e=[]){let s;for(const n of e){if(n.type===Mt.TOOL_USE)return n;n.type===Mt.TOOL_USE_START&&(s=n)}return s}(r);return t&&t.type===Mt.TOOL_USE?r.filter(e=>e.type!==Mt.TOOL_USE_START):r}class at{constructor(t,e,s,n){u(this,"_state");u(this,"_subscribers",new Set);u(this,"_focusModel",new io);u(this,"_onSendExchangeListeners",[]);u(this,"_onNewConversationListeners",[]);u(this,"_onHistoryDeleteListeners",[]);u(this,"_onBeforeChangeConversationListeners",[]);u(this,"_totalCharactersCacheThrottleMs",1e3);u(this,"_totalCharactersStore");u(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));u(this,"setConversation",(t,e=!0,s=!0)=>{const n=t.id!==this._state.id;n&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:l,toolUseId:h}=ar(o);return l===a.requestId&&h===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Pn(a)),[o,a]}return[o,{...a,...ar(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&n&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const i=at.isEmpty(t);if(n&&i){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(Gt)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),n&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});u(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});u(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});u(this,"setName",t=>{this.update({name:t})});u(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});u(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});u(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Pn(t)]:t}})});u(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:Cs.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Pn({requestId:t,toolUseId:e})]||{phase:Cs.new});u(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:Cs.unknown};const e=function(n=[]){let i;for(const o of n){if(o.type===Mt.TOOL_USE)return o;o.type===Mt.TOOL_USE_START&&(i=o)}return i}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:Cs.unknown}});u(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;Gt(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:ho.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});u(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});u(this,"updateExchangeById",(t,e,s=!1)=>{var a;const n=this.exchangeWithRequestId(e);if(n===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(n.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=Rl([...n.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==n.stop_reason&&n.stop_reason&&t.stop_reason===Ta.REASON_UNSPECIFIED&&(t.stop_reason=n.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...n.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const i=(a=(t.structured_output_nodes||[]).find(l=>l.type===Mt.MAIN_TEXT_FINISHED))==null?void 0:a.content;i&&i!==t.response_text&&(t.response_text=i);let o=this._state.isShareable||as({...n,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:o}),!0});u(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});u(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});u(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),n=s.map(i=>i.request_id).filter(i=>i!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:n}),s.forEach(i=>{this._onHistoryDeleteListeners.forEach(o=>o(i))})});u(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});u(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});u(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});u(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});u(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Ft.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));u(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});u(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);u(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});u(this,"historySummaryVersion",1);u(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:ss.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});u(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));u(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});u(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});u(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(n=>t.has(n.id)||n.recentFile||n.selection||n.sourceFolder),s=this._specialContextInputModel.recentItems.filter(n=>!(t.has(n.id)||n.recentFile||n.selection||n.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});u(this,"saveDraftExchange",(t,e)=>{var o,a,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),n=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!n)return;const i=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:i,status:Ft.draft}})});u(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});u(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var o,a;const s=!this.name&&this.chatHistory.length===1&&((o=this.firstExchange)==null?void 0:o.request_id)===this.chatHistory[0].request_id,n=ls(this)&&((a=this._state.extraData)==null?void 0:a.hasAgentOnboarded)&&(i=this.chatHistory,i.filter(l=>_r(l))).length===2;var i;this._chatFlagModel.summaryTitles&&(s||n)&&this.updateConversationTitle()}).finally(()=>{var s;ls(this)&&this._extensionClient.reportAgentRequestEvent({eventName:tr.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});u(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Ft.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});u(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const n={status:Ft.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:ss.unseen,timestamp:new Date().toISOString()};this.addExchange(n);for await(const i of this._extensionClient.sendInstructionMessage(n,e)){if(!this.updateExchangeById(i,s,!0))return;s=i.request_id||s}});u(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});u(this,"sendSummaryExchange",()=>{const t={status:Ft.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:ns.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});u(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:Ft.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:ss.unseen,chatItemType:ns.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});u(this,"sendExchange",async(t,e=!1)=>{var o;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,n=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&at.isNew(this._state)){const a=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,a)}catch(h){console.error("Failed to migrate conversation checkpoints:",h)}this._state={...this._state,id:a},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(a),this._subscribers.forEach(h=>h(this))}t=br(t);let i={status:Ft.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:n,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:ss.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(i),this._loadContextFromExchange(i),this._onSendExchangeListeners.forEach(a=>a(i)),this._chatFlagModel.useHistorySummary&&(this._clearStaleHistorySummaryNodes(),await this.maybeAddHistorySummaryNode()),i=await this._addIdeStateNode(i),this.updateExchangeById({structured_request_nodes:i.structured_request_nodes},s,!1);for await(const a of this.sendUserMessage(s,i,e)){if(((o=this.exchangeWithRequestId(s))==null?void 0:o.status)!==Ft.sent||!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});u(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Ft.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Vn.chatUseSuggestedQuestion)});u(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});u(this,"recoverExchange",async t=>{var n;if(!t.request_id||t.status!==Ft.sent)return;let e=t.request_id;const s=(n=t.structured_output_nodes)==null?void 0:n.filter(i=>i.type===Mt.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const i of this.getChatStream(t)){if(!this.updateExchangeById(i,e,!0))return;e=i.request_id||e}});u(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Gt(e)&&this._loadContextFromExchange(e)})});u(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});u(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Gt(e)&&this._unloadContextFromExchange(e)})});u(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});u(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});u(this,"_jsonToStructuredRequest",t=>{const e=[],s=i=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===se.TEXT){const l=((a=o.text_node)==null?void 0:a.content)??"",h={...o,text_node:{content:l+i}};e[e.length-1]=h}else e.push({id:e.length,type:se.TEXT,text_node:{content:i}})},n=i=>{var o,a,l,h;if(i.type==="doc"||i.type==="paragraph")for(const c of i.content??[])n(c);else if(i.type==="hardBreak")s(`
`);else if(i.type==="text")s(i.text??"");else if(i.type==="image"){if(typeof((o=i.attrs)==null?void 0:o.src)!="string")return void console.error("Image source is not a string: ",(a=i.attrs)==null?void 0:a.src);if(i.attrs.isLoading)return;const c=(l=i.attrs)==null?void 0:l.title,d=this._fileNameToImageFormat(c);e.push({id:e.length,type:se.IMAGE_ID,image_id_node:{image_id:i.attrs.src,format:d}})}else if(i.type==="mention"){const c=(h=i.attrs)==null?void 0:h.data;c&&xn(c)?e.push({id:e.length,type:se.TEXT,text_node:{content:Fl(this._chatFlagModel,c.personality.type)}}):s(`@\`${(c==null?void 0:c.name)??(c==null?void 0:c.id)}\``)}};return n(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=n,this._state={...at.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return ll(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,n)=>s+n,0))||0)<=4?Ct.PROTOTYPER:Ct.DEFAULT}catch(e){return console.error("Error determining persona type:",e),Ct.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Ct.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var n;if(t.name)return t.name;const e=t.chatHistory.find(Gt);return e&&e.request_message?at.toSentenceCase(e.request_message):(s=t,((n=s.extraData)==null?void 0:n.isAutofix)===!0?"Autofix Chat":ls(t)?"New Agent":"New Chat");var s}static isNew(t){return t.id===Lt}static isEmpty(t){var e;return!(t.chatHistory.some(Gt)||(e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?at.lastMessageTimestamp(t):e==="lastInteractedAt"?at.lastInteractedAt(t):at.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(Gt))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!at.isEmpty(t)||at.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const n of this._onBeforeChangeConversationListeners){const i=n(t,s);i!==void 0&&(s=i)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return at.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Ct.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return at.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return at.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=n=>Array.isArray(n)?n.some(e):!!n&&(n.type==="image"||!(!n.content||!Array.isArray(n.content))&&n.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(Gt)??null}get lastExchange(){return this.chatHistory.findLast(Gt)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>Gt(t)&&t.status===Ft.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>as(t)||Is(t)||vs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=(t=t.filter(n=>!vs(n)||n.summaryVersion===this.historySummaryVersion)).findLastIndex(n=>vs(n));this._chatFlagModel.useHistorySummary&&e>0&&(console.info("Using history summary node found at index %d",e),t=t.slice(e));const s=[];for(const n of t)if(as(n))s.push(yr(n));else if(Is(n)&&n.fromTimestamp!==void 0&&n.toTimestamp!==void 0){if(n.revertTarget){const i=Ll(n,1),o={request_message:"",response_text:"",request_id:n.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};s.push(o)}}else this._chatFlagModel.useHistorySummary&&vs(n)&&s.push(yr(n));return s}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Ft.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,n="";const i=await this._addIdeStateNode(br({...t,request_id:e,status:Ft.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,i,!0))o.response_text&&(n+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:n,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var c;const n=this._specialContextInputModel.chatActiveContext;let i;if(e.chatHistory!==void 0)i=e.chatHistory;else{let d=this.successfulMessages;if(e.chatItemType===ns.summaryTitle){const f=d.findIndex(g=>g.chatItemType!==ns.agentOnboarding&&_r(g));f!==-1&&(d=d.slice(f))}i=this._convertHistoryToExchanges(d)}let o=this.personaType;if(e.structured_request_nodes){const d=e.structured_request_nodes.find(f=>f.type===se.CHANGE_PERSONALITY);d&&d.change_personality_node&&(o=d.change_personality_node.personality_type)}const a={text:e.request_message,chatHistory:i,silent:s,modelId:e.model_id,context:n,userSpecifiedFiles:n.userSpecifiedFiles,externalSourceIds:(c=n.externalSources)==null?void 0:c.map(d=>d.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:o,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,a,{flags:this._chatFlagModel}),h=this._extensionClient.startChatStreamWithRetry(t,a,{flags:this._chatFlagModel});for await(const d of h){let f=d;for(const g of l)f=g.handleChunk(f)??f;yield f}for(const d of l)yield*d.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return ws.JPEG;case"png":return ws.PNG;case"gif":return ws.GIF;case"webp":return ws.WEBP;default:return ws.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(n=>n.type!==se.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(n){console.error("Failed to add IDE state to exchange:",n)}return e?(s=[...s,{id:bo(s)+1,type:se.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}async maybeAddHistorySummaryNode(){var y,C,F;const t=this._chatFlagModel.historySummaryPrompt;if(!t||t.trim()==="")return!1;const e=this._convertHistoryToExchanges(this.chatHistory),[s,n]=Ca(e,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(s.length===0)return!1;const i=JSON.stringify(e).length,o=JSON.stringify(s).length,a=JSON.stringify(n).length,l={totalHistoryCharCount:i,totalHistoryExchangeCount:e.length,headCharCount:o,headExchangeCount:s.length,headLastRequestId:((y=s.at(-1))==null?void 0:y.request_id)??"",tailCharCount:a,tailExchangeCount:n.length,tailLastRequestId:((C=n.at(-1))==null?void 0:C.request_id)??"",summaryCharCount:0,summarizationDurationMs:0};let h=((F=s.at(-1))==null?void 0:F.response_nodes)??[],c=h.filter(N=>N.type===Mt.TOOL_USE);c.length>0&&(s.at(-1).response_nodes=h.filter(N=>N.type!==Mt.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",s.length);const d=Date.now(),{responseText:f,requestId:g}=await this.sendSilentExchange({request_message:t,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:s}),x=Date.now();l.summaryCharCount=f.length,l.summarizationDurationMs=x-d,this._extensionClient.reportAgentRequestEvent({eventName:tr.chatHistorySummarization,conversationId:this.id,requestId:g??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length,eventData:{chatHistorySummarizationData:l}});const w={chatItemType:ns.historySummary,summaryVersion:this.historySummaryVersion,request_id:g,request_message:t,response_text:f,structured_output_nodes:[{id:c.map(N=>N.id).reduce((N,ot)=>Math.max(N,ot),-1)+1,type:Mt.RAW_RESPONSE,content:f},...c],status:Ft.success,seen_state:ss.seen,timestamp:new Date().toISOString()},k=this.chatHistory.findIndex(N=>N.request_id===s.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",k);const _=[...this._state.chatHistory];return _.splice(k,0,w),this.update({chatHistory:_}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(t=>!vs(t)||t.summaryVersion===this.historySummaryVersion)})}}function Ll(r,t){const e=(Is(r),r.fromTimestamp),s=(Is(r),r.toTimestamp),n=Is(r)&&r.revertTarget!==void 0;return{id:t,type:se.CHECKPOINT_REF,checkpoint_ref_node:{request_id:r.request_id||"",from_timestamp:e,to_timestamp:s,source:n?Aa.CHECKPOINT_REVERT:void 0}}}function yr(r){const t=(r.structured_output_nodes??[]).filter(e=>e.type===Mt.RAW_RESPONSE||e.type===Mt.TOOL_USE||e.type===Mt.TOOL_USE_START).map(e=>e.type===Mt.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:Mt.TOOL_USE}:e);return{request_message:r.request_message,response_text:r.response_text??"",request_id:r.request_id||"",request_nodes:r.structured_request_nodes??[],response_nodes:t}}function bo(r){return r.length>0?Math.max(...r.map(t=>t.id)):0}function br(r){var t;if(r.request_message.length>0&&!((t=r.structured_request_nodes)!=null&&t.some(e=>e.type===se.TEXT))){let e=r.structured_request_nodes??[];return e=[...e,{id:bo(e)+1,type:se.TEXT,text_node:{content:r.request_message}}],{...r,structured_request_nodes:e}}return r}class Nl{constructor(t=!0,e=setTimeout){u(this,"_notify",new Set);u(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});u(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});u(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});u(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,n){if(!t&&!n)return()=>{};const i={timeout:t,notify:e,once:s,date:n};return this._notify.add(i),this._schedule(i),()=>{this._clearTimeout(i),this._notify.delete(i)}}}class Dl{constructor(t=0,e=0,s=new Nl,n=ut("busy"),i=ut(!1)){u(this,"unsubNotify");u(this,"unsubMessage");u(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});u(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=n,this.focusAfterIdle=i,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var nn=(r=>(r.send="send",r.addTask="addTask",r))(nn||{});class zl{constructor(){u(this,"_mode",ut(nn.send));u(this,"_currentMode",nn.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(nn).includes(t)&&this._mode.set(t)}}const en=ut("idle");class ki{constructor(t,e,s,n={}){u(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});u(this,"extensionClient");u(this,"_chatFlagsModel");u(this,"_currConversationModel");u(this,"_chatModeModel");u(this,"_sendModeModel");u(this,"_currentChatMode");u(this,"_flagsLoaded",ut(!1));u(this,"subscribers",new Set);u(this,"idleMessageModel",new Dl);u(this,"isPanelCollapsed");u(this,"agentExecutionMode");u(this,"sortConversationsBy");u(this,"displayedAnnouncements");u(this,"onLoaded",async()=>{var s,n;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&(t.enableBackgroundAgents||t.enableNewThreadsList);this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??ya,bigSyncThreshold:t.bigSyncThreshold??ba,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??$a,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??fa.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:(t.enableBackgroundAgents??!1)||(t.enableNewThreadsList??!1),enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryMaxChars:t.historySummaryMaxChars??0,historySummaryLowerChars:t.historySummaryLowerChars??0,historySummaryPrompt:t.historySummaryPrompt??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._currentChatMode=t.currentChatMode,e&&this.onDoUseNewDraftFunctionalityChanged(),this._flagsLoaded.set(!0),(n=(s=this.options).onLoaded)==null||n.call(s),this.notifySubscribers()});u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Nn&&this.currentConversationId!==Nn||(delete this._state.conversations[Nn],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===Lt||at.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});u(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const n=s.chatHistory.some(i=>as(i));t[e]={...s,isShareable:n}}this._state.conversations=t});u(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[n,i]of Object.entries(e))i.isPinned&&s.add(n);this.setState(this._state),this.notifySubscribers()});u(this,"saveImmediate",()=>{this._host.setState(this._state)});u(this,"setState",Qn(t=>{this._host.setState({...t,isPanelCollapsed:ht(this.isPanelCollapsed),agentExecutionMode:ht(this.agentExecutionMode),sortConversationsBy:ht(this.sortConversationsBy),displayedAnnouncements:ht(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});u(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));u(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[Lt];if(this.currentConversationId&&this.currentConversationId!==Lt&&this._state.conversations[this.currentConversationId]&&at.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:Lt};this._state.conversations[Lt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=Lt,this._currConversationModel.setConversation(e)}});u(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:ns.educateFeatures,request_id:crypto.randomUUID(),seen_state:ss.seen})});u(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});u(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let n;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=Lt),n=this._state.conversations[t]??at.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===Lt&&(n.id=Lt),s!=null&&s.newTaskUuid&&(n.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(ls(this._currConversationModel)?"agent":"chat"),n=at.create({personaType:await this._currConversationModel.decidePersonaType()})):n=this._state.conversations[t]??at.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const i=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(n,!i,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});u(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[Lt]});u(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});u(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});u(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;en.set("copying");const s=e==null?void 0:e.chatHistory,n=s.reduce((a,l)=>(as(l)&&a.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),a),[]);if(n.length===0)throw new Error("No chat history to share");const i=at.getDisplayName(e),o=await this.extensionClient.saveChat(t,n,i);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});u(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void en.set("idle");navigator.clipboard.writeText(e),en.set("copied")}catch{en.set("failed")}});u(this,"deleteConversations",async(t,e=void 0,s=[],n)=>{const i=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${i>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);this.deleteConversationIds(o)}if(s.length>0&&n)for(const o of s)try{await n.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});u(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});u(this,"deleteConversationIds",async t=>{var s;const e=[];for(const n of t){const i=((s=this._state.conversations[n])==null?void 0:s.requestIds)??[];e.push(...i)}for(const n of Object.values(this._state.conversations))if(t.has(n.id)){for(const o of n.chatHistory)Gt(o)&&this.deleteImagesInExchange(o);const i=n.draftExchange;i&&this.deleteImagesInExchange(i)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([n])=>!t.has(n)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});u(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});u(this,"findImagesInJson",t=>{const e=[],s=n=>{var i;if(n.type==="image"&&((i=n.attrs)!=null&&i.src))e.push(n.attrs.src);else if((n.type==="doc"||n.type==="paragraph")&&n.content)for(const o of n.content)s(o)};return s(t),e});u(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===se.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));u(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});u(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});u(this,"smartPaste",(t,e,s,n)=>{const i=this._currConversationModel.historyTo(t,!0).filter(o=>as(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:i,targetFile:s??void 0,options:n})});u(this,"saveImage",async t=>await this.extensionClient.saveImage(t));u(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));u(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=n,this._chatFlagsModel=new ma(n.initialFlags),this.extensionClient=new _a(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new at(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new zl,this.initialize(n.initialConversation);const i=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ut(i),this.agentExecutionMode=ut(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ut(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ut(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get currentChatMode(){return this._currentChatMode}setCurrentChatMode(t){this._currentChatMode=t,this.extensionClient.setLastUsedChatMode(t)}get flagsLoaded(){return this._flagsLoaded}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const n=t||this._state.sortConversationsBy||"lastMessageTimestamp";let i=Object.values(this._state.conversations);return s&&(i=i.filter(s)),i.sort((o,a)=>{const l=at.getTime(o,n).getTime(),h=at.getTime(a,n).getTime();return e==="asc"?l-h:h-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===Lt)return!1;const n=!at.isValid(this.conversations[s]),i=ls(this.conversations[s]);return n&&(t==="agent"&&i||t==="chat"&&!i||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===ct.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}u(ki,"NEW_AGENT_KEY",Lt);function $r(r,t){let e,s,n=t;const i=()=>n.editor.getModifiedEditor(),o=()=>{const{afterLineNumber:a}=n,l=i();if(a===void 0)return void l.changeViewZones(c=>{e&&l&&s&&c.removeZone(s)});const h={...n,afterLineNumber:a,domNode:r,suppressMouseDown:!0};l==null||l.changeViewZones(c=>{e&&s&&c.removeZone(s),s=c.addZone(h),e=h})};return o(),{update:a=>{n=a,o()},destroy:()=>{const a=i();a.changeViewZones(l=>{if(e&&a&&s)try{l.removeZone(s)}catch(h){if(h instanceof Error){if(h.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${h}`)}})}}}var Te=(r=>(r.edit="edit",r.instruction="instruction",r))(Te||{}),Jn=(r=>(r[r.instructionDrawer=0]="instructionDrawer",r[r.chunkActionPanel=1]="chunkActionPanel",r))(Jn||{});const es=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,xr=new Set,ti=typeof process=="object"&&process?process:{},$o=(r,t,e,s)=>{typeof ti.emitWarning=="function"?ti.emitWarning(r,t,e,s):console.error(`[${e}] ${t}: ${r}`)};let yn=globalThis.AbortController,vr=globalThis.AbortSignal;var Br;if(yn===void 0){vr=class{constructor(){u(this,"onabort");u(this,"_onabort",[]);u(this,"reason");u(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},yn=class{constructor(){u(this,"signal",new vr);t()}abort(e){var s,n;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const i of this.signal._onabort)i(e);(n=(s=this.signal).onabort)==null||n.call(s,e)}}};let r=((Br=ti.env)==null?void 0:Br.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{r&&(r=!1,$o("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ve=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),xo=r=>ve(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?rn:null:null;class rn extends Array{constructor(t){super(t),this.fill(0)}}var cs;const Ue=class Ue{constructor(t,e){u(this,"heap");u(this,"length");if(!p(Ue,cs))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=xo(t);if(!e)return[];D(Ue,cs,!0);const s=new Ue(t,e);return D(Ue,cs,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};cs=new WeakMap,G(Ue,cs,!1);let ei=Ue;var Zr,Qr,Zt,Nt,Qt,Yt,hs,us,yt,Kt,pt,rt,P,At,Dt,St,wt,Xt,kt,Jt,te,zt,ee,Ie,Tt,M,ni,je,pe,Fs,qt,vo,He,ds,Os,we,ke,ii,on,an,it,ri,Ss,Ce,oi;const Mi=class Mi{constructor(t){G(this,M);G(this,Zt);G(this,Nt);G(this,Qt);G(this,Yt);G(this,hs);G(this,us);u(this,"ttl");u(this,"ttlResolution");u(this,"ttlAutopurge");u(this,"updateAgeOnGet");u(this,"updateAgeOnHas");u(this,"allowStale");u(this,"noDisposeOnSet");u(this,"noUpdateTTL");u(this,"maxEntrySize");u(this,"sizeCalculation");u(this,"noDeleteOnFetchRejection");u(this,"noDeleteOnStaleGet");u(this,"allowStaleOnFetchAbort");u(this,"allowStaleOnFetchRejection");u(this,"ignoreFetchAbort");G(this,yt);G(this,Kt);G(this,pt);G(this,rt);G(this,P);G(this,At);G(this,Dt);G(this,St);G(this,wt);G(this,Xt);G(this,kt);G(this,Jt);G(this,te);G(this,zt);G(this,ee);G(this,Ie);G(this,Tt);G(this,je,()=>{});G(this,pe,()=>{});G(this,Fs,()=>{});G(this,qt,()=>!1);G(this,He,t=>{});G(this,ds,(t,e,s)=>{});G(this,Os,(t,e,s,n)=>{if(s||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});u(this,Zr,"LRUCache");const{max:e=0,ttl:s,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:l,dispose:h,disposeAfter:c,noDisposeOnSet:d,noUpdateTTL:f,maxSize:g=0,maxEntrySize:x=0,sizeCalculation:w,fetchMethod:k,memoMethod:_,noDeleteOnFetchRejection:y,noDeleteOnStaleGet:C,allowStaleOnFetchRejection:F,allowStaleOnFetchAbort:N,ignoreFetchAbort:ot}=t;if(e!==0&&!ve(e))throw new TypeError("max option must be a nonnegative integer");const j=e?xo(e):Array;if(!j)throw new Error("invalid max value: "+e);if(D(this,Zt,e),D(this,Nt,g),this.maxEntrySize=x||p(this,Nt),this.sizeCalculation=w,this.sizeCalculation){if(!p(this,Nt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(_!==void 0&&typeof _!="function")throw new TypeError("memoMethod must be a function if defined");if(D(this,us,_),k!==void 0&&typeof k!="function")throw new TypeError("fetchMethod must be a function if specified");if(D(this,hs,k),D(this,Ie,!!k),D(this,pt,new Map),D(this,rt,new Array(e).fill(void 0)),D(this,P,new Array(e).fill(void 0)),D(this,At,new j(e)),D(this,Dt,new j(e)),D(this,St,0),D(this,wt,0),D(this,Xt,ei.create(e)),D(this,yt,0),D(this,Kt,0),typeof h=="function"&&D(this,Qt,h),typeof c=="function"?(D(this,Yt,c),D(this,kt,[])):(D(this,Yt,void 0),D(this,kt,void 0)),D(this,ee,!!p(this,Qt)),D(this,Tt,!!p(this,Yt)),this.noDisposeOnSet=!!d,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!y,this.allowStaleOnFetchRejection=!!F,this.allowStaleOnFetchAbort=!!N,this.ignoreFetchAbort=!!ot,this.maxEntrySize!==0){if(p(this,Nt)!==0&&!ve(p(this,Nt)))throw new TypeError("maxSize must be a positive integer if specified");if(!ve(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");T(this,M,vo).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!C,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ve(n)||n===0?n:1,this.ttlAutopurge=!!i,this.ttl=s||0,this.ttl){if(!ve(this.ttl))throw new TypeError("ttl must be a positive integer if specified");T(this,M,ni).call(this)}if(p(this,Zt)===0&&this.ttl===0&&p(this,Nt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!p(this,Zt)&&!p(this,Nt)){const V="LRU_CACHE_UNBOUNDED";(U=>!xr.has(U))(V)&&(xr.add(V),$o("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",V,Mi))}}static unsafeExposeInternals(t){return{starts:p(t,te),ttls:p(t,zt),sizes:p(t,Jt),keyMap:p(t,pt),keyList:p(t,rt),valList:p(t,P),next:p(t,At),prev:p(t,Dt),get head(){return p(t,St)},get tail(){return p(t,wt)},free:p(t,Xt),isBackgroundFetch:e=>{var s;return T(s=t,M,it).call(s,e)},backgroundFetch:(e,s,n,i)=>{var o;return T(o=t,M,an).call(o,e,s,n,i)},moveToTail:e=>{var s;return T(s=t,M,Ss).call(s,e)},indexes:e=>{var s;return T(s=t,M,we).call(s,e)},rindexes:e=>{var s;return T(s=t,M,ke).call(s,e)},isStale:e=>{var s;return p(s=t,qt).call(s,e)}}}get max(){return p(this,Zt)}get maxSize(){return p(this,Nt)}get calculatedSize(){return p(this,Kt)}get size(){return p(this,yt)}get fetchMethod(){return p(this,hs)}get memoMethod(){return p(this,us)}get dispose(){return p(this,Qt)}get disposeAfter(){return p(this,Yt)}getRemainingTTL(t){return p(this,pt).has(t)?1/0:0}*entries(){for(const t of T(this,M,we).call(this))p(this,P)[t]===void 0||p(this,rt)[t]===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield[p(this,rt)[t],p(this,P)[t]])}*rentries(){for(const t of T(this,M,ke).call(this))p(this,P)[t]===void 0||p(this,rt)[t]===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield[p(this,rt)[t],p(this,P)[t]])}*keys(){for(const t of T(this,M,we).call(this)){const e=p(this,rt)[t];e===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield e)}}*rkeys(){for(const t of T(this,M,ke).call(this)){const e=p(this,rt)[t];e===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield e)}}*values(){for(const t of T(this,M,we).call(this))p(this,P)[t]===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield p(this,P)[t])}*rvalues(){for(const t of T(this,M,ke).call(this))p(this,P)[t]===void 0||T(this,M,it).call(this,p(this,P)[t])||(yield p(this,P)[t])}[(Qr=Symbol.iterator,Zr=Symbol.toStringTag,Qr)](){return this.entries()}find(t,e={}){for(const s of T(this,M,we).call(this)){const n=p(this,P)[s],i=T(this,M,it).call(this,n)?n.__staleWhileFetching:n;if(i!==void 0&&t(i,p(this,rt)[s],this))return this.get(p(this,rt)[s],e)}}forEach(t,e=this){for(const s of T(this,M,we).call(this)){const n=p(this,P)[s],i=T(this,M,it).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,p(this,rt)[s],this)}}rforEach(t,e=this){for(const s of T(this,M,ke).call(this)){const n=p(this,P)[s],i=T(this,M,it).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,p(this,rt)[s],this)}}purgeStale(){let t=!1;for(const e of T(this,M,ke).call(this,{allowStale:!0}))p(this,qt).call(this,e)&&(T(this,M,Ce).call(this,p(this,rt)[e],"expire"),t=!0);return t}info(t){const e=p(this,pt).get(t);if(e===void 0)return;const s=p(this,P)[e],n=T(this,M,it).call(this,s)?s.__staleWhileFetching:s;if(n===void 0)return;const i={value:n};if(p(this,zt)&&p(this,te)){const o=p(this,zt)[e],a=p(this,te)[e];if(o&&a){const l=o-(es.now()-a);i.ttl=l,i.start=Date.now()}}return p(this,Jt)&&(i.size=p(this,Jt)[e]),i}dump(){const t=[];for(const e of T(this,M,we).call(this,{allowStale:!0})){const s=p(this,rt)[e],n=p(this,P)[e],i=T(this,M,it).call(this,n)?n.__staleWhileFetching:n;if(i===void 0||s===void 0)continue;const o={value:i};if(p(this,zt)&&p(this,te)){o.ttl=p(this,zt)[e];const a=es.now()-p(this,te)[e];o.start=Math.floor(Date.now()-a)}p(this,Jt)&&(o.size=p(this,Jt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const n=Date.now()-s.start;s.start=es.now()-n}this.set(e,s.value,s)}}set(t,e,s={}){var f,g,x,w,k;if(e===void 0)return this.delete(t),this;const{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:l}=s;let{noUpdateTTL:h=this.noUpdateTTL}=s;const c=p(this,Os).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&c>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),T(this,M,Ce).call(this,t,"set"),this;let d=p(this,yt)===0?void 0:p(this,pt).get(t);if(d===void 0)d=p(this,yt)===0?p(this,wt):p(this,Xt).length!==0?p(this,Xt).pop():p(this,yt)===p(this,Zt)?T(this,M,on).call(this,!1):p(this,yt),p(this,rt)[d]=t,p(this,P)[d]=e,p(this,pt).set(t,d),p(this,At)[p(this,wt)]=d,p(this,Dt)[d]=p(this,wt),D(this,wt,d),Xs(this,yt)._++,p(this,ds).call(this,d,c,l),l&&(l.set="add"),h=!1;else{T(this,M,Ss).call(this,d);const _=p(this,P)[d];if(e!==_){if(p(this,Ie)&&T(this,M,it).call(this,_)){_.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:y}=_;y===void 0||o||(p(this,ee)&&((f=p(this,Qt))==null||f.call(this,y,t,"set")),p(this,Tt)&&((g=p(this,kt))==null||g.push([y,t,"set"])))}else o||(p(this,ee)&&((x=p(this,Qt))==null||x.call(this,_,t,"set")),p(this,Tt)&&((w=p(this,kt))==null||w.push([_,t,"set"])));if(p(this,He).call(this,d),p(this,ds).call(this,d,c,l),p(this,P)[d]=e,l){l.set="replace";const y=_&&T(this,M,it).call(this,_)?_.__staleWhileFetching:_;y!==void 0&&(l.oldValue=y)}}else l&&(l.set="update")}if(n===0||p(this,zt)||T(this,M,ni).call(this),p(this,zt)&&(h||p(this,Fs).call(this,d,n,i),l&&p(this,pe).call(this,l,d)),!o&&p(this,Tt)&&p(this,kt)){const _=p(this,kt);let y;for(;y=_==null?void 0:_.shift();)(k=p(this,Yt))==null||k.call(this,...y)}return this}pop(){var t;try{for(;p(this,yt);){const e=p(this,P)[p(this,St)];if(T(this,M,on).call(this,!0),T(this,M,it).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(p(this,Tt)&&p(this,kt)){const e=p(this,kt);let s;for(;s=e==null?void 0:e.shift();)(t=p(this,Yt))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:n}=e,i=p(this,pt).get(t);if(i!==void 0){const o=p(this,P)[i];if(T(this,M,it).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!p(this,qt).call(this,i))return s&&p(this,je).call(this,i),n&&(n.has="hit",p(this,pe).call(this,n,i)),!0;n&&(n.has="stale",p(this,pe).call(this,n,i))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,n=p(this,pt).get(t);if(n===void 0||!s&&p(this,qt).call(this,n))return;const i=p(this,P)[n];return T(this,M,it).call(this,i)?i.__staleWhileFetching:i}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:l=0,sizeCalculation:h=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:d=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:x=this.allowStaleOnFetchAbort,context:w,forceRefresh:k=!1,status:_,signal:y}=e;if(!p(this,Ie))return _&&(_.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:_});const C={allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:l,sizeCalculation:h,noUpdateTTL:c,noDeleteOnFetchRejection:d,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:x,ignoreFetchAbort:g,status:_,signal:y};let F=p(this,pt).get(t);if(F===void 0){_&&(_.fetch="miss");const N=T(this,M,an).call(this,t,F,C,w);return N.__returned=N}{const N=p(this,P)[F];if(T(this,M,it).call(this,N)){const U=s&&N.__staleWhileFetching!==void 0;return _&&(_.fetch="inflight",U&&(_.returnedStale=!0)),U?N.__staleWhileFetching:N.__returned=N}const ot=p(this,qt).call(this,F);if(!k&&!ot)return _&&(_.fetch="hit"),T(this,M,Ss).call(this,F),n&&p(this,je).call(this,F),_&&p(this,pe).call(this,_,F),N;const j=T(this,M,an).call(this,t,F,C,w),V=j.__staleWhileFetching!==void 0&&s;return _&&(_.fetch=ot?"stale":"refresh",V&&ot&&(_.returnedStale=!0)),V?j.__staleWhileFetching:j.__returned=j}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=p(this,us);if(!s)throw new Error("no memoMethod provided to constructor");const{context:n,forceRefresh:i,...o}=e,a=this.get(t,o);if(!i&&a!==void 0)return a;const l=s(t,a,{options:o,context:n});return this.set(t,l,o),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=e,a=p(this,pt).get(t);if(a!==void 0){const l=p(this,P)[a],h=T(this,M,it).call(this,l);return o&&p(this,pe).call(this,o,a),p(this,qt).call(this,a)?(o&&(o.get="stale"),h?(o&&s&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?l.__staleWhileFetching:void 0):(i||T(this,M,Ce).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?l:void 0)):(o&&(o.get="hit"),h?l.__staleWhileFetching:(T(this,M,Ss).call(this,a),n&&p(this,je).call(this,a),l))}o&&(o.get="miss")}delete(t){return T(this,M,Ce).call(this,t,"delete")}clear(){return T(this,M,oi).call(this,"delete")}};Zt=new WeakMap,Nt=new WeakMap,Qt=new WeakMap,Yt=new WeakMap,hs=new WeakMap,us=new WeakMap,yt=new WeakMap,Kt=new WeakMap,pt=new WeakMap,rt=new WeakMap,P=new WeakMap,At=new WeakMap,Dt=new WeakMap,St=new WeakMap,wt=new WeakMap,Xt=new WeakMap,kt=new WeakMap,Jt=new WeakMap,te=new WeakMap,zt=new WeakMap,ee=new WeakMap,Ie=new WeakMap,Tt=new WeakMap,M=new WeakSet,ni=function(){const t=new rn(p(this,Zt)),e=new rn(p(this,Zt));D(this,zt,t),D(this,te,e),D(this,Fs,(i,o,a=es.now())=>{if(e[i]=o!==0?a:0,t[i]=o,o!==0&&this.ttlAutopurge){const l=setTimeout(()=>{p(this,qt).call(this,i)&&T(this,M,Ce).call(this,p(this,rt)[i],"expire")},o+1);l.unref&&l.unref()}}),D(this,je,i=>{e[i]=t[i]!==0?es.now():0}),D(this,pe,(i,o)=>{if(t[o]){const a=t[o],l=e[o];if(!a||!l)return;i.ttl=a,i.start=l,i.now=s||n();const h=i.now-l;i.remainingTTL=a-h}});let s=0;const n=()=>{const i=es.now();if(this.ttlResolution>0){s=i;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return i};this.getRemainingTTL=i=>{const o=p(this,pt).get(i);if(o===void 0)return 0;const a=t[o],l=e[o];return!a||!l?1/0:a-((s||n())-l)},D(this,qt,i=>{const o=e[i],a=t[i];return!!a&&!!o&&(s||n())-o>a})},je=new WeakMap,pe=new WeakMap,Fs=new WeakMap,qt=new WeakMap,vo=function(){const t=new rn(p(this,Zt));D(this,Kt,0),D(this,Jt,t),D(this,He,e=>{D(this,Kt,p(this,Kt)-t[e]),t[e]=0}),D(this,Os,(e,s,n,i)=>{if(T(this,M,it).call(this,s))return 0;if(!ve(n)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof i!="function")throw new TypeError("sizeCalculation must be a function");if(n=i(s,e),!ve(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return n}),D(this,ds,(e,s,n)=>{if(t[e]=s,p(this,Nt)){const i=p(this,Nt)-t[e];for(;p(this,Kt)>i;)T(this,M,on).call(this,!0)}D(this,Kt,p(this,Kt)+t[e]),n&&(n.entrySize=s,n.totalCalculatedSize=p(this,Kt))})},He=new WeakMap,ds=new WeakMap,Os=new WeakMap,we=function*({allowStale:t=this.allowStale}={}){if(p(this,yt))for(let e=p(this,wt);T(this,M,ii).call(this,e)&&(!t&&p(this,qt).call(this,e)||(yield e),e!==p(this,St));)e=p(this,Dt)[e]},ke=function*({allowStale:t=this.allowStale}={}){if(p(this,yt))for(let e=p(this,St);T(this,M,ii).call(this,e)&&(!t&&p(this,qt).call(this,e)||(yield e),e!==p(this,wt));)e=p(this,At)[e]},ii=function(t){return t!==void 0&&p(this,pt).get(p(this,rt)[t])===t},on=function(t){var i,o;const e=p(this,St),s=p(this,rt)[e],n=p(this,P)[e];return p(this,Ie)&&T(this,M,it).call(this,n)?n.__abortController.abort(new Error("evicted")):(p(this,ee)||p(this,Tt))&&(p(this,ee)&&((i=p(this,Qt))==null||i.call(this,n,s,"evict")),p(this,Tt)&&((o=p(this,kt))==null||o.push([n,s,"evict"]))),p(this,He).call(this,e),t&&(p(this,rt)[e]=void 0,p(this,P)[e]=void 0,p(this,Xt).push(e)),p(this,yt)===1?(D(this,St,D(this,wt,0)),p(this,Xt).length=0):D(this,St,p(this,At)[e]),p(this,pt).delete(s),Xs(this,yt)._--,e},an=function(t,e,s,n){const i=e===void 0?void 0:p(this,P)[e];if(T(this,M,it).call(this,i))return i;const o=new yn,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const l={signal:o.signal,options:s,context:n},h=(g,x=!1)=>{const{aborted:w}=o.signal,k=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(w&&!x?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,k&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),w&&!k&&!x)return c(o.signal.reason);const _=d;return p(this,P)[e]===d&&(g===void 0?_.__staleWhileFetching?p(this,P)[e]=_.__staleWhileFetching:T(this,M,Ce).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,l.options))),g},c=g=>{const{aborted:x}=o.signal,w=x&&s.allowStaleOnFetchAbort,k=w||s.allowStaleOnFetchRejection,_=k||s.noDeleteOnFetchRejection,y=d;if(p(this,P)[e]===d&&(!_||y.__staleWhileFetching===void 0?T(this,M,Ce).call(this,t,"fetch"):w||(p(this,P)[e]=y.__staleWhileFetching)),k)return s.status&&y.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),y.__staleWhileFetching;if(y.__returned===y)throw g};s.status&&(s.status.fetchDispatched=!0);const d=new Promise((g,x)=>{var k;const w=(k=p(this,hs))==null?void 0:k.call(this,t,i,l);w&&w instanceof Promise&&w.then(_=>g(_===void 0?void 0:_),x),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=_=>h(_,!0)))})}).then(h,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),c(g))),f=Object.assign(d,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return e===void 0?(this.set(t,f,{...l.options,status:void 0}),e=p(this,pt).get(t)):p(this,P)[e]=f,f},it=function(t){if(!p(this,Ie))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof yn},ri=function(t,e){p(this,Dt)[e]=t,p(this,At)[t]=e},Ss=function(t){t!==p(this,wt)&&(t===p(this,St)?D(this,St,p(this,At)[t]):T(this,M,ri).call(this,p(this,Dt)[t],p(this,At)[t]),T(this,M,ri).call(this,p(this,wt),t),D(this,wt,t))},Ce=function(t,e){var n,i,o,a;let s=!1;if(p(this,yt)!==0){const l=p(this,pt).get(t);if(l!==void 0)if(s=!0,p(this,yt)===1)T(this,M,oi).call(this,e);else{p(this,He).call(this,l);const h=p(this,P)[l];if(T(this,M,it).call(this,h)?h.__abortController.abort(new Error("deleted")):(p(this,ee)||p(this,Tt))&&(p(this,ee)&&((n=p(this,Qt))==null||n.call(this,h,t,e)),p(this,Tt)&&((i=p(this,kt))==null||i.push([h,t,e]))),p(this,pt).delete(t),p(this,rt)[l]=void 0,p(this,P)[l]=void 0,l===p(this,wt))D(this,wt,p(this,Dt)[l]);else if(l===p(this,St))D(this,St,p(this,At)[l]);else{const c=p(this,Dt)[l];p(this,At)[c]=p(this,At)[l];const d=p(this,At)[l];p(this,Dt)[d]=p(this,Dt)[l]}Xs(this,yt)._--,p(this,Xt).push(l)}}if(p(this,Tt)&&((o=p(this,kt))!=null&&o.length)){const l=p(this,kt);let h;for(;h=l==null?void 0:l.shift();)(a=p(this,Yt))==null||a.call(this,...h)}return s},oi=function(t){var e,s,n;for(const i of T(this,M,ke).call(this,{allowStale:!0})){const o=p(this,P)[i];if(T(this,M,it).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=p(this,rt)[i];p(this,ee)&&((e=p(this,Qt))==null||e.call(this,o,a,t)),p(this,Tt)&&((s=p(this,kt))==null||s.push([o,a,t]))}}if(p(this,pt).clear(),p(this,P).fill(void 0),p(this,rt).fill(void 0),p(this,zt)&&p(this,te)&&(p(this,zt).fill(0),p(this,te).fill(0)),p(this,Jt)&&p(this,Jt).fill(0),D(this,St,0),D(this,wt,0),p(this,Xt).length=0,D(this,Kt,0),D(this,yt,0),p(this,Tt)&&p(this,kt)){const i=p(this,kt);let o;for(;o=i==null?void 0:i.shift();)(n=p(this,Yt))==null||n.call(this,...o)}};let si=Mi;class wo{constructor(){u(this,"_syncStatus",{status:Ea.done,foldersProgress:[]});u(this,"_syncEnabledState",er.initializing);u(this,"_workspaceGuidelines",[]);u(this,"_openUserGuidelinesInput",!1);u(this,"_userGuidelines");u(this,"_contextStore",new ql);u(this,"_prevOpenFiles",[]);u(this,"_disableContext",!1);u(this,"_enableAgentMemories",!1);u(this,"subscribers",new Set);u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case ct.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case ct.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case ct.fileRangesSelected:this.updateSelections(e.data);break;case ct.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case ct.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case ct.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});u(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:bt.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});u(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});u(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});u(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});u(this,"addFile",t=>{this.addFiles([t])});u(this,"addFiles",t=>{this.updateFiles(t,[])});u(this,"removeFile",t=>{this.removeFiles([t])});u(this,"removeFiles",t=>{this.updateFiles([],t)});u(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});u(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});u(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...Dn(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...va(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});u(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});u(this,"setCurrentlyOpenFiles",t=>{const e=t.map(n=>({recentFile:n,...Dn(n)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,n=>n.id),s.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.recentFile&&(i.file=i.recentFile,delete i.recentFile)}),e.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.file&&(i.recentFile=i.file,delete i.file)}),this.notifySubscribers()});u(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});u(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,n=t.overLimit||((e==null?void 0:e.overLimit)??!1),i={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:bt.active,referenceCount:1,showWarning:n,rulesAndGuidelinesState:e};this._contextStore.update([i],s,o=>o.id),this.notifySubscribers()});u(this,"onGuidelinesStateUpdate",t=>{var n;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const i=e||{enabled:!1,overLimit:!1,contents:"",lengthLimit:((n=t.rulesAndGuidelines)==null?void 0:n.lengthLimit)??2e3};this.updateUserGuidelines(i,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(i=>i.sourceFolder))});u(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(n=>n.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));u(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});u(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});u(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});u(this,"updateSelections",t=>{const e=this._contextStore.values.filter(un),s=t.map(n=>({selection:n,...Dn(n)}));this._contextStore.update([],e,n=>n.id),this._contextStore.update(s,[],n=>n.id),this.notifySubscribers()});u(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});u(this,"markInactive",t=>{this.markItemsInactive([t])});u(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,bt.inactive)}),this.notifySubscribers()});u(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});u(this,"markActive",t=>{this.markItemsActive([t])});u(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,bt.active)}),this.notifySubscribers()});u(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});u(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});u(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>gi(t)&&!hn(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(hn)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(un)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(mi)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(dn)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(_i)}get userGuidelines(){return this._contextStore.values.filter(fn)}get agentMemories(){return[{...xa,status:this._enableAgentMemories?bt.active:bt.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>pn(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===bt.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===bt.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===bt.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===bt.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===bt.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===bt.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===er.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(h=>h.progress!==void 0);if(t.length===0)return;const e=t.reduce((h,c)=>{var d;return h+(((d=c==null?void 0:c.progress)==null?void 0:d.trackedFiles)??0)},0),s=t.reduce((h,c)=>{var d;return h+(((d=c==null?void 0:c.progress)==null?void 0:d.backlogSize)??0)},0),n=Math.max(e,0),i=Math.min(Math.max(s,0),n),o=n-i,a=[];for(const h of t)(l=h==null?void 0:h.progress)!=null&&l.newlyTracked&&a.push(h.folderRoot);return{status:this._syncStatus.status,totalFiles:n,syncedCount:o,backlogSize:i,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(dn(t)||fn(t)||xn(t)||pn(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===bt.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===bt.inactive)}get isContextDisabled(){return this._disableContext}}class ql{constructor(){u(this,"_cache",new si({max:1e3}));u(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));u(this,"clear",()=>{this._cache.clear()});u(this,"update",(t,e,s)=>{t.forEach(n=>this.addInPlace(n,s)),e.forEach(n=>this.removeInPlace(n,s))});u(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});u(this,"addInPlace",(t,e)=>{const s=e(t),n=t.referenceCount??1,i=this._cache.get(s),o=t.status??(i==null?void 0:i.status)??bt.active;i?(i.referenceCount+=n,i.status=o,i.pinned=t.pinned??i.pinned,i.showWarning=t.showWarning??i.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in i&&(i.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in i&&(i.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:n,status:o})});u(this,"removeInPlace",(t,e)=>{const s=e(t),n=this._cache.get(s);n&&(n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(s))});u(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});u(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});u(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});u(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});u(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===bt.active?bt.inactive:bt.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Pl{constructor(t,e,s){u(this,"_originalModel");u(this,"_modifiedModel");u(this,"_fullEdits",[]);u(this,"_currEdit");u(this,"_currOriginalEdit");u(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(e=>{this._modifiedModel.applyEdits([e])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});u(this,"finish",()=>this._completeCurrEdit());u(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);u(this,"_completeCurrEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};if(!t)return e;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const s=this._nextModifiedInsertPosition(),n=t.stagedEndLine-t.stagedStartLine,i={range:new this._monaco.Range(s.lineNumber,0,s.lineNumber+n,0),text:""};e.modified.push(i),this._modifiedModel.applyEdits([i]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return e});u(this,"_startNewEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},e.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),e});u(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const e=this._nextModifiedInsertPosition(),s={...this._currEdit,text:t.newText,range:new this._monaco.Range(e.lineNumber,e.column,e.lineNumber,e.column)};return this._modifiedModel.applyEdits([s]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[s]:[]}});u(this,"_nextModifiedInsertPosition",()=>{var e;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((e=this._currEdit.text)==null?void 0:e.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=e,this._monaco=s,this._originalModel=this._monaco.editor.createModel(e),this._modifiedModel=this._monaco.editor.createModel(e)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Ul{constructor(t,e,s){u(this,"_asyncMsgSender");u(this,"_editor");u(this,"_chatModel");u(this,"_focusModel",new io);u(this,"_hasScrolledOnInit",!1);u(this,"_markHasScrolledOnInit",Qn(()=>{this._hasScrolledOnInit=!0},200));u(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});u(this,"_subscribers",new Set);u(this,"_disposables",[]);u(this,"_rootChunk");u(this,"_keybindings",ut({}));u(this,"_requestId",ut(void 0));u(this,"requestId",this._requestId);u(this,"_disableResolution",ut(!1));u(this,"disableResolution",Ji(this._disableResolution));u(this,"_disableApply",ut(!1));u(this,"disableApply",Ji(this._disableApply));u(this,"_currStream");u(this,"_isLoadingDiffChunks",ut(!1));u(this,"_selectionLines",ut(void 0));u(this,"_mode",ut(Te.edit));u(this,"initializeEditor",t=>{var e,s,n,i,o,a,l,h,c,d,f,g;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new ki(new oo(Ms),Ms,new wo),(s=(e=this._monaco.editor).registerCommand)==null||s.call(e,"acceptFocusedChunk",this.acceptFocusedChunk),(i=(n=this._monaco.editor).registerCommand)==null||i.call(n,"rejectFocusedChunk",this.rejectFocusedChunk),(a=(o=this._monaco.editor).registerCommand)==null||a.call(o,"acceptAllChunks",this.acceptAllChunks),(h=(l=this._monaco.editor).registerCommand)==null||h.call(l,"rejectAllChunks",this.rejectAllChunks),(d=(c=this._monaco.editor).registerCommand)==null||d.call(c,"focusNextChunk",this.focusNextChunk),(g=(f=this._monaco.editor).registerCommand)==null||g.call(f,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(x=>this.notifySubscribers())}),this.initialize()});u(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));u(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});u(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});u(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const e=this.leaves[0];this.revealChunk(e)}this.notifyDiffViewUpdated(),this.notifySubscribers()});u(this,"onMouseMoveModified",t=>{var n,i,o,a,l,h;if(((n=t.target.position)==null?void 0:n.lineNumber)===void 0||this.leaves===void 0)return;const e=this.editorOffset,s=(i=t.target.position)==null?void 0:i.lineNumber;for(let c=0;c<this.leaves.length;c++){const d=this.leaves[c],f=(o=d.unitOfCodeWork.lineChanges)==null?void 0:o.lineChanges[0].modifiedStart,g=(a=d.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedEnd,x=(l=d.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].originalStart,w=(h=d.unitOfCodeWork.lineChanges)==null?void 0:h.lineChanges[0].originalEnd;if(f!==void 0&&g!==void 0&&x!==void 0&&w!==void 0){if(f!==g||s!==f){if(f<=s&&s<g){this.setCurrFocusedChunkIdx(c,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const k=this._editor.getOriginalEditor(),_=k.getOption(this._monaco.editor.EditorOption.lineHeight),y=k.getScrolledVisiblePosition({lineNumber:x,column:0}),C=k.getScrolledVisiblePosition({lineNumber:w+1,column:0});if(y===null||C===null)continue;const F=y.top-_/2+e,N=C.top-_/2+e;if(t.event.posy>=F&&t.event.posy<=N){this.setCurrFocusedChunkIdx(c,!1);break}break}}}});u(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:ct.diffViewWindowFocusChange,data:t})});u(this,"setCurrFocusedChunkIdx",(t,e=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),e&&this.revealCurrFocusedChunk(),this.notifySubscribers())});u(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});u(this,"revealChunk",t=>{var n;const e=(n=t.unitOfCodeWork.lineChanges)==null?void 0:n.lineChanges[0],s=e==null?void 0:e.modifiedStart;s!==void 0&&this._editor.revealLineNearTop(s-1)});u(this,"renderCentralOverlayWidget",t=>{const e=()=>({editor:this._editor,id:"central-overlay-widget"}),s=function(n,i,o){let a,l=i;const h=()=>l.editor.getModifiedEditor(),c=()=>{const d=h();if(!d)return;const f={getDomNode:()=>n,getId:()=>l.id,getPosition:()=>({preference:o.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};a&&d.removeOverlayWidget(a),d.addOverlayWidget(f),a=f};return c(),{update:d=>{l=d,c()},destroy:()=>{const d=h();d&&a&&d.removeOverlayWidget(a)}}}(t,e(),{monaco:this._monaco});return{update:()=>{s.update(e())},destroy:s.destroy}});u(this,"renderInstructionsDrawerViewZone",(t,e)=>{let s=!1,n=e;const i=e.autoFocus??!0,o=c=>{i&&!s&&(this._editor.revealLineNearTop(c),s=!0)},a=c=>({...c,ordinal:Jn.instructionDrawer,editor:this._editor,afterLineNumber:c.line}),l=$r(t,a(e)),h=[];return i&&h.push(this._editor.onDidUpdateDiff(()=>{o(n.line)})),{update:c=>{const d={...n,...c};al(d,n)||(l.update(a(d)),n=d,o(d.line))},destroy:()=>{l.destroy(),h.forEach(c=>c.dispose())}}});u(this,"renderActionsViewZone",(t,e)=>{const s=i=>{var a;let o;return o=i.chunk?(a=i.chunk.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedStart:1,{...i,ordinal:Jn.chunkActionPanel,editor:this._editor,afterLineNumber:o?o-1:void 0}},n=$r(t,s(e));return{update:i=>{n.update(s(i))},destroy:n.destroy}});u(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});u(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});u(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});u(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});u(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});u(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});u(this,"initialize",async()=>{var l;const t=await this._asyncMsgSender.send({type:ct.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:e,instruction:s,keybindings:n,editable:i}=t.data;this._editor.updateOptions({readOnly:!i});const o=ht(this._keybindings);this._keybindings.set(n??o);const a=s==null?void 0:s.selection;a&&(a.start.line===a.end.line&&a.start.character===a.end.character&&this._mode.set(Te.instruction),ht(this.selectionLines)===void 0&&this._selectionLines.set({start:a.start.line,end:a.end.line})),this.updateModels(e.originalCode??"",e.modifiedCode??"",{rootPath:e.repoRoot,relPath:e.pathName}),(l=this._currStream)==null||l.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});u(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:ct.disposeDiffView})});u(this,"_tryFetchStream",async()=>{var e,s,n;const t=this._asyncMsgSender.stream({type:ct.diffViewFetchPendingStream},15e3,6e4);for await(const i of t)switch(i.type){case ct.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(i.data.requestId);const o=this._editor.getOriginalEditor().getValue();this._currStream=new Pl(i.data.streamId,o,this._monaco),this._syncStreamToModels();break}case ct.diffViewDiffStreamEnded:if(((e=this._currStream)==null?void 0:e.id)!==i.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case ct.diffViewDiffStreamChunk:{if(((s=this._currStream)==null?void 0:s.id)!==i.data.streamId)return;const o=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!o)return this.setLoading(!1),void this._cleanupStream();const a=(n=this._currStream)==null?void 0:n.onReceiveChunk(i);a&&(this._applyDeltaDiff(a),ht(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});u(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case ct.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case ct.diffViewAcceptAllChunks:this.acceptAllChunks();break;case ct.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case ct.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case ct.diffViewFocusPrevChunk:this.focusPrevChunk();break;case ct.diffViewFocusNextChunk:this.focusNextChunk()}});u(this,"_applyDeltaDiff",t=>{const e=this._editor.getOriginalEditor().getModel(),s=this._editor.getModifiedEditor().getModel();e&&s&&(e.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(n=>{e.pushEditOperations([],[n],()=>[])}),t.modified.forEach(n=>{s.pushEditOperations([],[n],()=>[])}))});u(this,"_cleanupStream",()=>{var t;if(this._currStream){const e=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(e),this._currStream=void 0,this._resetScrollOnInit()}});u(this,"_syncStreamToModels",()=>{var s,n;const t=(s=this._currStream)==null?void 0:s.originalValue,e=(n=this._currStream)==null?void 0:n.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),e&&e!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(e)});u(this,"acceptChunk",async t=>{ht(this._disableApply)||this.acceptChunks([t])});u(this,"acceptChunks",async(t,e=!1)=>{ht(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Ln.accept,e),await Gn(),this.areModelsEqual()&&!ht(this.isLoading)&&this.disposeDiffViewPanel())});u(this,"areModelsEqual",()=>{var s,n;const t=(s=this._editor.getModel())==null?void 0:s.original,e=(n=this._editor.getModel())==null?void 0:n.modified;return(t==null?void 0:t.getValue())===(e==null?void 0:e.getValue())});u(this,"rejectChunk",async t=>{this.rejectChunks([t])});u(this,"rejectChunks",async(t,e=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Ln.reject,e),await Gn(),this.areModelsEqual()&&!ht(this.isLoading)&&this.disposeDiffViewPanel()});u(this,"notifyDiffViewUpdated",Qn(()=>{this.notifyResolvedChunks([],Ln.accept)},1e3));u(this,"notifyResolvedChunks",async(t,e,s=!1)=>{var i;const n=(i=this._editor.getModel())==null?void 0:i.original.uri.path;n&&await this._asyncMsgSender.send({type:ct.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:n,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(o=>o.unitOfCodeWork),resolveType:e,shouldApplyToAll:s}},2e3)});u(this,"executeDiffChunks",(t,e)=>{var c,d,f;if(ht(this._disableResolution)||e&&ht(this._disableApply))return;const s=(c=this._editor.getModel())==null?void 0:c.original,n=(d=this._editor.getModel())==null?void 0:d.modified;if(!s||!n||this._currStream!==void 0)return;const i=[],o=[];for(const g of t){const x=(f=g.unitOfCodeWork.lineChanges)==null?void 0:f.lineChanges[0];if(!x||g.unitOfCodeWork.originalCode===void 0||g.unitOfCodeWork.modifiedCode===void 0)continue;let w={startLineNumber:x.originalStart,startColumn:1,endLineNumber:x.originalEnd,endColumn:1},k={startLineNumber:x.modifiedStart,startColumn:1,endLineNumber:x.modifiedEnd,endColumn:1};const _=e?g.unitOfCodeWork.modifiedCode:g.unitOfCodeWork.originalCode;_!==void 0&&(i.push({range:w,text:_}),o.push({range:k,text:_}))}s.pushEditOperations([],i,()=>[]),n.pushEditOperations([],o,()=>[]);const a=this._focusModel.nextIdx({nowrap:!0});if(a===void 0)return;const l=a===this._focusModel.focusedItemIdx?a-1:a,h=this._focusModel.items[l];h&&this.revealChunk(h)});u(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});u(this,"handleInstructionSubmit",t=>{const e=this._editor.getModifiedEditor(),s=this.getSelectedCodeDetails(e);if(!s)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,s)});u(this,"updateModels",(t,e,s)=>{var o,a;const n=(a=(o=this._editor.getModel())==null?void 0:o.original)==null?void 0:a.uri,i=(s&&this._monaco.Uri.file(s.relPath))??n;if(i)if((n==null?void 0:n.fsPath)!==i.fsPath||(n==null?void 0:n.authority)!==i.authority){const l=i.with({fragment:crypto.randomUUID()}),h=i.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,l),modified:this._monaco.editor.createModel(e??"",void 0,h)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==e&&this.getModifiedEditor().setValue(e??"");else console.warn("No URI found for diff view. Not updating models.")});u(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=s,this._asyncMsgSender=new pa(n=>Ms.postMessage(n)),this.initializeEditor(e)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return co(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var i,o;const t=[],e=this._editor.getLineChanges(),s=(i=this._editor.getModel())==null?void 0:i.original,n=(o=this._editor.getModel())==null?void 0:o.modified;if(e&&s&&n){for(const a of e){const l=wr({startLineNumber:a.originalStartLineNumber,startColumn:1,endLineNumber:a.originalEndLineNumber,endColumn:1}),h=wr({startLineNumber:a.modifiedStartLineNumber,startColumn:1,endLineNumber:a.modifiedEndLineNumber,endColumn:1}),c=jl(this._editor,l,h);t.push(c)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(a=>a.id)}}}getSelectedCodeDetails(t){const e=t.getModel();if(!e)return null;const s=e.getLanguageId(),n=1,i=1,o={lineNumber:e.getLineCount(),column:e.getLineMaxColumn(e.getLineCount())},a=ht(this._selectionLines);if(!a)throw new Error("No selection lines found");const l=Math.min(a.end+1,o.lineNumber),h=new this._monaco.Range(a.start+1,1,l,e.getLineMaxColumn(l));let c=e.getValueInRange(h);l<e.getLineCount()&&(c+=e.getEOL());const d=new this._monaco.Range(n,i,h.startLineNumber,h.startColumn),f=Math.min(h.endLineNumber+1,o.lineNumber),g=new this._monaco.Range(f,1,o.lineNumber,o.column);return{selectedCode:c,prefix:e.getValueInRange(d),suffix:e.getValueInRange(g),path:e.uri.path,language:s,prefixBegin:d.startLineNumber-1,suffixEnd:g.endLineNumber-1}}}function jl(r,t,e){var i,o;const s=(i=r.getModel())==null?void 0:i.original,n=(o=r.getModel())==null?void 0:o.modified;if(!s||!n)throw new Error("No models found");return function(a,l,h,c){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:a,modifiedCode:l,lineChanges:{lineChanges:[{originalStart:h.startLineNumber,originalEnd:h.endLineNumber,modifiedStart:c.startLineNumber,modifiedEnd:c.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(s.getValueInRange(t),n.getValueInRange(e),t,e)}function wr(r){return r.endLineNumber===0?{startLineNumber:r.startLineNumber+1,startColumn:1,endLineNumber:r.startLineNumber+1,endColumn:1}:{startLineNumber:r.startLineNumber,startColumn:1,endLineNumber:r.endLineNumber+1,endColumn:1}}function kr(r){let t,e;return t=new Ae({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Hl]},$$scope:{ctx:r}}}),t.$on("click",function(){me(r[4])&&r[4].apply(this,arguments)}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){r=s;const i={};132096&n&&(i.$$scope={dirty:n,ctx:r}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Hl(r){let t,e,s;return t=new Qe({props:{keybinding:r[10].acceptFocusedChunk}}),{c(){I(t.$$.fragment),e=gt(`
        Accept`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};1024&i&&(o.keybinding=n[10].acceptFocusedChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function Wl(r){let t,e,s;return t=new Qe({props:{keybinding:r[10].rejectFocusedChunk}}),{c(){I(t.$$.fragment),e=gt(`
      Reject`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};1024&i&&(o.keybinding=n[10].rejectFocusedChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function Gl(r){let t,e,s,n,i,o,a,l,h,c,d=!r[3]&&kr(r);return a=new Ae({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Wl]},$$scope:{ctx:r}}}),a.$on("click",function(){me(r[5])&&r[5].apply(this,arguments)}),{c(){t=et("div"),s=K(),n=et("div"),i=et("div"),d&&d.c(),o=K(),I(a.$$.fragment),Y(t,"class","svelte-zm1705"),Wt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),Y(i,"class","c-button-container svelte-zm1705"),Wt(i,"c-button-container--focused",r[1]),Wt(i,"c-button-container--transparent",r[9]),Y(n,"class","c-chunk-action-panel-anchor svelte-zm1705"),rs(n,"top",r[8]+"px"),Wt(n,"c-chunk-action-panel-anchor--left",r[0]==="left"),Wt(n,"c-chunk-action-panel-anchor--right",r[0]==="right"),Wt(n,"c-chunk-action-panel-anchor--focused",r[1])},m(f,g){O(f,t,g),O(f,s,g),O(f,n,g),st(n,i),d&&d.m(i,null),st(i,o),E(a,i,null),l=!0,h||(c=[Yr(e=r[6].renderActionsViewZone(t,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]})),Ee(n,"mouseenter",r[13]),Ee(n,"mousemove",r[13]),Ee(n,"mouseleave",r[13])],h=!0)},p(f,[g]){r=f,e&&me(e.update)&&132&g&&e.update.call(null,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]}),(!l||130&g)&&Wt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),r[3]?d&&($t(),v(d,1,1,()=>{d=null}),xt()):d?(d.p(r,g),8&g&&$(d,1)):(d=kr(r),d.c(),$(d,1),d.m(i,o));const x={};132096&g&&(x.$$scope={dirty:g,ctx:r}),a.$set(x),(!l||2&g)&&Wt(i,"c-button-container--focused",r[1]),(!l||512&g)&&Wt(i,"c-button-container--transparent",r[9]),(!l||256&g)&&rs(n,"top",r[8]+"px"),(!l||1&g)&&Wt(n,"c-chunk-action-panel-anchor--left",r[0]==="left"),(!l||1&g)&&Wt(n,"c-chunk-action-panel-anchor--right",r[0]==="right"),(!l||2&g)&&Wt(n,"c-chunk-action-panel-anchor--focused",r[1])},i(f){l||($(d),$(a.$$.fragment,f),l=!0)},o(f){v(d),v(a.$$.fragment,f),l=!1},d(f){f&&(R(t),R(s),R(n)),d&&d.d(),A(a),h=!1,fi(c)}}}function Vl(r,t,e){let s,{align:n="right"}=t,{isFocused:i}=t,{heightInPx:o=1}=t,{disableApply:a=!1}=t,{onAccept:l}=t,{onReject:h}=t,{diffViewModel:c}=t,{leaf:d}=t;const f=c.keybindings;We(r,f,_=>e(10,s=_));let g=0,x,w=!1;function k(){x&&(clearTimeout(x),x=void 0),e(9,w=!1)}return r.$$set=_=>{"align"in _&&e(0,n=_.align),"isFocused"in _&&e(1,i=_.isFocused),"heightInPx"in _&&e(2,o=_.heightInPx),"disableApply"in _&&e(3,a=_.disableApply),"onAccept"in _&&e(4,l=_.onAccept),"onReject"in _&&e(5,h=_.onReject),"diffViewModel"in _&&e(6,c=_.diffViewModel),"leaf"in _&&e(7,d=_.leaf)},[n,i,o,a,l,h,c,d,g,w,s,f,_=>{e(8,g=_)},function(_){_.target.closest(".c-button-container")?k():_.type==="mouseenter"||_.type==="mousemove"?(k(),x=setTimeout(()=>{e(9,w=!0)},400)):_.type==="mouseleave"&&k()}]}class Bl extends Ve{constructor(t){super(),Be(this,t,Vl,Gl,Ze,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function Cr(r){let t,e,s;function n(o){r[18](o)}let i={onOpenChange:r[16],content:r[3],triggerOn:[Wa.Hover],$$slots:{default:[Ql]},$$scope:{ctx:r}};return r[4]!==void 0&&(i.requestClose=r[4]),t=new Fa({props:i}),os.push(()=>oa(t,"requestClose",n)),{c(){I(t.$$.fragment)},m(o,a){E(t,o,a),s=!0},p(o,a){const l={};8&a&&(l.content=o[3]),1048576&a&&(l.$$scope={dirty:a,ctx:o}),!e&&16&a&&(e=!0,l.requestClose=o[4],aa(()=>e=!1)),t.$set(l)},i(o){s||($(t.$$.fragment,o),s=!0)},o(o){v(t.$$.fragment,o),s=!1},d(o){A(t,o)}}}function Zl(r){let t,e;return t=new Sa({}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Ql(r){let t,e;return t=new ga({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Zl]},$$scope:{ctx:r}}}),t.$on("click",r[17]),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1048576&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Yl(r){let t;return{c(){t=et("span"),t.textContent="No changes",Y(t,"class","c-diff-page-counter svelte-4zjwll")},m(e,s){O(e,t,s)},p:tt,i:tt,o:tt,d(e){e&&R(t)}}}function Kl(r){var h,c;let t,e,s,n,i,o,a,l=((c=(h=r[1])==null?void 0:h.leaves)==null?void 0:c.length)+"";return o=new Kr({props:{size:1,loading:r[10]}}),{c(){t=et("span"),e=gt(r[2]),s=gt(" of "),n=gt(l),i=K(),I(o.$$.fragment),Y(t,"class","c-diff-page-counter svelte-4zjwll")},m(d,f){O(d,t,f),st(t,e),st(t,s),st(t,n),st(t,i),E(o,t,null),a=!0},p(d,f){var x,w;(!a||4&f)&&ne(e,d[2]),(!a||2&f)&&l!==(l=((w=(x=d[1])==null?void 0:x.leaves)==null?void 0:w.length)+"")&&ne(n,l);const g={};1024&f&&(g.loading=d[10]),o.$set(g)},i(d){a||($(o.$$.fragment,d),a=!0)},o(d){v(o.$$.fragment,d),a=!1},d(d){d&&R(t),A(o)}}}function Xl(r){let t,e,s,n;return s=new Kr({props:{size:1,loading:r[10]}}),{c(){t=et("span"),e=gt(`Generating changes
        `),I(s.$$.fragment),Y(t,"class","c-diff-page-counter svelte-4zjwll")},m(i,o){O(i,t,o),st(t,e),E(s,t,null),n=!0},p(i,o){const a={};1024&o&&(a.loading=i[10]),s.$set(a)},i(i){n||($(s.$$.fragment,i),n=!0)},o(i){v(s.$$.fragment,i),n=!1},d(i){i&&R(t),A(s)}}}function Sr(r){let t,e,s,n,i,o;t=new Ae({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Jl]},$$scope:{ctx:r}}}),t.$on("click",function(){me(r[0].focusPrevChunk)&&r[0].focusPrevChunk.apply(this,arguments)}),s=new Ae({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[tc]},$$scope:{ctx:r}}}),s.$on("click",function(){me(r[0].focusNextChunk)&&r[0].focusNextChunk.apply(this,arguments)});let a=!r[12]&&Mr(r);return{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment),n=K(),a&&a.c(),i=ce()},m(l,h){E(t,l,h),O(l,e,h),E(s,l,h),O(l,n,h),a&&a.m(l,h),O(l,i,h),o=!0},p(l,h){r=l;const c={};1050624&h&&(c.$$scope={dirty:h,ctx:r}),t.$set(c);const d={};1050624&h&&(d.$$scope={dirty:h,ctx:r}),s.$set(d),r[12]?a&&($t(),v(a,1,1,()=>{a=null}),xt()):a?(a.p(r,h),4096&h&&$(a,1)):(a=Mr(r),a.c(),$(a,1),a.m(i.parentNode,i))},i(l){o||($(t.$$.fragment,l),$(s.$$.fragment,l),$(a),o=!0)},o(l){v(t.$$.fragment,l),v(s.$$.fragment,l),v(a),o=!1},d(l){l&&(R(e),R(n),R(i)),A(t,l),A(s,l),a&&a.d(l)}}}function Jl(r){let t,e,s;return t=new Qe({props:{keybinding:r[11].focusPrevChunk}}),{c(){I(t.$$.fragment),e=gt(`
        Back`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].focusPrevChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function tc(r){let t,e,s;return t=new Qe({props:{keybinding:r[11].focusNextChunk}}),{c(){I(t.$$.fragment),e=gt(`
        Next`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].focusNextChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function Mr(r){let t,e,s,n=!r[13]&&Ir(r);return e=new Ae({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[sc]},$$scope:{ctx:r}}}),e.$on("click",function(){me(r[0].rejectAllChunks)&&r[0].rejectAllChunks.apply(this,arguments)}),{c(){n&&n.c(),t=K(),I(e.$$.fragment)},m(i,o){n&&n.m(i,o),O(i,t,o),E(e,i,o),s=!0},p(i,o){(r=i)[13]?n&&($t(),v(n,1,1,()=>{n=null}),xt()):n?(n.p(r,o),8192&o&&$(n,1)):(n=Ir(r),n.c(),$(n,1),n.m(t.parentNode,t));const a={};1050624&o&&(a.$$scope={dirty:o,ctx:r}),e.$set(a)},i(i){s||($(n),$(e.$$.fragment,i),s=!0)},o(i){v(n),v(e.$$.fragment,i),s=!1},d(i){i&&R(t),n&&n.d(i),A(e,i)}}}function Ir(r){let t,e;return t=new Ae({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[ec]},$$scope:{ctx:r}}}),t.$on("click",function(){me(r[0].acceptAllChunks)&&r[0].acceptAllChunks.apply(this,arguments)}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){r=s;const i={};1050624&n&&(i.$$scope={dirty:n,ctx:r}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function ec(r){let t,e,s;return t=new Qe({props:{keybinding:r[11].acceptAllChunks}}),{c(){I(t.$$.fragment),e=gt(`
            Accept All`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].acceptAllChunks),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function sc(r){let t,e,s;return t=new Qe({props:{keybinding:r[11].rejectAllChunks}}),{c(){I(t.$$.fragment),e=gt(`
          Reject All`)},m(n,i){E(t,n,i),O(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].rejectAllChunks),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){v(t.$$.fragment,n),s=!1},d(n){n&&R(e),A(t,n)}}}function nc(r){let t,e,s,n,i,o,a,l=r[9]&&Cr(r);const h=[Xl,Kl,Yl],c=[];function d(g,x){return!g[5]&&g[10]?0:g[5]?1:2}n=d(r),i=c[n]=h[n](r);let f=r[5]&&Sr(r);return{c(){t=et("div"),e=et("div"),l&&l.c(),s=K(),i.c(),o=K(),f&&f.c(),Y(e,"class","c-button-container svelte-4zjwll"),Y(t,"class","c-top-action-panel-anchor svelte-4zjwll")},m(g,x){O(g,t,x),st(t,e),l&&l.m(e,null),st(e,s),c[n].m(e,null),st(e,o),f&&f.m(e,null),a=!0},p(g,[x]){g[9]?l?(l.p(g,x),512&x&&$(l,1)):(l=Cr(g),l.c(),$(l,1),l.m(e,s)):l&&($t(),v(l,1,1,()=>{l=null}),xt());let w=n;n=d(g),n===w?c[n].p(g,x):($t(),v(c[w],1,1,()=>{c[w]=null}),xt(),i=c[n],i?i.p(g,x):(i=c[n]=h[n](g),i.c()),$(i,1),i.m(e,o)),g[5]?f?(f.p(g,x),32&x&&$(f,1)):(f=Sr(g),f.c(),$(f,1),f.m(e,null)):f&&($t(),v(f,1,1,()=>{f=null}),xt())},i(g){a||($(l),$(i),$(f),a=!0)},o(g){v(l),v(i),v(f),a=!1},d(g){g&&R(t),l&&l.d(),c[n].d(),f&&f.d()}}}function ic(r,t,e){let s,n,i,o,a,l,h,c,d,f,g=tt,x=()=>(g(),g=ge(y,U=>e(1,l=U)),y),w=tt,k=tt,_=tt;r.$$.on_destroy.push(()=>g()),r.$$.on_destroy.push(()=>w()),r.$$.on_destroy.push(()=>k()),r.$$.on_destroy.push(()=>_());let{diffViewModel:y}=t;x();const C=y.keybindings;We(r,C,U=>e(11,c=U));const F=y.requestId;We(r,F,U=>e(9,a=U));let N,ot="x",j="Copy request ID",V=()=>{};return r.$$set=U=>{"diffViewModel"in U&&x(e(0,y=U.diffViewModel))},r.$$.update=()=>{var U;2&r.$$.dirty&&(e(8,s=l.disableResolution),k(),k=ge(s,vt=>e(12,d=vt))),2&r.$$.dirty&&(e(7,n=l.disableApply),_(),_=ge(n,vt=>e(13,f=vt))),2&r.$$.dirty&&(l.currFocusedChunkIdx!==void 0?e(2,ot=(l.currFocusedChunkIdx+1).toString()):e(2,ot="x")),2&r.$$.dirty&&(e(6,i=l.isLoading),w(),w=ge(i,vt=>e(10,h=vt))),2&r.$$.dirty&&e(5,o=!!((U=l.leaves)!=null&&U.length))},[y,l,ot,j,V,o,i,n,s,a,h,c,d,f,C,F,function(U){U||(clearTimeout(N),N=void 0,e(3,j="Copy request ID"))},async function(){a&&(await navigator.clipboard.writeText(a),e(3,j="Copied!"),clearTimeout(N),N=setTimeout(V,1500))},function(U){V=U,e(4,V)}]}class rc extends Ve{constructor(t){super(),Be(this,t,ic,nc,Ze,{diffViewModel:0})}}var ko="Expected a function",Er=NaN,oc="[object Symbol]",ac=/^\s+|\s+$/g,lc=/^[-+]0x[0-9a-f]+$/i,cc=/^0b[01]+$/i,hc=/^0o[0-7]+$/i,uc=parseInt,dc=typeof Vt=="object"&&Vt&&Vt.Object===Object&&Vt,fc=typeof self=="object"&&self&&self.Object===Object&&self,pc=dc||fc||Function("return this")(),gc=Object.prototype.toString,mc=Math.max,_c=Math.min,Un=function(){return pc.Date.now()};function yc(r,t,e){var s,n,i,o,a,l,h=0,c=!1,d=!1,f=!0;if(typeof r!="function")throw new TypeError(ko);function g(y){var C=s,F=n;return s=n=void 0,h=y,o=r.apply(F,C)}function x(y){var C=y-l;return l===void 0||C>=t||C<0||d&&y-h>=i}function w(){var y=Un();if(x(y))return k(y);a=setTimeout(w,function(C){var F=t-(C-l);return d?_c(F,i-(C-h)):F}(y))}function k(y){return a=void 0,f&&s?g(y):(s=n=void 0,o)}function _(){var y=Un(),C=x(y);if(s=arguments,n=this,l=y,C){if(a===void 0)return function(F){return h=F,a=setTimeout(w,t),c?g(F):o}(l);if(d)return a=setTimeout(w,t),g(l)}return a===void 0&&(a=setTimeout(w,t)),o}return t=Ar(t)||0,bn(e)&&(c=!!e.leading,i=(d="maxWait"in e)?mc(Ar(e.maxWait)||0,t):i,f="trailing"in e?!!e.trailing:f),_.cancel=function(){a!==void 0&&clearTimeout(a),h=0,s=l=n=a=void 0},_.flush=function(){return a===void 0?o:k(Un())},_}function bn(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function Ar(r){if(typeof r=="number")return r;if(function(s){return typeof s=="symbol"||function(n){return!!n&&typeof n=="object"}(s)&&gc.call(s)==oc}(r))return Er;if(bn(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=bn(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=r.replace(ac,"");var e=cc.test(r);return e||hc.test(r)?uc(r.slice(2),e?2:8):lc.test(r)?Er:+r}const bc=di(function(r,t,e){var s=!0,n=!0;if(typeof r!="function")throw new TypeError(ko);return bn(e)&&(s="leading"in e?!!e.leading:s,n="trailing"in e?!!e.trailing:n),yc(r,t,{leading:s,maxWait:t,trailing:n})});function _e(r){return Array.isArray?Array.isArray(r):Mo(r)==="[object Array]"}const $c=1/0;function xc(r){return r==null?"":function(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-$c?"-0":e}(r)}function le(r){return typeof r=="string"}function Co(r){return typeof r=="number"}function vc(r){return r===!0||r===!1||function(t){return So(t)&&t!==null}(r)&&Mo(r)=="[object Boolean]"}function So(r){return typeof r=="object"}function Ut(r){return r!=null}function jn(r){return!r.trim().length}function Mo(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}const wc=r=>`Missing ${r} property in key`,kc=r=>`Property 'weight' in key '${r}' must be a positive integer`,Tr=Object.prototype.hasOwnProperty;class Cc{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach(s=>{let n=Io(s);this._keys.push(n),this._keyMap[n.id]=n,e+=n.weight}),this._keys.forEach(s=>{s.weight/=e})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Io(r){let t=null,e=null,s=null,n=1,i=null;if(le(r)||_e(r))s=r,t=Fr(r),e=ai(r);else{if(!Tr.call(r,"name"))throw new Error(wc("name"));const o=r.name;if(s=o,Tr.call(r,"weight")&&(n=r.weight,n<=0))throw new Error(kc(o));t=Fr(o),e=ai(o),i=r.getFn}return{path:t,id:e,weight:n,src:s,getFn:i}}function Fr(r){return _e(r)?r:r.split(".")}function ai(r){return _e(r)?r.join("."):r}const Sc={useExtendedSearch:!1,getFn:function(r,t){let e=[],s=!1;const n=(i,o,a)=>{if(Ut(i))if(o[a]){const l=i[o[a]];if(!Ut(l))return;if(a===o.length-1&&(le(l)||Co(l)||vc(l)))e.push(xc(l));else if(_e(l)){s=!0;for(let h=0,c=l.length;h<c;h+=1)n(l[h],o,a+1)}else o.length&&n(l,o,a+1)}else e.push(i)};return n(r,le(t)?t.split("."):t,0),s?e:e[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var z={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(r,t)=>r.score===t.score?r.idx<t.idx?-1:1:r.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...Sc};const Mc=/[^ ]+/g;class Ci{constructor({getFn:t=z.getFn,fieldNormWeight:e=z.fieldNormWeight}={}){this.norm=function(s=1,n=3){const i=new Map,o=Math.pow(10,n);return{get(a){const l=a.match(Mc).length;if(i.has(l))return i.get(l);const h=1/Math.pow(l,.5*s),c=parseFloat(Math.round(h*o)/o);return i.set(l,c),c},clear(){i.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((e,s)=>{this._keysMap[e.id]=s})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,le(this.docs[0])?this.docs.forEach((t,e)=>{this._addString(t,e)}):this.docs.forEach((t,e)=>{this._addObject(t,e)}),this.norm.clear())}add(t){const e=this.size();le(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,s=this.size();e<s;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!Ut(t)||jn(t))return;let s={v:t,i:e,n:this.norm.get(t)};this.records.push(s)}_addObject(t,e){let s={i:e,$:{}};this.keys.forEach((n,i)=>{let o=n.getFn?n.getFn(t):this.getFn(t,n.path);if(Ut(o)){if(_e(o)){let a=[];const l=[{nestedArrIndex:-1,value:o}];for(;l.length;){const{nestedArrIndex:h,value:c}=l.pop();if(Ut(c))if(le(c)&&!jn(c)){let d={v:c,i:h,n:this.norm.get(c)};a.push(d)}else _e(c)&&c.forEach((d,f)=>{l.push({nestedArrIndex:f,value:d})})}s.$[i]=a}else if(le(o)&&!jn(o)){let a={v:o,n:this.norm.get(o)};s.$[i]=a}}}),this.records.push(s)}toJSON(){return{keys:this.keys,records:this.records}}}function Eo(r,t,{getFn:e=z.getFn,fieldNormWeight:s=z.fieldNormWeight}={}){const n=new Ci({getFn:e,fieldNormWeight:s});return n.setKeys(r.map(Io)),n.setSources(t),n.create(),n}function sn(r,{errors:t=0,currentLocation:e=0,expectedLocation:s=0,distance:n=z.distance,ignoreLocation:i=z.ignoreLocation}={}){const o=t/r.length;if(i)return o;const a=Math.abs(s-e);return n?o+a/n:a?1:o}const Pe=32;function Ic(r,t,e,{location:s=z.location,distance:n=z.distance,threshold:i=z.threshold,findAllMatches:o=z.findAllMatches,minMatchCharLength:a=z.minMatchCharLength,includeMatches:l=z.includeMatches,ignoreLocation:h=z.ignoreLocation}={}){if(t.length>Pe)throw new Error(`Pattern length exceeds max of ${Pe}.`);const c=t.length,d=r.length,f=Math.max(0,Math.min(s,d));let g=i,x=f;const w=a>1||l,k=w?Array(d):[];let _;for(;(_=r.indexOf(t,x))>-1;){let j=sn(t,{currentLocation:_,expectedLocation:f,distance:n,ignoreLocation:h});if(g=Math.min(j,g),x=_+c,w){let V=0;for(;V<c;)k[_+V]=1,V+=1}}x=-1;let y=[],C=1,F=c+d;const N=1<<c-1;for(let j=0;j<c;j+=1){let V=0,U=F;for(;V<U;)sn(t,{errors:j,currentLocation:f+U,expectedLocation:f,distance:n,ignoreLocation:h})<=g?V=U:F=U,U=Math.floor((F-V)/2+V);F=U;let vt=Math.max(1,f-U+1),dt=o?d:Math.min(f+U,d)+c,It=Array(dt+2);It[dt+1]=(1<<j)-1;for(let B=dt;B>=vt;B-=1){let H=B-1,jt=e[r.charAt(H)];if(w&&(k[H]=+!!jt),It[B]=(It[B+1]<<1|1)&jt,j&&(It[B]|=(y[B+1]|y[B])<<1|1|y[B+1]),It[B]&N&&(C=sn(t,{errors:j,currentLocation:H,expectedLocation:f,distance:n,ignoreLocation:h}),C<=g)){if(g=C,x=H,x<=f)break;vt=Math.max(1,2*f-x)}}if(sn(t,{errors:j+1,currentLocation:f,expectedLocation:f,distance:n,ignoreLocation:h})>g)break;y=It}const ot={isMatch:x>=0,score:Math.max(.001,C)};if(w){const j=function(V=[],U=z.minMatchCharLength){let vt=[],dt=-1,It=-1,B=0;for(let H=V.length;B<H;B+=1){let jt=V[B];jt&&dt===-1?dt=B:jt||dt===-1||(It=B-1,It-dt+1>=U&&vt.push([dt,It]),dt=-1)}return V[B-1]&&B-dt>=U&&vt.push([dt,B-1]),vt}(k,a);j.length?l&&(ot.indices=j):ot.isMatch=!1}return ot}function Ec(r){let t={};for(let e=0,s=r.length;e<s;e+=1){const n=r.charAt(e);t[n]=(t[n]||0)|1<<s-e-1}return t}class Ao{constructor(t,{location:e=z.location,threshold:s=z.threshold,distance:n=z.distance,includeMatches:i=z.includeMatches,findAllMatches:o=z.findAllMatches,minMatchCharLength:a=z.minMatchCharLength,isCaseSensitive:l=z.isCaseSensitive,ignoreLocation:h=z.ignoreLocation}={}){if(this.options={location:e,threshold:s,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:h},this.pattern=l?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const c=(f,g)=>{this.chunks.push({pattern:f,alphabet:Ec(f),startIndex:g})},d=this.pattern.length;if(d>Pe){let f=0;const g=d%Pe,x=d-g;for(;f<x;)c(this.pattern.substr(f,Pe),f),f+=Pe;if(g){const w=d-Pe;c(this.pattern.substr(w),w)}}else c(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,includeMatches:s}=this.options;if(e||(t=t.toLowerCase()),this.pattern===t){let x={isMatch:!0,score:0};return s&&(x.indices=[[0,t.length-1]]),x}const{location:n,distance:i,threshold:o,findAllMatches:a,minMatchCharLength:l,ignoreLocation:h}=this.options;let c=[],d=0,f=!1;this.chunks.forEach(({pattern:x,alphabet:w,startIndex:k})=>{const{isMatch:_,score:y,indices:C}=Ic(t,x,w,{location:n+k,distance:i,threshold:o,findAllMatches:a,minMatchCharLength:l,includeMatches:s,ignoreLocation:h});_&&(f=!0),d+=y,_&&C&&(c=[...c,...C])});let g={isMatch:f,score:f?d/this.chunks.length:1};return f&&s&&(g.indices=c),g}}class Se{constructor(t){this.pattern=t}static isMultiMatch(t){return Or(t,this.multiRegex)}static isSingleMatch(t){return Or(t,this.singleRegex)}search(){}}function Or(r,t){const e=r.match(t);return e?e[1]:null}class To extends Se{constructor(t,{location:e=z.location,threshold:s=z.threshold,distance:n=z.distance,includeMatches:i=z.includeMatches,findAllMatches:o=z.findAllMatches,minMatchCharLength:a=z.minMatchCharLength,isCaseSensitive:l=z.isCaseSensitive,ignoreLocation:h=z.ignoreLocation}={}){super(t),this._bitapSearch=new Ao(t,{location:e,threshold:s,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class Fo extends Se{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,s=0;const n=[],i=this.pattern.length;for(;(e=t.indexOf(this.pattern,s))>-1;)s=e+i,n.push([e,s-1]);const o=!!n.length;return{isMatch:o,score:o?0:1,indices:n}}}const li=[class extends Se{constructor(r){super(r)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(r){const t=r===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},Fo,class extends Se{constructor(r){super(r)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(r){const t=r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(r){const t=!r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(r){const t=!r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(r){const t=r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[r.length-this.pattern.length,r.length-1]}}},class extends Se{constructor(r){super(r)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(r){const t=r.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},To],Rr=li.length,Ac=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Tc=new Set([To.type,Fo.type]);class Fc{constructor(t,{isCaseSensitive:e=z.isCaseSensitive,includeMatches:s=z.includeMatches,minMatchCharLength:n=z.minMatchCharLength,ignoreLocation:i=z.ignoreLocation,findAllMatches:o=z.findAllMatches,location:a=z.location,threshold:l=z.threshold,distance:h=z.distance}={}){this.query=null,this.options={isCaseSensitive:e,includeMatches:s,minMatchCharLength:n,findAllMatches:o,ignoreLocation:i,location:a,threshold:l,distance:h},this.pattern=e?t:t.toLowerCase(),this.query=function(c,d={}){return c.split("|").map(f=>{let g=f.trim().split(Ac).filter(w=>w&&!!w.trim()),x=[];for(let w=0,k=g.length;w<k;w+=1){const _=g[w];let y=!1,C=-1;for(;!y&&++C<Rr;){const F=li[C];let N=F.isMultiMatch(_);N&&(x.push(new F(N,d)),y=!0)}if(!y)for(C=-1;++C<Rr;){const F=li[C];let N=F.isSingleMatch(_);if(N){x.push(new F(N,d));break}}}return x})}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:s,isCaseSensitive:n}=this.options;t=n?t:t.toLowerCase();let i=0,o=[],a=0;for(let l=0,h=e.length;l<h;l+=1){const c=e[l];o.length=0,i=0;for(let d=0,f=c.length;d<f;d+=1){const g=c[d],{isMatch:x,indices:w,score:k}=g.search(t);if(!x){a=0,i=0,o.length=0;break}if(i+=1,a+=k,s){const _=g.constructor.type;Tc.has(_)?o=[...o,...w]:o.push(w)}}if(i){let d={isMatch:!0,score:a/i};return s&&(d.indices=o),d}}return{isMatch:!1,score:1}}}const ci=[];function hi(r,t){for(let e=0,s=ci.length;e<s;e+=1){let n=ci[e];if(n.condition(r,t))return new n(r,t)}return new Ao(r,t)}const Si="$and",Oc="$or",Lr="$path",Rc="$val",Hn=r=>!(!r[Si]&&!r[Oc]),Nr=r=>({[Si]:Object.keys(r).map(t=>({[t]:r[t]}))});function Oo(r,t,{auto:e=!0}={}){const s=n=>{let i=Object.keys(n);const o=(l=>!!l[Lr])(n);if(!o&&i.length>1&&!Hn(n))return s(Nr(n));if((l=>!_e(l)&&So(l)&&!Hn(l))(n)){const l=o?n[Lr]:i[0],h=o?n[Rc]:n[l];if(!le(h))throw new Error((d=>`Invalid value for key ${d}`)(l));const c={keyId:ai(l),pattern:h};return e&&(c.searcher=hi(h,t)),c}let a={children:[],operator:i[0]};return i.forEach(l=>{const h=n[l];_e(h)&&h.forEach(c=>{a.children.push(s(c))})}),a};return Hn(r)||(r=Nr(r)),s(r)}function Lc(r,t){const e=r.matches;t.matches=[],Ut(e)&&e.forEach(s=>{if(!Ut(s.indices)||!s.indices.length)return;const{indices:n,value:i}=s;let o={indices:n,value:i};s.key&&(o.key=s.key.src),s.idx>-1&&(o.refIndex=s.idx),t.matches.push(o)})}function Nc(r,t){t.score=r.score}class is{constructor(t,e={},s){this.options={...z,...e},this.options.useExtendedSearch,this._keyStore=new Cc(this.options.keys),this.setCollection(t,s)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof Ci))throw new Error("Incorrect 'index' type");this._myIndex=e||Eo(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){Ut(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const e=[];for(let s=0,n=this._docs.length;s<n;s+=1){const i=this._docs[s];t(i,s)&&(this.removeAt(s),s-=1,n-=1,e.push(i))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:s,includeScore:n,shouldSort:i,sortFn:o,ignoreFieldNorm:a}=this.options;let l=le(t)?le(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(h,{ignoreFieldNorm:c=z.ignoreFieldNorm}){h.forEach(d=>{let f=1;d.matches.forEach(({key:g,norm:x,score:w})=>{const k=g?g.weight:null;f*=Math.pow(w===0&&k?Number.EPSILON:w,(k||1)*(c?1:x))}),d.score=f})}(l,{ignoreFieldNorm:a}),i&&l.sort(o),Co(e)&&e>-1&&(l=l.slice(0,e)),function(h,c,{includeMatches:d=z.includeMatches,includeScore:f=z.includeScore}={}){const g=[];return d&&g.push(Lc),f&&g.push(Nc),h.map(x=>{const{idx:w}=x,k={item:c[w],refIndex:w};return g.length&&g.forEach(_=>{_(x,k)}),k})}(l,this._docs,{includeMatches:s,includeScore:n})}_searchStringList(t){const e=hi(t,this.options),{records:s}=this._myIndex,n=[];return s.forEach(({v:i,i:o,n:a})=>{if(!Ut(i))return;const{isMatch:l,score:h,indices:c}=e.searchIn(i);l&&n.push({item:i,idx:o,matches:[{score:h,value:i,norm:a,indices:c}]})}),n}_searchLogical(t){const e=Oo(t,this.options),s=(a,l,h)=>{if(!a.children){const{keyId:d,searcher:f}=a,g=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(l,d),searcher:f});return g&&g.length?[{idx:h,item:l,matches:g}]:[]}const c=[];for(let d=0,f=a.children.length;d<f;d+=1){const g=a.children[d],x=s(g,l,h);if(x.length)c.push(...x);else if(a.operator===Si)return[]}return c},n=this._myIndex.records,i={},o=[];return n.forEach(({$:a,i:l})=>{if(Ut(a)){let h=s(e,a,l);h.length&&(i[l]||(i[l]={idx:l,item:a,matches:[]},o.push(i[l])),h.forEach(({matches:c})=>{i[l].matches.push(...c)}))}}),o}_searchObjectList(t){const e=hi(t,this.options),{keys:s,records:n}=this._myIndex,i=[];return n.forEach(({$:o,i:a})=>{if(!Ut(o))return;let l=[];s.forEach((h,c)=>{l.push(...this._findMatches({key:h,value:o[c],searcher:e}))}),l.length&&i.push({idx:a,item:o,matches:l})}),i}_findMatches({key:t,value:e,searcher:s}){if(!Ut(e))return[];let n=[];if(_e(e))e.forEach(({v:i,i:o,n:a})=>{if(!Ut(i))return;const{isMatch:l,score:h,indices:c}=s.searchIn(i);l&&n.push({score:h,key:t,value:i,idx:o,norm:a,indices:c})});else{const{v:i,n:o}=e,{isMatch:a,score:l,indices:h}=s.searchIn(i);a&&n.push({score:l,key:t,value:i,norm:o,indices:h})}return n}}is.version="7.0.0",is.createIndex=Eo,is.parseIndex=function(r,{getFn:t=z.getFn,fieldNormWeight:e=z.fieldNormWeight}={}){const{keys:s,records:n}=r,i=new Ci({getFn:t,fieldNormWeight:e});return i.setKeys(s),i.setIndexRecords(n),i},is.config=z,is.parseQuery=Oo,function(...r){ci.push(...r)}(Fc);const Me=class Me{constructor(t,e){u(this,"_disposers",[]);u(this,"_allMentionables",ut([]));u(this,"_breadcrumbIds",ut([]));u(this,"_userQuery",ut(""));u(this,"_active",ut(!1));u(this,"_allGroups",Rn([this._active,this._allMentionables],([t,e])=>t?wa(e):[]));u(this,"_currentGroup",Rn([this._breadcrumbIds,this._allGroups],([t,e])=>{if(t.length===0)return;const s=t[t.length-1];return e.find(n=>Ts(n)&&n.id===s)}));u(this,"dispose",()=>{for(const t of this._disposers)t()});u(this,"openDropdown",()=>{this._active.set(!0)});u(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});u(this,"toggleDropdown",()=>ht(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));u(this,"pushBreadcrumb",t=>{ht(this._active)&&this._breadcrumbIds.update(e=>[...e,t.id])});u(this,"popBreadcrumb",()=>{ht(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});u(this,"selectMentionable",t=>{var n;const e=this._chatModel.extensionClient,s=this._chatModel.specialContextInputModel;return Ts(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):to(t)?(s.markAllActive(),this.closeDropdown(),e.reportWebviewClientEvent(Vn.chatRestoreDefaultContext),!0):t.clearContext?(s.markAllInactive(),this.closeDropdown(),e.reportWebviewClientEvent(Vn.chatClearContext),!0):t.userGuidelines?(e.openSettingsPage("guidelines"),this.closeDropdown(),!0):((n=this._insertMentionNode)==null||n.call(this,t),this.closeDropdown(),!0)});u(this,"_displayItems",Rn([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,e,s,n,i])=>{if(!t)return[];if(e.length>0&&n)return[{...n,type:"breadcrumb-back"},...n.group.items.slice(0,Me.SINGLE_GROUP_MAX_ITEMS).map(o=>({...o,type:"item"}))];if(s.length>0){const o=Dc(ht(this._userQuery)).map(a=>({...a,type:"item"}));return i.flatMap(a=>[{...a,type:"breadcrumb"},...a.group.items.slice(0,Me.MULTI_GROUP_MAX_ITEMS).map(l=>({...l,type:"item"}))]).concat(o)}return[{...eo,type:"item"},...i.map(o=>({...o,type:"breadcrumb"})),{...so,type:"item"},{...no,type:"item"}]}));u(this,"_refreshSeqNum",0);u(this,"_refreshMentionables",bc(async()=>{if(!ht(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,e=this._chatModel.currentConversationModel&&ls(this._chatModel.currentConversationModel),s=ht(this._userQuery),n=await this._chatModel.extensionClient.getSuggestions(s,e);t===this._refreshSeqNum&&this._allMentionables.set(Ro({query:s,mentionables:n}))},Me.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=e,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};u(Me,"REFRESH_THROTTLE_MS",600),u(Me,"SINGLE_GROUP_MAX_ITEMS",12),u(Me,"MULTI_GROUP_MAX_ITEMS",6);let ui=Me;const Ro=({query:r,mentionables:t,returnAllIfNoResults:e=!0,threshold:s=1})=>{if(r.length<=1)return t;const n=new is(t,{keys:["label"],threshold:s,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(r);return n.length===0&&e?t:n.map(i=>i.item)},Dc=r=>Ro({query:r,mentionables:[eo,so,no],returnAllIfNoResults:!1,threshold:.6});function $n(r){switch(r){case Ct.DEFAULT:return ir;case Ct.PROTOTYPER:return La;case Ct.BRAINSTORM:return Ra;case Ct.REVIEWER:return Oa;default:return ir}}function zc(r){let t,e,s,n=r[0].label+"";return{c(){t=et("span"),e=et("span"),s=gt(n),Y(e,"class","c-mentionable-group-label__text right"),Y(t,"class","c-mentionable-group-label")},m(i,o){O(i,t,o),st(t,e),st(e,s)},p(i,o){1&o&&n!==(n=i[0].label+"")&&ne(s,n)},i:tt,o:tt,d(i){i&&R(t)}}}function qc(r){let t,e;return t=new Na({props:{$$slots:{text:[Qc],leftIcon:[Zc]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};17&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Pc(r){let t,e=r[0].label+"";return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&ne(t,e)},i:tt,o:tt,d(s){s&&R(t)}}}function Uc(r){let t,e;return t=new Ot({props:{filepath:r[0].rule.path,$$slots:{leftIcon:[Yc]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].rule.path),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function jc(r){let t,e;return t=new Ot({props:{filepath:r[0].recentFile.pathName,$$slots:{leftIcon:[Kc]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].recentFile.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Hc(r){let t,e;return t=new Ot({props:{filepath:r[0].selection.pathName,$$slots:{leftIcon:[Xc]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].selection.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Wc(r){let t,e;return t=new Ot({props:{filepath:r[0].sourceFolder.folderRoot,$$slots:{leftIcon:[Jc]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].sourceFolder.folderRoot),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Gc(r){let t,e;return t=new Ot({props:{filepath:r[0].externalSource.name,$$slots:{leftIcon:[th]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].externalSource.name),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Vc(r){let t,e;return t=new Ot({props:{filepath:r[0].folder.pathName,$$slots:{leftIcon:[eh]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].folder.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Bc(r){let t,e;return t=new Ot({props:{filepath:r[0].file.pathName,$$slots:{leftIcon:[sh]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].file.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Zc(r){let t,e,s;var n=$n(r[0].personality.type);return n&&(e=fs(n,{})),{c(){t=et("span"),e&&I(e.$$.fragment),Y(t,"slot","leftIcon"),Y(t,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(i,o){O(i,t,o),e&&E(e,t,null),s=!0},p(i,o){if(1&o&&n!==(n=$n(i[0].personality.type))){if(e){$t();const a=e;v(a.$$.fragment,1,0,()=>{A(a,1)}),xt()}n?(e=fs(n,{}),I(e.$$.fragment),$(e.$$.fragment,1),E(e,t,null)):e=null}},i(i){s||(e&&$(e.$$.fragment,i),s=!0)},o(i){e&&v(e.$$.fragment,i),s=!1},d(i){i&&R(t),e&&A(e)}}}function Qc(r){let t,e,s=r[0].label+"";return{c(){t=et("span"),e=gt(s),Y(t,"slot","text")},m(n,i){O(n,t,i),st(t,e)},p(n,i){1&i&&s!==(s=n[0].label+"")&&ne(e,s)},d(n){n&&R(t)}}}function Yc(r){let t,e;return t=new ao({props:{slot:"leftIcon",iconName:"rule"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Kc(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"description"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Xc(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Jc(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function th(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function eh(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function sh(r){let t,e;return t=new ps({props:{slot:"leftIcon",iconName:"description"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function nh(r){let t,e,s,n,i,o,a,l,h,c,d,f,g,x;const w=[Bc,Vc,Gc,Wc,Hc,jc,Uc,Pc,qc,zc],k=[];function _(y,C){return 1&C&&(t=null),1&C&&(e=null),1&C&&(s=null),1&C&&(n=null),1&C&&(i=null),1&C&&(o=null),1&C&&(a=null),1&C&&(l=null),1&C&&(h=null),1&C&&(c=null),t==null&&(t=!!gi(y[0])),t?0:(e==null&&(e=!!mi(y[0])),e?1:(s==null&&(s=!!_i(y[0])),s?2:(n==null&&(n=!!dn(y[0])),n?3:(i==null&&(i=!!un(y[0])),i?4:(o==null&&(o=!!hn(y[0])),o?5:(a==null&&(a=!!pn(y[0])),a?6:(l==null&&(l=!!Ts(y[0])),l?7:(h==null&&(h=!!xn(y[0])),h?8:(c==null&&(c=!!(to(y[0])||ka(y[0])||fn(y[0]))),c?9:-1)))))))))}return~(d=_(r,-1))&&(f=k[d]=w[d](r)),{c(){f&&f.c(),g=ce()},m(y,C){~d&&k[d].m(y,C),O(y,g,C),x=!0},p(y,C){let F=d;d=_(y,C),d===F?~d&&k[d].p(y,C):(f&&($t(),v(k[F],1,1,()=>{k[F]=null}),xt()),~d?(f=k[d],f?f.p(y,C):(f=k[d]=w[d](y),f.c()),$(f,1),f.m(g.parentNode,g)):f=null)},i(y){x||($(f),x=!0)},o(y){v(f),x=!1},d(y){y&&R(g),~d&&k[d].d(y)}}}function ih(r){let t,e,s;var n=r[3];function i(o,a){return{props:{highlight:o[2],onSelect:o[1],$$slots:{default:[nh]},$$scope:{ctx:o}}}}return n&&(t=fs(n,i(r))),{c(){t&&I(t.$$.fragment),e=ce()},m(o,a){t&&E(t,o,a),O(o,e,a),s=!0},p(o,[a]){if(8&a&&n!==(n=o[3])){if(t){$t();const l=t;v(l.$$.fragment,1,0,()=>{A(l,1)}),xt()}n?(t=fs(n,i(o)),I(t.$$.fragment),$(t.$$.fragment,1),E(t,e.parentNode,e)):t=null}else if(n){const l={};4&a&&(l.highlight=o[2]),2&a&&(l.onSelect=o[1]),17&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)}},i(o){s||(t&&$(t.$$.fragment,o),s=!0)},o(o){t&&v(t.$$.fragment,o),s=!1},d(o){o&&R(e),t&&A(t,o)}}}function rh(r,t,e){let s,{item:n}=t,{onSelect:i}=t,{highlight:o}=t;return r.$$set=a=>{"item"in a&&e(0,n=a.item),"onSelect"in a&&e(1,i=a.onSelect),"highlight"in a&&e(2,o=a.highlight)},r.$$.update=()=>{1&r.$$.dirty&&(n.type==="breadcrumb-back"?e(3,s=zn.BreadcrumbBackItem):n.type==="breadcrumb"&&Ts(n)?e(3,s=zn.BreadcrumbItem):n.type!=="item"||Ts(n)||e(3,s=zn.Item))},[n,i,o,s]}class oh extends Ve{constructor(t){super(),Be(this,t,rh,ih,Ze,{item:0,onSelect:1,highlight:2})}}function ah(r){let t,e=r[0].label+"";return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&ne(t,e)},i:tt,o:tt,d(s){s&&R(t)}}}function lh(r){let t,e,s,n;return t=new ao({}),s=new Ot({props:{filepath:`${sr}/${nr}/${r[0].rule.path}`}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=`${sr}/${nr}/${i[0].rule.path}`),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function ch(r){let t,e,s,n,i,o,a,l;return e=new Da({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[_h]},$$scope:{ctx:r}}}),i=new ln({props:{size:2,weight:"medium",$$slots:{default:[yh]},$$scope:{ctx:r}}}),a=new ln({props:{size:1,$$slots:{default:[bh]},$$scope:{ctx:r}}}),{c(){t=et("div"),I(e.$$.fragment),s=K(),n=et("div"),I(i.$$.fragment),o=K(),I(a.$$.fragment),Y(t,"class","c-mention-hover-contents__personality-icon svelte-11069rs"),Y(n,"class","c-mention-hover-contents__personality svelte-11069rs")},m(h,c){O(h,t,c),E(e,t,null),O(h,s,c),O(h,n,c),E(i,n,null),st(n,o),E(a,n,null),l=!0},p(h,c){const d={};3&c&&(d.$$scope={dirty:c,ctx:h}),e.$set(d);const f={};3&c&&(f.$$scope={dirty:c,ctx:h}),i.$set(f);const g={};3&c&&(g.$$scope={dirty:c,ctx:h}),a.$set(g)},i(h){l||($(e.$$.fragment,h),$(i.$$.fragment,h),$(a.$$.fragment,h),l=!0)},o(h){v(e.$$.fragment,h),v(i.$$.fragment,h),v(a.$$.fragment,h),l=!1},d(h){h&&(R(t),R(s),R(n)),A(e),A(i),A(a)}}}function hh(r){var i,o;let t,e,s,n;return t=new za({}),s=new Ot({props:{filepath:`${r[0].selection.pathName}:L${(i=r[0].selection.fullRange)==null?void 0:i.startLineNumber}-${(o=r[0].selection.fullRange)==null?void 0:o.endLineNumber}`}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(a,l){E(t,a,l),O(a,e,l),E(s,a,l),n=!0},p(a,l){var c,d;const h={};1&l&&(h.filepath=`${a[0].selection.pathName}:L${(c=a[0].selection.fullRange)==null?void 0:c.startLineNumber}-${(d=a[0].selection.fullRange)==null?void 0:d.endLineNumber}`),s.$set(h)},i(a){n||($(t.$$.fragment,a),$(s.$$.fragment,a),n=!0)},o(a){v(t.$$.fragment,a),v(s.$$.fragment,a),n=!1},d(a){a&&R(e),A(t,a),A(s,a)}}}function uh(r){var n;let t,e,s=(r[0].userGuidelines.overLimit||((n=r[0].rulesAndGuidelinesState)==null?void 0:n.overLimit))&&Dr(r);return{c(){s&&s.c(),t=ce()},m(i,o){s&&s.m(i,o),O(i,t,o),e=!0},p(i,o){var a;i[0].userGuidelines.overLimit||(a=i[0].rulesAndGuidelinesState)!=null&&a.overLimit?s?(s.p(i,o),1&o&&$(s,1)):(s=Dr(i),s.c(),$(s,1),s.m(t.parentNode,t)):s&&($t(),v(s,1,1,()=>{s=null}),xt())},i(i){e||($(s),e=!0)},o(i){v(s),e=!1},d(i){i&&R(t),s&&s.d(i)}}}function dh(r){let t,e,s,n,i,o,a,l;return s=new qa({}),i=new Ot({props:{class:"c-source-folder-item",filepath:r[0].sourceFolder.folderRoot}}),a=new Pa({props:{class:"guidelines-filespan",sourceFolder:r[0].sourceFolder}}),{c(){t=et("div"),e=et("div"),I(s.$$.fragment),n=K(),I(i.$$.fragment),o=K(),I(a.$$.fragment),Y(e,"class","l-source-folder-name svelte-11069rs"),Y(t,"class","l-mention-hover-contents__source-folder")},m(h,c){O(h,t,c),st(t,e),E(s,e,null),st(e,n),E(i,e,null),st(t,o),E(a,t,null),l=!0},p(h,c){const d={};1&c&&(d.filepath=h[0].sourceFolder.folderRoot),i.$set(d);const f={};1&c&&(f.sourceFolder=h[0].sourceFolder),a.$set(f)},i(h){l||($(s.$$.fragment,h),$(i.$$.fragment,h),$(a.$$.fragment,h),l=!0)},o(h){v(s.$$.fragment,h),v(i.$$.fragment,h),v(a.$$.fragment,h),l=!1},d(h){h&&R(t),A(s),A(i),A(a)}}}function fh(r){let t,e,s,n;return t=new Ua({}),s=new Ot({props:{filepath:r[0].externalSource.name}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].externalSource.name),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function ph(r){let t,e,s,n;return t=new Ma({}),s=new Ot({props:{filepath:r[0].folder.pathName}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].folder.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function gh(r){let t,e,s,n;return t=new ro({}),s=new Ot({props:{filepath:r[0].recentFile.pathName}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].recentFile.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function mh(r){let t,e,s,n;return t=new ro({}),s=new Ot({props:{filepath:r[0].file.pathName}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].file.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function _h(r){let t,e,s;var n=$n(r[0].personality.type);return n&&(t=fs(n,{})),{c(){t&&I(t.$$.fragment),e=ce()},m(i,o){t&&E(t,i,o),O(i,e,o),s=!0},p(i,o){if(1&o&&n!==(n=$n(i[0].personality.type))){if(t){$t();const a=t;v(a.$$.fragment,1,0,()=>{A(a,1)}),xt()}n?(t=fs(n,{}),I(t.$$.fragment),$(t.$$.fragment,1),E(t,e.parentNode,e)):t=null}},i(i){s||(t&&$(t.$$.fragment,i),s=!0)},o(i){t&&v(t.$$.fragment,i),s=!1},d(i){i&&R(e),t&&A(t,i)}}}function yh(r){let t,e=r[0].label+"";return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&ne(t,e)},d(s){s&&R(t)}}}function bh(r){let t,e=r[0].personality.description+"";return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=s[0].personality.description+"")&&ne(t,e)},d(s){s&&R(t)}}}function Dr(r){let t,e,s,n;const i=[xh,$h],o=[];function a(l,h){var c;return(c=l[0].rulesAndGuidelinesState)!=null&&c.overLimit?0:l[0].userGuidelines.overLimit?1:-1}return~(t=a(r))&&(e=o[t]=i[t](r)),{c(){e&&e.c(),s=ce()},m(l,h){~t&&o[t].m(l,h),O(l,s,h),n=!0},p(l,h){let c=t;t=a(l),t===c?~t&&o[t].p(l,h):(e&&($t(),v(o[c],1,1,()=>{o[c]=null}),xt()),~t?(e=o[t],e?e.p(l,h):(e=o[t]=i[t](l),e.c()),$(e,1),e.m(s.parentNode,s)):e=null)},i(l){n||($(e),n=!0)},o(l){v(e),n=!1},d(l){l&&R(s),~t&&o[t].d(l)}}}function $h(r){let t,e;return t=new ln({props:{size:1,$$slots:{default:[vh]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};3&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function xh(r){let t,e;return t=new ln({props:{size:1,$$slots:{default:[wh]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};3&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function vh(r){let t,e=`Guidelines exceeded length limit of ${r[0].userGuidelines.lengthLimit} characters`;return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=`Guidelines exceeded length limit of ${s[0].userGuidelines.lengthLimit} characters`)&&ne(t,e)},d(s){s&&R(t)}}}function wh(r){let t,e=`Rules and workspace guidelines (${r[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${r[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`;return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){1&n&&e!==(e=`Rules and workspace guidelines (${s[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${s[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`)&&ne(t,e)},d(s){s&&R(t)}}}function kh(r){let t,e,s,n,i,o,a,l,h,c,d,f,g;const x=[mh,gh,ph,fh,dh,uh,hh,ch,lh,ah],w=[];function k(_,y){return 1&y&&(e=null),1&y&&(s=null),1&y&&(n=null),1&y&&(i=null),1&y&&(o=null),1&y&&(a=null),1&y&&(l=null),1&y&&(h=null),1&y&&(c=null),e==null&&(e=!(!_[0]||!gi(_[0]))),e?0:(s==null&&(s=!(!_[0]||!hn(_[0]))),s?1:(n==null&&(n=!(!_[0]||!mi(_[0]))),n?2:(i==null&&(i=!(!_[0]||!_i(_[0]))),i?3:(o==null&&(o=!(!_[0]||!dn(_[0]))),o?4:(a==null&&(a=!!(_[0]&&fn(_[0])&&_[0].userGuidelines.enabled)),a?5:(l==null&&(l=!(!_[0]||!un(_[0]))),l?6:(h==null&&(h=!(!_[0]||!xn(_[0]))),h?7:(c==null&&(c=!(!_[0]||!pn(_[0]))),c?8:9))))))))}return d=k(r,-1),f=w[d]=x[d](r),{c(){t=et("div"),f.c(),Y(t,"class","c-mention-hover-contents svelte-11069rs")},m(_,y){O(_,t,y),w[d].m(t,null),g=!0},p(_,[y]){let C=d;d=k(_,y),d===C?w[d].p(_,y):($t(),v(w[C],1,1,()=>{w[C]=null}),xt(),f=w[d],f?f.p(_,y):(f=w[d]=x[d](_),f.c()),$(f,1),f.m(t,null))},i(_){g||($(f),g=!0)},o(_){v(f),g=!1},d(_){_&&R(t),w[d].d()}}}function Ch(r,t,e){let{option:s}=t;return r.$$set=n=>{"option"in n&&e(0,s=n.option)},[s]}class Sh extends Ve{constructor(t){super(),Be(this,t,Ch,kh,Ze,{option:0})}}function zr(r,t,e){const s=r.slice();return s[15]=t[e],s}function qr(r){let t,e;function s(){return r[8](r[15])}return t=new oh({props:{item:r[15],highlight:r[15]===r[14],onSelect:s}}),{c(){I(t.$$.fragment)},m(n,i){E(t,n,i),e=!0},p(n,i){r=n;const o={};4&i&&(o.item=r[15]),16388&i&&(o.highlight=r[15]===r[14]),4&i&&(o.onSelect=s),t.$set(o)},i(n){e||($(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){A(t,n)}}}function Mh(r){let t,e,s=cn(r[2]),n=[];for(let o=0;o<s.length;o+=1)n[o]=qr(zr(r,s,o));const i=o=>v(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=ce()},m(o,a){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,a);O(o,t,a),e=!0},p(o,a){if(16420&a){let l;for(s=cn(o[2]),l=0;l<s.length;l+=1){const h=zr(o,s,l);n[l]?(n[l].p(h,a),$(n[l],1)):(n[l]=qr(h),n[l].c(),$(n[l],1),n[l].m(t.parentNode,t))}for($t(),l=s.length;l<n.length;l+=1)i(l);xt()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)v(n[a]);e=!1},d(o){o&&R(t),Xr(n,o)}}}function Ih(r){let t,e;return t=new Sh({props:{slot:"mentionable",option:r[13]}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p(s,n){const i={};8192&n&&(i.option=s[13]),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Eh(r){let t,e,s,n;return t=new Bn.Menu.Root({props:{mentionables:r[2],onQueryUpdate:r[4],onSelectMentionable:r[5],$$slots:{default:[Mh,({activeItem:i})=>({14:i}),({activeItem:i})=>i?16384:0]},$$scope:{ctx:r}}}),s=new Bn.ChipTooltip({props:{$$slots:{mentionable:[Ih,({mentionable:i})=>({13:i}),({mentionable:i})=>i?8192:0]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment)},m(i,o){E(t,i,o),O(i,e,o),E(s,i,o),n=!0},p(i,o){const a={};4&o&&(a.mentionables=i[2]),278532&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a);const l={};270336&o&&(l.$$scope={dirty:o,ctx:i}),s.$set(l)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){v(t.$$.fragment,i),v(s.$$.fragment,i),n=!1},d(i){i&&R(e),A(t,i),A(s,i)}}}function Ah(r){let t,e,s={triggerCharacter:"@",onMentionItemsUpdated:r[0],$$slots:{default:[Eh]},$$scope:{ctx:r}};return t=new Bn.Root({props:s}),r[9](t),{c(){I(t.$$.fragment)},m(n,i){E(t,n,i),e=!0},p(n,[i]){const o={};1&i&&(o.onMentionItemsUpdated=n[0]),262148&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||($(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){r[9](null),A(t,n)}}}function Th(r,t,e){let s,{requestEditorFocus:n}=t,{onMentionItemsUpdated:i}=t;const o=la("chatModel");if(!o)throw new Error("ChatModel not found in context");const a=new ui(o,c),l=a.displayItems;let h;function c(f){return!!h&&(h.insertMention(f),a.closeDropdown(),!0)}function d(f){const g=a.selectMentionable(f);return n(),g}return We(r,l,f=>e(2,s=f)),pi(()=>{a.dispose()}),r.$$set=f=>{"requestEditorFocus"in f&&e(6,n=f.requestEditorFocus),"onMentionItemsUpdated"in f&&e(0,i=f.onMentionItemsUpdated)},[i,h,s,l,function(f){f===void 0?a.closeDropdown():(a.openDropdown(),a.userQuery.set(f))},d,n,f=>c(f),f=>d(f),function(f){os[f?"unshift":"push"](()=>{h=f,e(1,h)})}]}class Fh extends Ve{constructor(t){super(),Be(this,t,Th,Ah,Ze,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function Pr(r){let t,e,s,n,i,o,a,l,h,c,d,f,g,x,w={focusOnInit:!0,$$slots:{default:[Oh]},$$scope:{ctx:r}};return o=new lo.Root({props:w}),r[25](o),h=new Ae({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[Rh]},$$scope:{ctx:r}}}),h.$on("click",function(){me(r[0].disposeDiffViewPanel)&&r[0].disposeDiffViewPanel.apply(this,arguments)}),d=new Ae({props:{id:"send",size:1,variant:"solid",color:"accent",title:r[3]===Te.instruction?"Instruct Augment":"Edit with Augment",disabled:!r[4].trim()||r[11],$$slots:{iconRight:[Nh],default:[Lh]},$$scope:{ctx:r}}}),d.$on("click",r[14]),{c(){t=et("div"),e=K(),s=et("div"),n=et("div"),i=et("div"),I(o.$$.fragment),a=K(),l=et("div"),I(h.$$.fragment),c=K(),I(d.$$.fragment),Y(i,"class","l-input-area__input svelte-1cxscce"),Y(l,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),Y(n,"class","instruction-drawer-panel__contents svelte-1cxscce"),Y(n,"tabindex","0"),Y(n,"role","button"),Y(s,"class","instruction-drawer-panel svelte-1cxscce"),rs(s,"top",r[5]+"px"),rs(s,"height",r[6]+"px")},m(k,_){O(k,t,_),O(k,e,_),O(k,s,_),st(s,n),st(n,i),E(o,i,null),r[26](i),st(n,a),st(n,l),E(h,l,null),st(l,c),E(d,l,null),f=!0,g||(x=[Yr(r[15].call(null,t)),Ee(n,"click",r[17]),Ee(n,"keydown",r[27])],g=!0)},p(k,_){r=k;const y={};1296&_[0]|256&_[1]&&(y.$$scope={dirty:_,ctx:r}),o.$set(y);const C={};256&_[1]&&(C.$$scope={dirty:_,ctx:r}),h.$set(C);const F={};8&_[0]&&(F.title=r[3]===Te.instruction?"Instruct Augment":"Edit with Augment"),2064&_[0]&&(F.disabled=!r[4].trim()||r[11]),8&_[0]|256&_[1]&&(F.$$scope={dirty:_,ctx:r}),d.$set(F),(!f||32&_[0])&&rs(s,"top",r[5]+"px"),(!f||64&_[0])&&rs(s,"height",r[6]+"px")},i(k){f||($(o.$$.fragment,k),$(h.$$.fragment,k),$(d.$$.fragment,k),f=!0)},o(k){v(o.$$.fragment,k),v(h.$$.fragment,k),v(d.$$.fragment,k),f=!1},d(k){k&&(R(t),R(e),R(s)),r[25](null),A(o),r[26](null),A(h),A(d),g=!1,fi(x)}}}function Oh(r){let t,e,s,n,i,o,a,l;t=new ja({props:{shortcuts:{Enter:r[23]}}});let h={requestEditorFocus:r[16],onMentionItemsUpdated:r[18]};return s=new Fh({props:h}),r[24](s),i=new lo.Content({props:{content:r[4],onContentChanged:r[19]}}),a=new Ha({props:{placeholder:r[10]}}),{c(){I(t.$$.fragment),e=K(),I(s.$$.fragment),n=K(),I(i.$$.fragment),o=K(),I(a.$$.fragment)},m(c,d){E(t,c,d),O(c,e,d),E(s,c,d),O(c,n,d),E(i,c,d),O(c,o,d),E(a,c,d),l=!0},p(c,d){s.$set({});const f={};16&d[0]&&(f.content=c[4]),i.$set(f);const g={};1024&d[0]&&(g.placeholder=c[10]),a.$set(g)},i(c){l||($(t.$$.fragment,c),$(s.$$.fragment,c),$(i.$$.fragment,c),$(a.$$.fragment,c),l=!0)},o(c){v(t.$$.fragment,c),v(s.$$.fragment,c),v(i.$$.fragment,c),v(a.$$.fragment,c),l=!1},d(c){c&&(R(e),R(n),R(o)),A(t,c),r[24](null),A(s,c),A(i,c),A(a,c)}}}function Rh(r){let t,e,s;return e=new Qe({props:{keybinding:"esc"}}),{c(){t=gt(`Close
          `),I(e.$$.fragment)},m(n,i){O(n,t,i),E(e,n,i),s=!0},p:tt,i(n){s||($(e.$$.fragment,n),s=!0)},o(n){v(e.$$.fragment,n),s=!1},d(n){n&&R(t),A(e,n)}}}function Lh(r){let t,e=r[3]===Te.instruction?"Instruct":"Edit";return{c(){t=gt(e)},m(s,n){O(s,t,n)},p(s,n){8&n[0]&&e!==(e=s[3]===Te.instruction?"Instruct":"Edit")&&ne(t,e)},d(s){s&&R(t)}}}function Nh(r){let t,e;return t=new Ia({props:{slot:"iconRight"}}),{c(){I(t.$$.fragment)},m(s,n){E(t,s,n),e=!0},p:tt,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){v(t.$$.fragment,s),e=!1},d(s){A(t,s)}}}function Dh(r){let t,e,s=r[2]&&Pr(r);return{c(){s&&s.c(),t=ce()},m(n,i){s&&s.m(n,i),O(n,t,i),e=!0},p(n,i){n[2]?s?(s.p(n,i),4&i[0]&&$(s,1)):(s=Pr(n),s.c(),$(s,1),s.m(t.parentNode,t)):s&&($t(),v(s,1,1,()=>{s=null}),xt())},i(n){e||($(s),e=!0)},o(n){v(s),e=!1},d(n){n&&R(t),s&&s.d(n)}}}function zh(r,t,e){let s,n,i,o,a,l,h=tt,c=()=>(h(),h=ge(f,q=>e(22,o=q)),f),d=tt;r.$$.on_destroy.push(()=>h()),r.$$.on_destroy.push(()=>d());let{diffViewModel:f}=t;c();let{initialConversation:g}=t,{initialFlags:x}=t;const w=Ga.getContext().monaco,k={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},_=new oo(Ms);let y=new wo;_.registerConsumer(y);let C=new ki(_,Ms,y,{initialConversation:g,initialFlags:x});const F=C.currentConversationModel;let N,ot;_.registerConsumer(C),function(q){ca("chatModel",q)}(C);let j,V="";const U=f.mode;We(r,U,q=>e(3,a=q));const vt=f.selectionLines;function dt(){const q=f.getModifiedEditor(),Oe=ht(w);if(!q||!Oe||(j==null||j.clear(),!i))return;const Ke=i.start,gs=i.end,Ns={range:new Oe.Range(Ke+1,1,gs+1,1),options:k};j||(j=q.createDecorationsCollection()),j.set([Ns])}function It(){return!!(V!=null&&V.trim())&&(f.handleInstructionSubmit(V),!0)}We(r,vt,q=>e(2,i=q)),Jr(async()=>{await Gn(),Rt(),e(5,jt=f.editorOffset)}),pi(()=>{N==null||N.destroy(),j==null||j.clear()});let B,H,jt=0,Fe=57;const Rt=()=>B==null?void 0:B.forceFocus();return r.$$set=q=>{"diffViewModel"in q&&c(e(0,f=q.diffViewModel)),"initialConversation"in q&&e(20,g=q.initialConversation),"initialFlags"in q&&e(21,x=q.initialFlags)},r.$$.update=()=>{if(8&r.$$.dirty[0]&&e(10,s=(a===Te.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&r.$$.dirty[0]&&(e(9,n=o.isLoading),d(),d=ge(n,q=>e(11,l=q))),6&r.$$.dirty[0]&&ot){if(i==null)e(6,Fe=0);else{const q=ot.scrollHeight;e(6,Fe=Math.min(40+q,108))}N==null||N.update({heightInPx:Fe}),dt()}},[f,ot,i,a,V,jt,Fe,B,H,n,s,l,U,vt,It,function(q){if(q){const Oe=i?i.start:1;N=f.renderInstructionsDrawerViewZone(q,{line:Oe,heightInPx:Fe,onDomNodeTop:Ke=>{e(5,jt=f.editorOffset+Ke)},autoFocus:!0}),dt()}},()=>B==null?void 0:B.requestFocus(),Rt,q=>{F.saveDraftMentions(q.current)},function(q){e(4,V=q.rawText)},g,x,o,()=>It(),function(q){os[q?"unshift":"push"](()=>{H=q,e(8,H)})},function(q){os[q?"unshift":"push"](()=>{B=q,e(7,B)})},function(q){os[q?"unshift":"push"](()=>{ot=q,e(1,ot)})},q=>{q.key==="Enter"&&(Rt(),q.stopPropagation(),q.preventDefault())}]}class qh extends Ve{constructor(t){super(),Be(this,t,zh,Dh,Ze,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Wn}=da;function Ur(r,t,e){const s=r.slice();return s[17]=t[e],s[19]=e,s}function jr(r){let t,e,s,n,i;return e=new rc({props:{diffViewModel:r[3]}}),n=new qh({props:{diffViewModel:r[3]}}),{c(){t=et("div"),I(e.$$.fragment),s=K(),I(n.$$.fragment),Y(t,"class","sticky-top svelte-453n6i")},m(o,a){O(o,t,a),E(e,t,null),O(o,s,a),E(n,o,a),i=!0},p(o,a){const l={};8&a&&(l.diffViewModel=o[3]),e.$set(l);const h={};8&a&&(h.diffViewModel=o[3]),n.$set(h)},i(o){i||($(e.$$.fragment,o),$(n.$$.fragment,o),i=!0)},o(o){v(e.$$.fragment,o),v(n.$$.fragment,o),i=!1},d(o){o&&(R(t),R(s)),A(e),A(n,o)}}}function Hr(r){let t,e,s=cn(r[4]),n=[];for(let o=0;o<s.length;o+=1)n[o]=Gr(Ur(r,s,o));const i=o=>v(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=ce()},m(o,a){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,a);O(o,t,a),e=!0},p(o,a){if(280&a){let l;for(s=cn(o[4]),l=0;l<s.length;l+=1){const h=Ur(o,s,l);n[l]?(n[l].p(h,a),$(n[l],1)):(n[l]=Gr(h),n[l].c(),$(n[l],1),n[l].m(t.parentNode,t))}for($t(),l=s.length;l<n.length;l+=1)i(l);xt()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)v(n[a]);e=!1},d(o){o&&R(t),Xr(n,o)}}}function Wr(r){var i;let t,e;function s(){return r[14](r[17])}function n(){return r[15](r[17])}return t=new Bl({props:{isFocused:((i=r[3])==null?void 0:i.currFocusedChunkIdx)===r[19],onAccept:s,onReject:n,diffViewModel:r[3],leaf:r[17],align:"right",disableApply:r[8]}}),{c(){I(t.$$.fragment)},m(o,a){E(t,o,a),e=!0},p(o,a){var h;r=o;const l={};8&a&&(l.isFocused=((h=r[3])==null?void 0:h.currFocusedChunkIdx)===r[19]),24&a&&(l.onAccept=s),24&a&&(l.onReject=n),8&a&&(l.diffViewModel=r[3]),16&a&&(l.leaf=r[17]),256&a&&(l.disableApply=r[8]),t.$set(l)},i(o){e||($(t.$$.fragment,o),e=!0)},o(o){v(t.$$.fragment,o),e=!1},d(o){A(t,o)}}}function Gr(r){let t,e,s=r[17].unitOfCodeWork.modifiedCode!==r[17].unitOfCodeWork.originalCode&&Wr(r);return{c(){s&&s.c(),t=ce()},m(n,i){s&&s.m(n,i),O(n,t,i),e=!0},p(n,i){n[17].unitOfCodeWork.modifiedCode!==n[17].unitOfCodeWork.originalCode?s?(s.p(n,i),16&i&&$(s,1)):(s=Wr(n),s.c(),$(s,1),s.m(t.parentNode,t)):s&&($t(),v(s,1,1,()=>{s=null}),xt())},i(n){e||($(s),e=!0)},o(n){v(s),e=!1},d(n){n&&R(t),s&&s.d(n)}}}function Ph(r){var h;let t,e,s,n,i,o,a=r[3]&&jr(r),l=r[3]&&((h=r[4])==null?void 0:h.length)&&!r[7]&&Hr(r);return{c(){t=et("div"),a&&a.c(),e=K(),s=et("div"),n=et("div"),i=K(),l&&l.c(),Y(n,"class","editor svelte-453n6i"),Y(s,"class","editor-container svelte-453n6i"),Y(t,"class","diff-view-container svelte-453n6i")},m(c,d){O(c,t,d),a&&a.m(t,null),st(t,e),st(t,s),st(s,n),r[13](n),st(s,i),l&&l.m(s,null),o=!0},p(c,d){var f;c[3]?a?(a.p(c,d),8&d&&$(a,1)):(a=jr(c),a.c(),$(a,1),a.m(t,e)):a&&($t(),v(a,1,1,()=>{a=null}),xt()),c[3]&&((f=c[4])!=null&&f.length)&&!c[7]?l?(l.p(c,d),152&d&&$(l,1)):(l=Hr(c),l.c(),$(l,1),l.m(s,null)):l&&($t(),v(l,1,1,()=>{l=null}),xt())},i(c){o||($(a),$(l),o=!0)},o(c){v(a),v(l),o=!1},d(c){c&&R(t),a&&a.d(),r[13](null),l&&l.d()}}}function Uh(r){let t,e,s,n;return t=new Va.Root({props:{$$slots:{default:[Ph]},$$scope:{ctx:r}}}),{c(){I(t.$$.fragment)},m(i,o){E(t,i,o),e=!0,s||(n=[Ee(Wn,"message",function(){var a,l;me((a=r[0])==null?void 0:a.handleMessageFromExtension)&&((l=r[0])==null||l.handleMessageFromExtension.apply(this,arguments))}),Ee(Wn,"focus",r[11]),Ee(Wn,"blur",r[12])],s=!0)},p(i,[o]){r=i;const a={};1048986&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(i){e||($(t.$$.fragment,i),e=!0)},o(i){v(t.$$.fragment,i),e=!1},d(i){A(t,i),s=!1,fi(n)}}}function jh(r,t,e){let s,n,i,o,a,l,h,c,d,f,g=tt,x=tt,w=tt;function k(y){const C=ua.dark;return Ba((y==null?void 0:y.category)||C,y==null?void 0:y.intensity)??Za.get(C)}We(r,ha,y=>e(10,a=y)),r.$$.on_destroy.push(()=>g()),r.$$.on_destroy.push(()=>x()),r.$$.on_destroy.push(()=>w()),Jr(async()=>{e(9,f=await window.augmentDeps.monaco),f||console.error("Monaco not loaded. Diff view cannot be initialized.")}),pi(()=>{c==null||c.dispose()});let _=!1;return r.$$.update=()=>{if(1539&r.$$.dirty&&f&&d&&!c&&(e(0,c=new Ul(d,k(a),f)),g(),g=ge(c,y=>e(3,o=y))),1&r.$$.dirty&&(e(6,s=c==null?void 0:c.disableApply),w(),w=ge(s,y=>e(8,h=y))),1&r.$$.dirty&&(e(5,n=c==null?void 0:c.disableResolution),x(),x=ge(n,y=>e(7,l=y))),1025&r.$$.dirty){const y=a;c&&(c==null||c.updateTheme(k(y)))}8&r.$$.dirty&&e(4,i=o==null?void 0:o.leaves),5&r.$$.dirty&&(c==null||c.updateIsWebviewFocused(_))},[c,d,_,o,i,n,s,l,h,f,a,()=>e(2,_=!0),()=>e(2,_=!1),function(y){os[y?"unshift":"push"](()=>{d=y,e(1,d)})},y=>o==null?void 0:o.acceptChunk(y),y=>o==null?void 0:o.rejectChunk(y)]}new class extends Ve{constructor(r){super(),Be(this,r,jh,Uh,Ze,{})}}({target:document.getElementById("app")});
