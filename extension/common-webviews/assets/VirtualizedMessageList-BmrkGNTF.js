var se=Object.defineProperty;var re=(r,t,n)=>t in r?se(r,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[t]=n;var C=(r,t,n)=>re(r,typeof t!="symbol"?t+"":t,n);import{N as K,ak as T,al as q,am as Et,S as Gt,i as Ft,s as Ht,$ as oe,W as N,c as A,ac as Mt,Y as W,e as B,a0 as ie,a1 as ae,a2 as le,u as h,t as f,h as z,E as w,J as H,F as y,f as O,a8 as Lt,q as V,r as P,I as M,aa as Nt,ag as bt,ah as F,a5 as ce,a6 as me,a9 as mt,ad as ut,n as R,B as ct,C as ue,A as U}from"./SpinnerAugment-CQKp6jSN.js";import{e as tt,u as jt,o as Wt}from"./BaseButton-ESlFPUk1.js";import{l as de,g as ge,t as pe,a as he,b as fe,m as $e,n as _e,A as Se,M as Ie,R as Ut,c as xe,S as we,d as ye,G as Me,P as Le,e as be,f as Ce,C as Be,h as ze,U as ve,i as Yt,E as qe,j as Re,k as De}from"./RemoteAgentRetry-B9aPolhZ.js";import"./Content-D7Q35t53.js";import{g as dt,a as Jt,h as Ot,b as Qt,c as Xt,d as Zt,e as Kt,f as te,j as ee,k as gt,l as ke,S as pt,i as Ct,E as Te}from"./arrow-up-right-from-square-ChzPb9WB.js";import"./folder-D9ce_3EI.js";import{aI as Ve,aq as Pe,ar as Ae}from"./AugmentMessage-DZ4G-ao3.js";import{S as Ee}from"./main-panel-7MFJkt6m.js";import"./isObjectLike-BYlJR0wA.js";import{R as Ge}from"./check-DlU29TPV.js";import"./types-DDm27S8B.js";import"./MaterialIcon-CprIOK2c.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-C4SxYn1J.js";import"./Keybindings-CmEJIsef.js";import"./CalloutAugment-ZPisEIAt.js";import"./exclamation-triangle-CEPjk4z2.js";import"./CardAugment-KjDsYzQv.js";import"./TextTooltipAugment--NM_J2iY.js";import"./IconButtonAugment-D-fvrWAT.js";import"./index-DvKVcjj3.js";import"./pen-to-square-CIi2Dx1U.js";import"./augment-logo-DhUYorpN.js";import"./ButtonAugment-CAn8LxGl.js";import"./folder-opened-Bb7oiaOR.js";import"./expand-BOHHm5_f.js";import"./diff-utils-Dvc7ppQm.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-csTQmXPq.js";import"./trash-can-YBGqp3xH.js";import"./CollapseButtonAugment--cD032dy.js";import"./github-CwfQWdpa.js";import"./index-CMtlLYew.js";import"./utils-C8gPzElB.js";import"./chat-types-DOHETl9Q.js";import"./globals-D0QH3NT1.js";import"./types-DwxhLPcD.js";import"./file-paths-BcSg4gks.js";import"./types-CGlLNakm.js";import"./TextAreaAugment-b9B2aQlO.js";import"./await_block-p2Uc7RoJ.js";import"./CopyButton-BT3AalMT.js";import"./magnifying-glass-CXSTSDWT.js";import"./ellipsis-DA5Ek1es.js";import"./IconFilePath-QXTl3raU.js";import"./LanguageIcon-CBQUAmpN.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-BI7GEaM4.js";import"./lodash-8faY21Ia.js";import"./mcp-logo-DrfFqzDb.js";import"./terminal-CB1sTE6C.js";import"./design-system-init-D-hN7xfd.js";import"./StatusIndicator-CVSFyrPR.js";import"./ra-diff-ops-model-CRIDwIDf.js";import"./VSCodeCodicon-CONIBlZ6.js";import"./chat-flags-model-BhsWla-l.js";class Fe{constructor(t=10){C(this,"samples",[]);C(this,"maxSamples");this.maxSamples=t}addSample(t,n=performance.now()){this.samples.push({position:t,timestamp:n}),this.samples.length>this.maxSamples&&this.samples.shift()}getVelocity(){if(this.samples.length<2)return 0;const t=this.samples.at(-1),n=this.samples.at(0),e=t.position-n.position,s=t.timestamp-n.timestamp;return s>0?e/s:0}getVelocityPPS(){return 1e3*this.getVelocity()}isFastScroll(t=500){return Math.abs(this.getVelocityPPS())>t}getDirection(){const t=this.getVelocity();return t>.05?"down":t<-.05?"up":"static"}predictPositionAfter(t){if(this.samples.length===0)return 0;const n=this.getVelocity();return this.samples[this.samples.length-1].position+n*t}reset(){this.samples=[]}getCurrentPosition(){return this.samples.length===0?0:this.samples[this.samples.length-1].position}}class He{captureScrollPosition(t){const{scrollHeight:n,scrollTop:e,clientHeight:s}=t;return{scrollHeight:n,scrollTop:e,clientHeight:s,distanceFromBottom:de(t),timestamp:performance.now()}}restoreScrollPosition(t,n){const e=t.scrollHeight-n.distanceFromBottom-t.clientHeight;t.scrollTop=Math.max(0,e)}}class Ne{constructor(t,n={initialVisibleCount:20,batchSize:10}){C(this,"_config");C(this,"_disposables",[]);C(this,"_currentBatchSize");C(this,"_startIndex");C(this,"_endIndex");C(this,"_isLoading");C(this,"_loadingDirection");C(this,"_allItems");C(this,"_groupedAllItems");C(this,"_groupedVisibleItems");C(this,"hasMoreBefore");C(this,"hasMoreAfter");C(this,"isLoading");C(this,"loadingDirection");C(this,"totalItemCount");this._conversationModel=t,this._config={...n,minBatchSize:n.minBatchSize??n.batchSize,maxBatchSize:n.maxBatchSize??3*n.batchSize},this._currentBatchSize=this._config.batchSize,this._startIndex=K(0),this._endIndex=K(void 0),this._isLoading=K(!1),this._loadingDirection=K(null),this._allItems=T(this._conversationModel,s=>s.chatHistory.filter(o=>dt(o)||Jt(o)||Ot(o)||Qt(o)||Xt(o)||Zt(o)||Kt(o)||te(o)||ee(o)||gt(o))),this._groupedAllItems=T(this._allItems,s=>{const o=s.map((i,c)=>({turn:i,idx:c}));return this._groupItems(o)}),this._groupedVisibleItems=T([this._groupedAllItems,this._startIndex,this._endIndex],([s,o,i])=>{if(s.length===0)return[];let c=0;for(let l=0;l<s.length;l++){const m=s[l];if(m.turns[m.turns.length-1].idx>=o){c=l;break}if(l===s.length-1&&m.turns[m.turns.length-1].idx>=o){c=l;break}}let a=s.length-1;if(i!==void 0)for(let l=s.length-1;l>=0;l--){const m=s[l];if(m.turns[0].idx<=i){a=l;break}if(l===0&&m.turns[0].idx<=i){a=l;break}}return s.slice(c,a+1).map((l,m,g)=>({...l,isLastGroup:m===g.length-1}))}),this.hasMoreBefore=T([this._startIndex],([s])=>s>0),this.hasMoreAfter=T([this._endIndex,this._allItems],([s,o])=>s!==void 0&&s<o.length-1),this.isLoading=T(this._isLoading,s=>s),this.loadingDirection=T(this._loadingDirection,s=>s),this.totalItemCount=T(this._allItems,s=>s.length);const e=q(this._conversationModel);typeof e.onNewConversation=="function"&&this._disposables.push(e.onNewConversation(()=>{this.resetToBottom()})),this.resetToBottom()}subscribe(t){return this._groupedVisibleItems.subscribe(t)}loadMoreBefore(t){const n=q(this._startIndex),e=q(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const s=this._getValidBatchSize(t),o=Math.max(0,n-s);return this._startIndex.set(o),this._isLoading.set(!1),this._loadingDirection.set(null),!0}async loadToStart(t={}){const n=q(this._startIndex),e=q(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const{smooth:s,smoothInterval:o=500}=t;if(s)for(;this.loadMoreBefore();)await new Promise(i=>setTimeout(i,o)),await Et();else this._startIndex.set(0);return this._isLoading.set(!1),this._loadingDirection.set(null),!0}loadMoreAfter(t){const n=q(this._endIndex),e=q(this._isLoading),s=q(this._allItems);if(n===void 0||n>=s.length-1||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("after");const o=this._getValidBatchSize(t),i=Math.min(s.length-1,n+o);return i>=s.length-1?this._endIndex.set(void 0):this._endIndex.set(i),this._isLoading.set(!1),this._loadingDirection.set(null),!0}resetToBottom(){const t=q(this._allItems);if(t.length===0)return;const n=Math.max(0,t.length-this._config.initialVisibleCount);this._startIndex.set(n),this._endIndex.set(void 0)}resetToTop(){const t=q(this._allItems);if(t.length===0)return;const n=Math.min(t.length-1,this._config.initialVisibleCount-1);this._startIndex.set(0),this._endIndex.set(n)}async jumpToMessage(t){const n=q(this._allItems),e=n.findIndex(c=>c.request_id===t);if(e===-1)return!1;const s=Math.floor(this._config.initialVisibleCount/2),o=Math.max(0,e-s),i=e+s>=n.length-5?void 0:Math.min(n.length-1,e+s);return this._startIndex.set(o),this._endIndex.set(i),!0}_groupItems(t){return t.reduce((n,{turn:e,idx:s})=>((e.isToolResult===!0||dt(e)&&ke(e))&&n.length>0||gt(e)&&n.length>0?n[n.length-1].turns.push({turn:e,idx:s}):n.push({turns:[{turn:e,idx:s}],firstRequestId:e.request_id,lastRequestId:e.request_id,isLastGroup:!1}),n),[]).map((n,e,s)=>{const o=n.turns.findLast(({turn:c})=>!!c.request_id),i=o==null?void 0:o.turn.request_id;return{...n,lastRequestId:i,isLastGroup:e===s.length-1}})}setDynamicBatchSize(t){this._currentBatchSize=this._getValidBatchSize(t)}getCurrentBatchSize(){return this._currentBatchSize}_getValidBatchSize(t){if(t===void 0)return this._currentBatchSize;const n=this._config.minBatchSize??this._config.batchSize,e=this._config.maxBatchSize??3*this._config.batchSize;return Math.max(n,Math.min(e,t))}dispose(){this._disposables.forEach(t=>t())}}function je(r){let t,n,e;const s=r[4].default,o=oe(s,r,r[3],null);return{c(){t=N("div"),o&&o.c(),A(t,"class",n="c-gradient-mask "+r[2]+" svelte-say8yn"),Mt(t,"--fade-size",r[1]+"px"),W(t,"is-horizontal",r[0]==="horizontal")},m(i,c){B(i,t,c),o&&o.m(t,null),e=!0},p(i,[c]){o&&o.p&&(!e||8&c)&&ie(o,s,i,i[3],e?le(s,i[3],c,null):ae(i[3]),null),(!e||4&c&&n!==(n="c-gradient-mask "+i[2]+" svelte-say8yn"))&&A(t,"class",n),(!e||2&c)&&Mt(t,"--fade-size",i[1]+"px"),(!e||5&c)&&W(t,"is-horizontal",i[0]==="horizontal")},i(i){e||(h(o,i),e=!0)},o(i){f(o,i),e=!1},d(i){i&&z(t),o&&o.d(i)}}}function We(r,t,n){let{$$slots:e={},$$scope:s}=t,{direction:o="vertical"}=t,{fadeSize:i=Ve}=t,{class:c=""}=t;return r.$$set=a=>{"direction"in a&&n(0,o=a.direction),"fadeSize"in a&&n(1,i=a.fadeSize),"class"in a&&n(2,c=a.class),"$$scope"in a&&n(3,s=a.$$scope)},[o,i,c,s,e]}class Ue extends Gt{constructor(t){super(),Ft(this,t,We,je,Ht,{direction:0,fadeSize:1,class:2})}}function Bt(r,t,n){const e=r.slice();return e[50]=t[n],e[52]=n,e}function zt(r,t,n){const e=r.slice();e[53]=t[n].turn,e[54]=t[n].idx;const s=e[54]+1===e[13].length;return e[55]=s,e}function vt(r){let t,n;return t=new Se({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function qt(r){let t;return{c(){t=N("div"),t.textContent="Loading earlier messages...",A(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){B(n,t,e)},d(n){n&&z(t)}}}function Ye(r){let t,n,e,s;const o=[nn,en],i=[];function c(a,l){return a[19].enableRichCheckpointInfo?0:1}return t=c(r),n=i[t]=o[t](r),{c(){n.c(),e=U()},m(a,l){i[t].m(a,l),B(a,e,l),s=!0},p(a,l){let m=t;t=c(a),t===m?i[t].p(a,l):(V(),f(i[m],1,1,()=>{i[m]=null}),P(),n=i[t],n?n.p(a,l):(n=i[t]=o[t](a),n.c()),h(n,1),n.m(e.parentNode,e))},i(a){s||(h(n),s=!0)},o(a){f(n),s=!1},d(a){a&&z(e),i[t].d(a)}}}function Je(r){let t,n;return t=new Be({props:{group:r[50].turns,chatModel:r[1],turn:r[53],turnIndex:r[54],isLastTurn:r[55],messageListContainer:r[0]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.group=e[50].turns),2&s[0]&&(o.chatModel=e[1]),2097152&s[0]&&(o.turn=e[53]),2097152&s[0]&&(o.turnIndex=e[54]),2105344&s[0]&&(o.isLastTurn=e[55]),1&s[0]&&(o.messageListContainer=e[0]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function Oe(r){let t,n;return t=new ze({props:{stage:r[53].stage,iterationId:r[53].iterationId,stageCount:r[53].stageCount}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.stage=e[53].stage),2097152&s[0]&&(o.iterationId=e[53].iterationId),2097152&s[0]&&(o.stageCount=e[53].stageCount),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function Qe(r){let t,n;return t=new ve({props:{chatModel:r[1],msg:r[53].response_text??""}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2&s[0]&&(o.chatModel=e[1]),2097152&s[0]&&(o.msg=e[53].response_text??""),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function Xe(r){let t,n;return t=new Pe({props:{group:r[50].turns,markdown:r[53].response_text??"",messageListContainer:r[0]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.group=e[50].turns),2097152&s[0]&&(o.markdown=e[53].response_text??""),1&s[0]&&(o.messageListContainer=e[0]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function Ze(r){let t,n;function e(){return r[38](r[53])}return t=new Yt({props:{turn:r[53],preamble:Ee,resendTurn:e,$$slots:{default:[sn]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(s,o){y(t,s,o),n=!0},p(s,o){r=s;const i={};2097152&o[0]&&(i.turn=r[53]),2097156&o[0]&&(i.resendTurn=e),2162688&o[0]|134217728&o[1]&&(i.$$scope={dirty:o,ctx:r}),t.$set(i)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){f(t.$$.fragment,s),n=!1},d(s){M(t,s)}}}function Ke(r){let t,n;return t=new qe({props:{flagsModel:r[14],turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};16384&s[0]&&(o.flagsModel=e[14]),2097152&s[0]&&(o.turn=e[53]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function tn(r){let t,n;return t=new Yt({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.turn=e[53]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function en(r){let t,n;return t=new Re({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.turn=e[53]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function nn(r){let t,n;return t=new De({props:{turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};2097152&s[0]&&(o.turn=e[53]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function sn(r){let t,n;return t=new Ae({props:{conversationModel:r[16],turn:r[53]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};65536&s[0]&&(o.conversationModel=e[16]),2097152&s[0]&&(o.turn=e[53]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function Rt(r){let t,n,e,s;function o(){return r[39](r[53])}return{c(){t=N("div"),A(t,"class","c-msg-list__turn-seen")},m(i,c){B(i,t,c),e||(s=mt(n=xe.call(null,t,{onSeen:o,track:r[53].seen_state!==pt.seen})),e=!0)},p(i,c){r=i,n&&ut(n.update)&&2097152&c[0]&&n.update.call(null,{onSeen:o,track:r[53].seen_state!==pt.seen})},d(i){i&&z(t),e=!1,s()}}}function Dt(r,t){let n,e,s,o,i,c,a,l,m,g,L,S,I,v,D=Ct(t[53]);const b=[tn,Ke,Ze,Xe,Qe,Oe,Je,Ye],_=[];function p($,x){return 2097152&x[0]&&(e=null),2097152&x[0]&&(s=null),2097152&x[0]&&(o=null),2097152&x[0]&&(i=null),2097152&x[0]&&(c=null),2097152&x[0]&&(a=null),2097152&x[0]&&(l=null),2097152&x[0]&&(m=null),e==null&&(e=!!Jt($[53])),e?0:(s==null&&(s=!!Qt($[53])),s?1:(o==null&&(o=!!Xt($[53])),o?2:(i==null&&(i=!!Zt($[53])),i?3:(c==null&&(c=!!Kt($[53])),c?4:(a==null&&(a=!!te($[53])),a?5:(l==null&&(l=!!(dt($[53])||Ot($[53])||ee($[53]))),l?6:(m==null&&(m=!(!gt($[53])||$[53].status!==Te.success)),m?7:-1)))))))}~(g=p(t,[-1,-1]))&&(L=_[g]=b[g](t));let d=D&&Rt(t);return{key:r,first:null,c(){n=U(),L&&L.c(),S=H(),d&&d.c(),I=U(),this.first=n},m($,x){B($,n,x),~g&&_[g].m($,x),B($,S,x),d&&d.m($,x),B($,I,x),v=!0},p($,x){let E=g;g=p(t=$,x),g===E?~g&&_[g].p(t,x):(L&&(V(),f(_[E],1,1,()=>{_[E]=null}),P()),~g?(L=_[g],L?L.p(t,x):(L=_[g]=b[g](t),L.c()),h(L,1),L.m(S.parentNode,S)):L=null),2097152&x[0]&&(D=Ct(t[53])),D?d?d.p(t,x):(d=Rt(t),d.c(),d.m(I.parentNode,I)):d&&(d.d(1),d=null)},i($){v||(h(L),v=!0)},o($){f(L),v=!1},d($){$&&(z(n),z(S),z(I)),~g&&_[g].d($),d&&d.d($)}}}function kt(r){let t,n,e,s;const o=[dn,un,mn,cn,ln,an,on,rn],i=[];function c(a,l){return a[8]?0:a[6].retryMessage?1:a[6].showResumingRemoteAgent?2:a[6].showPaused?3:a[6].showGeneratingResponse?4:a[6].showAwaitingUserInput?5:a[6].showRunningSpacer?6:a[6].showStopped?7:-1}return~(t=c(r))&&(n=i[t]=o[t](r)),{c(){n&&n.c(),e=U()},m(a,l){~t&&i[t].m(a,l),B(a,e,l),s=!0},p(a,l){let m=t;t=c(a),t===m?~t&&i[t].p(a,l):(n&&(V(),f(i[m],1,1,()=>{i[m]=null}),P()),~t?(n=i[t],n?n.p(a,l):(n=i[t]=o[t](a),n.c()),h(n,1),n.m(e.parentNode,e)):n=null)},i(a){s||(h(n),s=!0)},o(a){f(n),s=!1},d(a){a&&z(e),~t&&i[t].d(a)}}}function rn(r){let t,n;return t=new we({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:R,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function on(r){let t;return{c(){t=N("div"),A(t,"class","c-agent-running-spacer svelte-80qwt2")},m(n,e){B(n,t,e)},p:R,i:R,o:R,d(n){n&&z(t)}}}function an(r){let t,n;return t=new ye({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:R,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function ln(r){let t,n;return t=new Me({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:R,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function cn(r){let t,n;return t=new Le({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:R,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function mn(r){let t,n;return t=new be({}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p:R,i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function un(r){let t,n;return t=new Ce({props:{message:r[6].retryMessage}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};64&s[0]&&(o.message=e[6].retryMessage),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function dn(r){let t,n;return t=new Ut({props:{error:r[8].error,onRetry:r[8].onRetry,onDelete:r[8].onDelete}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};256&s[0]&&(o.error=e[8].error),256&s[0]&&(o.onRetry=e[8].onRetry),256&s[0]&&(o.onDelete=e[8].onDelete),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function gn(r){let t,n,e,s=[],o=new Map,i=tt(r[50].turns);const c=l=>l[53].request_id??`no-request-id-${l[54]}`;for(let l=0;l<i.length;l+=1){let m=zt(r,i,l),g=c(m);o.set(g,s[l]=Dt(g,m))}let a=r[50].isLastGroup&&kt(r);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();t=H(),a&&a.c(),n=U()},m(l,m){for(let g=0;g<s.length;g+=1)s[g]&&s[g].m(l,m);B(l,t,m),a&&a.m(l,m),B(l,n,m),e=!0},p(l,m){2711559&m[0]|2&m[1]&&(i=tt(l[50].turns),V(),s=jt(s,m,c,1,l,i,o,t.parentNode,Wt,Dt,t,zt),P()),l[50].isLastGroup?a?(a.p(l,m),2097152&m[0]&&h(a,1)):(a=kt(l),a.c(),h(a,1),a.m(n.parentNode,n)):a&&(V(),f(a,1,1,()=>{a=null}),P())},i(l){if(!e){for(let m=0;m<i.length;m+=1)h(s[m]);h(a),e=!0}},o(l){for(let m=0;m<s.length;m+=1)f(s[m]);f(a),e=!1},d(l){l&&(z(t),z(n));for(let m=0;m<s.length;m+=1)s[m].d(l);a&&a.d(l)}}}function Tt(r,t){let n,e,s;return e=new Ie({props:{class:"c-msg-list__item--grouped",chatModel:t[1],isLastItem:t[50].isLastGroup,userControlsScroll:t[7],requestId:t[50].firstRequestId,releaseScroll:t[40],messageListContainer:t[0],minHeight:t[50].isLastGroup?t[12]:0,dataRequestId:t[50].firstRequestId,$$slots:{default:[gn]},$$scope:{ctx:t}}}),{key:r,first:null,c(){n=U(),w(e.$$.fragment),this.first=n},m(o,i){B(o,n,i),y(e,o,i),s=!0},p(o,i){t=o;const c={};2&i[0]&&(c.chatModel=t[1]),2097152&i[0]&&(c.isLastItem=t[50].isLastGroup),128&i[0]&&(c.userControlsScroll=t[7]),2097152&i[0]&&(c.requestId=t[50].firstRequestId),128&i[0]&&(c.releaseScroll=t[40]),1&i[0]&&(c.messageListContainer=t[0]),2101248&i[0]&&(c.minHeight=t[50].isLastGroup?t[12]:0),2097152&i[0]&&(c.dataRequestId=t[50].firstRequestId),2711879&i[0]|134217728&i[1]&&(c.$$scope={dirty:i,ctx:t}),e.$set(c)},i(o){s||(h(e.$$.fragment,o),s=!0)},o(o){f(e.$$.fragment,o),s=!1},d(o){o&&z(n),M(e,o)}}}function Vt(r){let t;return{c(){t=N("div"),t.textContent="Loading more messages...",A(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){B(n,t,e)},d(n){n&&z(t)}}}function Pt(r){let t,n;return t=new Ut({props:{error:r[8].error,onRetry:r[8].onRetry,onDelete:r[8].onDelete}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};256&s[0]&&(o.error=e[8].error),256&s[0]&&(o.onRetry=e[8].onRetry),256&s[0]&&(o.onDelete=e[8].onDelete),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function pn(r){let t,n,e,s,o,i,c,a,l,m,g=[],L=new Map,S=r[9]&&vt(),I=r[18]&&r[20]==="before"&&qt(),v=tt(r[21]);const D=p=>p[50].firstRequestId??`no-request-id-${p[52]}`;for(let p=0;p<v.length;p+=1){let d=Bt(r,v,p),$=D(d);L.set($,g[p]=Tt($,d))}let b=r[18]&&r[20]==="after"&&Vt(),_=!r[13].length&&r[8]&&Pt(r);return{c(){t=N("div"),S&&S.c(),n=H(),I&&I.c(),e=H();for(let p=0;p<g.length;p+=1)g[p].c();s=H(),b&&b.c(),o=H(),_&&_.c(),A(t,"class","c-msg-list svelte-80qwt2"),W(t,"c-msg-list--minimal",!r[19].fullFeatured)},m(p,d){B(p,t,d),S&&S.m(t,null),O(t,n),I&&I.m(t,null),O(t,e);for(let $=0;$<g.length;$+=1)g[$]&&g[$].m(t,null);O(t,s),b&&b.m(t,null),O(t,o),_&&_.m(t,null),r[41](t),a=!0,l||(m=[mt(i=pe.call(null,t,{onScrollIntoBottom:r[42],onScrollAwayFromBottom:r[43],onScroll:r[44]})),mt(c=he.call(null,t,{onHeightChange:r[45]}))],l=!0)},p(p,d){p[9]?S?512&d[0]&&h(S,1):(S=vt(),S.c(),h(S,1),S.m(t,n)):S&&(V(),f(S,1,1,()=>{S=null}),P()),p[18]&&p[20]==="before"?I||(I=qt(),I.c(),I.m(t,e)):I&&(I.d(1),I=null),2716103&d[0]|2&d[1]&&(v=tt(p[21]),V(),g=jt(g,d,D,1,p,v,L,t,Wt,Tt,s,Bt),P()),p[18]&&p[20]==="after"?b||(b=Vt(),b.c(),b.m(t,o)):b&&(b.d(1),b=null),!p[13].length&&p[8]?_?(_.p(p,d),8448&d[0]&&h(_,1)):(_=Pt(p),_.c(),h(_,1),_.m(t,null)):_&&(V(),f(_,1,1,()=>{_=null}),P()),i&&ut(i.update)&&131233&d[0]&&i.update.call(null,{onScrollIntoBottom:p[42],onScrollAwayFromBottom:p[43],onScroll:p[44]}),c&&ut(c.update)&&16&d[0]&&c.update.call(null,{onHeightChange:p[45]}),(!a||524288&d[0])&&W(t,"c-msg-list--minimal",!p[19].fullFeatured)},i(p){if(!a){h(S);for(let d=0;d<v.length;d+=1)h(g[d]);h(_),a=!0}},o(p){f(S);for(let d=0;d<g.length;d+=1)f(g[d]);f(_),a=!1},d(p){p&&z(t),S&&S.d(),I&&I.d();for(let d=0;d<g.length;d+=1)g[d].d();b&&b.d(),_&&_.d(),r[41](null),l=!1,Nt(m)}}}function At(r){let t,n;return t=new fe({props:{messageListElement:r[0],showScrollDown:r[11]}}),{c(){w(t.$$.fragment)},m(e,s){y(t,e,s),n=!0},p(e,s){const o={};1&s[0]&&(o.messageListElement=e[0]),2048&s[0]&&(o.showScrollDown=e[11]),t.$set(o)},i(e){n||(h(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){M(t,e)}}}function hn(r){let t,n,e,s,o,i;n=new Ue({props:{$$slots:{default:[pn]},$$scope:{ctx:r}}});let c=r[10]&&At(r);return{c(){t=N("div"),w(n.$$.fragment),e=H(),c&&c.c(),A(t,"class","c-msg-list-container svelte-80qwt2"),A(t,"data-testid","chat-message-list"),W(t,"c-msg-list--minimal",!r[19].fullFeatured)},m(a,l){B(a,t,l),y(n,t,null),O(t,e),c&&c.m(t,null),s=!0,o||(i=[Lt(t,"mouseenter",r[46]),Lt(t,"mouseleave",r[47])],o=!0)},p(a,l){const m={};4158455&l[0]|134217728&l[1]&&(m.$$scope={dirty:l,ctx:a}),n.$set(m),a[10]?c?(c.p(a,l),1024&l[0]&&h(c,1)):(c=At(a),c.c(),h(c,1),c.m(t,null)):c&&(V(),f(c,1,1,()=>{c=null}),P()),(!s||524288&l[0])&&W(t,"c-msg-list--minimal",!a[19].fullFeatured)},i(a){s||(h(n.$$.fragment,a),h(c),s=!0)},o(a){f(n.$$.fragment,a),f(c),s=!1},d(a){a&&z(t),M(n),c&&c.d(),o=!1,Nt(i)}}}function fn(r,t,n){let e,s,o,i,c,a,l,m,g,L,S,I,v,D,b,_,p,d,$,x,E=R,et=R,ht=()=>(et(),et=ct(Y,u=>n(37,p=u)),Y),nt=R;r.$$.on_destroy.push(()=>E()),r.$$.on_destroy.push(()=>et()),r.$$.on_destroy.push(()=>nt());let{chatModel:Y}=t;ht();let{onboardingWorkspaceModel:st}=t,{msgListElement:k}=t;const ne=bt("agentConversationModel"),{agentExchangeStatus:ft,isCurrConversationAgentic:$t}=ne;F(r,ft,u=>n(36,_=u)),F(r,$t,u=>n(35,b=u));const _t=bt(Ge.key);F(r,_t,u=>n(34,D=u));const G=new Ne(T(Y,u=>u.currentConversationModel),{initialVisibleCount:20,batchSize:10,minBatchSize:5,maxBatchSize:20});F(r,G,u=>n(21,x=u));const{hasMoreBefore:St,isLoading:It,loadingDirection:xt}=G;F(r,St,u=>n(17,I=u)),F(r,It,u=>n(18,v=u)),F(r,xt,u=>n(20,$=u));const J=new Fe(3),wt=new He;ce(()=>{G.dispose()});let Q=!1,X=!1;function j(){n(7,Q=!0)}async function rt(){if(!k||!I||v)return;j();const u=wt.captureScrollPosition(k),it=J.getVelocityPPS(),at=$e(it,G.getCurrentBatchSize()),lt=G.loadMoreBefore(at);u&&(j(),wt.restoreScrollPosition(k,u),await Et()),lt&&k&&k.scrollTop<=1&&I&&rt()}me(()=>{var u;((u=S.lastExchange)==null?void 0:u.seen_state)===pt.unseen&&j()});let ot=0,Z=!0;const yt=u=>S.markSeen(u);return r.$$set=u=>{"chatModel"in u&&ht(n(1,Y=u.chatModel)),"onboardingWorkspaceModel"in u&&n(2,st=u.onboardingWorkspaceModel),"msgListElement"in u&&n(0,k=u.msgListElement)},r.$$.update=()=>{64&r.$$.dirty[1]&&(n(15,e=p.currentConversationModel),E(),E=ct(e,u=>n(16,S=u))),64&r.$$.dirty[1]&&(n(14,s=p.flags),nt(),nt=ct(s,u=>n(19,d=u))),120&r.$$.dirty[1]&&n(33,o=ge(p,_,b,D)),4&r.$$.dirty[1]&&n(13,i=o.chatHistory),16&r.$$.dirty[0]&&n(12,c=ot),40&r.$$.dirty[0]&&n(11,a=!Z&&X),4&r.$$.dirty[1]&&n(6,l=o.lastGroupConfig),4&r.$$.dirty[1]&&n(10,m=o.doShowFloatingButtons),4&r.$$.dirty[1]&&n(9,g=o.doShowAgentSetupLogs),64&r.$$.dirty[0]&&n(8,L=l.remoteAgentErrorConfig)},[k,Y,st,X,ot,Z,l,Q,L,g,m,a,c,i,s,e,S,I,v,d,$,x,ft,$t,_t,G,St,It,xt,J,j,rt,yt,o,D,b,_,p,u=>st.retryProjectSummary(u),u=>yt(u),()=>n(7,Q=!0),function(u){ue[u?"unshift":"push"](()=>{k=u,n(0,k)})},()=>{n(7,Q=!1),n(5,Z=!0),G.resetToBottom()},()=>{j(),n(5,Z=!1)},u=>{if(u<=1&&j(),k){J.addSample(u);const it=J.getVelocityPPS(),at=J.getDirection(),lt=_e(it,{baseThreshold:200,predictTime:300});at==="up"&&u<lt&&I&&rt()}},u=>n(4,ot=u),()=>n(3,X=!0),()=>n(3,X=!1)]}class ws extends Gt{constructor(t){super(),Ft(this,t,fn,hn,Ht,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{ws as default};
