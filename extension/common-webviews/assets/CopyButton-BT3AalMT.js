import{S as X,i as Y,s as oo,C as so,D as lo,W as j,E as b,c as P,e as O,F as I,G as ro,u as d,t as m,h as T,I as S,Z as J,_ as eo,a as D,j as ao,A as no,q as A,r as B,ab as C,g as to,a4 as io,$ as h,a0 as k,a1 as v,a2 as x,J as W,n as co}from"./SpinnerAugment-CQKp6jSN.js";import{C as uo}from"./magnifying-glass-CXSTSDWT.js";import"./BaseButton-ESlFPUk1.js";import{T as po}from"./Content-D7Q35t53.js";import{B as $o}from"./ButtonAugment-CAn8LxGl.js";import{I as fo}from"./IconButtonAugment-D-fvrWAT.js";import{T as mo}from"./TextTooltipAugment--NM_J2iY.js";import{C as Co}from"./check-DlU29TPV.js";const go=t=>({}),Z=t=>({}),yo=t=>({}),_=t=>({slot:"iconRight"}),ho=t=>({}),K=t=>({}),ko=t=>({}),M=t=>({});function vo(t){let o,e;const n=[t[8],{color:t[4]},{variant:t[7]}];let i={$$slots:{iconRight:[bo],iconLeft:[To],default:[wo]},$$scope:{ctx:t}};for(let c=0;c<n.length;c+=1)i=D(i,n[c]);return o=new $o({props:i}),o.$on("click",t[10]),o.$on("keyup",t[29]),o.$on("keydown",t[30]),o.$on("mousedown",t[31]),o.$on("mouseover",t[32]),o.$on("focus",t[33]),o.$on("mouseleave",t[34]),o.$on("blur",t[35]),o.$on("contextmenu",t[36]),{c(){b(o.$$.fragment)},m(c,s){I(o,c,s),e=!0},p(c,s){const a=400&s[0]?to(n,[256&s[0]&&io(c[8]),16&s[0]&&{color:c[4]},128&s[0]&&{variant:c[7]}]):{};2058&s[0]|128&s[1]&&(a.$$scope={dirty:s,ctx:c}),o.$set(a)},i(c){e||(d(o.$$.fragment,c),e=!0)},o(c){m(o.$$.fragment,c),e=!1},d(c){S(o,c)}}}function xo(t){let o,e;const n=[t[8],{color:t[4]},{variant:t[7]}];let i={$$slots:{default:[Io]},$$scope:{ctx:t}};for(let c=0;c<n.length;c+=1)i=D(i,n[c]);return o=new fo({props:i}),o.$on("click",t[10]),o.$on("keyup",t[21]),o.$on("keydown",t[22]),o.$on("mousedown",t[23]),o.$on("mouseover",t[24]),o.$on("focus",t[25]),o.$on("mouseleave",t[26]),o.$on("blur",t[27]),o.$on("contextmenu",t[28]),{c(){b(o.$$.fragment)},m(c,s){I(o,c,s),e=!0},p(c,s){const a=400&s[0]?to(n,[256&s[0]&&io(c[8]),16&s[0]&&{color:c[4]},128&s[0]&&{variant:c[7]}]):{};128&s[1]&&(a.$$scope={dirty:s,ctx:c}),o.$set(a)},i(c){e||(d(o.$$.fragment,c),e=!0)},o(c){m(o.$$.fragment,c),e=!1},d(c){S(o,c)}}}function wo(t){let o;const e=t[20].default,n=h(e,t,t[38],null);return{c(){n&&n.c()},m(i,c){n&&n.m(i,c),o=!0},p(i,c){n&&n.p&&(!o||128&c[1])&&k(n,e,i,i[38],o?x(e,i[38],c,null):v(i[38]),null)},i(i){o||(d(n,i),o=!0)},o(i){m(n,i),o=!1},d(i){n&&n.d(i)}}}function No(t){let o;const e=t[20].iconLeft,n=h(e,t,t[38],Z);return{c(){n&&n.c()},m(i,c){n&&n.m(i,c),o=!0},p(i,c){n&&n.p&&(!o||128&c[1])&&k(n,e,i,i[38],o?x(e,i[38],c,go):v(i[38]),Z)},i(i){o||(d(n,i),o=!0)},o(i){m(n,i),o=!1},d(i){n&&n.d(i)}}}function Oo(t){let o,e;return o=new Co({}),{c(){b(o.$$.fragment)},m(n,i){I(o,n,i),e=!0},p:co,i(n){e||(d(o.$$.fragment,n),e=!0)},o(n){m(o.$$.fragment,n),e=!1},d(n){S(o,n)}}}function To(t){let o,e,n,i;const c=[Oo,No],s=[];function a(l,p){return l[11].iconLeft&&l[3]==="success"&&l[1]?0:1}return e=a(t),n=s[e]=c[e](t),{c(){o=j("svelte.fragment"),n.c(),P(o,"slot","iconLeft")},m(l,p){O(l,o,p),s[e].m(o,null),i=!0},p(l,p){let u=e;e=a(l),e===u?s[e].p(l,p):(A(),m(s[u],1,1,()=>{s[u]=null}),B(),n=s[e],n?n.p(l,p):(n=s[e]=c[e](l),n.c()),d(n,1),n.m(o,null))},i(l){i||(d(n),i=!0)},o(l){m(n),i=!1},d(l){l&&T(o),s[e].d()}}}function bo(t){let o;const e=t[20].iconRight,n=h(e,t,t[38],_);return{c(){n&&n.c()},m(i,c){n&&n.m(i,c),o=!0},p(i,c){n&&n.p&&(!o||128&c[1])&&k(n,e,i,i[38],o?x(e,i[38],c,yo):v(i[38]),_)},i(i){o||(d(n,i),o=!0)},o(i){m(n,i),o=!1},d(i){n&&n.d(i)}}}function Io(t){let o,e,n;const i=t[20].iconLeft,c=h(i,t,t[38],M),s=t[20].default,a=h(s,t,t[38],null),l=t[20].iconRight,p=h(l,t,t[38],K);return{c(){c&&c.c(),o=W(),a&&a.c(),e=W(),p&&p.c()},m(u,f){c&&c.m(u,f),O(u,o,f),a&&a.m(u,f),O(u,e,f),p&&p.m(u,f),n=!0},p(u,f){c&&c.p&&(!n||128&f[1])&&k(c,i,u,u[38],n?x(i,u[38],f,ko):v(u[38]),M),a&&a.p&&(!n||128&f[1])&&k(a,s,u,u[38],n?x(s,u[38],f,null):v(u[38]),null),p&&p.p&&(!n||128&f[1])&&k(p,l,u,u[38],n?x(l,u[38],f,ho):v(u[38]),K)},i(u){n||(d(c,u),d(a,u),d(p,u),n=!0)},o(u){m(c,u),m(a,u),m(p,u),n=!1},d(u){u&&(T(o),T(e)),c&&c.d(u),a&&a.d(u),p&&p.d(u)}}}function So(t){let o,e,n,i;const c=[xo,vo],s=[];function a(l,p){return l[0]?0:1}return o=a(t),e=s[o]=c[o](t),{c(){e.c(),n=no()},m(l,p){s[o].m(l,p),O(l,n,p),i=!0},p(l,p){let u=o;o=a(l),o===u?s[o].p(l,p):(A(),m(s[u],1,1,()=>{s[u]=null}),B(),e=s[o],e?e.p(l,p):(e=s[o]=c[o](l),e.c()),d(e,1),e.m(n.parentNode,n))},i(l){i||(d(e),i=!0)},o(l){m(e),i=!1},d(l){l&&T(n),s[o].d(l)}}}function zo(t){let o,e,n,i;function c(a){t[37](a)}let s={onOpenChange:t[9],content:t[6],triggerOn:[po.Hover],nested:t[2],$$slots:{default:[So]},$$scope:{ctx:t}};return t[5]!==void 0&&(s.requestClose=t[5]),e=new mo({props:s}),so.push(()=>lo(e,"requestClose",c)),{c(){o=j("div"),b(e.$$.fragment),P(o,"class","c-successful-button svelte-1dvyzw2")},m(a,l){O(a,o,l),I(e,o,null),i=!0},p(a,l){const p={};64&l[0]&&(p.content=a[6]),4&l[0]&&(p.nested=a[2]),2459&l[0]|128&l[1]&&(p.$$scope={dirty:l,ctx:a}),!n&&32&l[0]&&(n=!0,p.requestClose=a[5],ro(()=>n=!1)),e.$set(p)},i(a){i||(d(e.$$.fragment,a),i=!0)},o(a){m(e.$$.fragment,a),i=!1},d(a){a&&T(o),S(e)}}}function Do(t,o,e){let n,i,c;const s=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","replaceIconOnSuccess","tooltipNested"];let a=J(o,s),{$$slots:l={},$$scope:p}=o;const u=eo(l);let f,w,{defaultColor:N}=o,{tooltip:g}=o,{stateVariant:$}=o,{onClick:L}=o,{tooltipDuration:V=1500}=o,{icon:E=!1}=o,{stickyColor:z=!0}=o,{persistOnTooltipClose:q=!1}=o,{replaceIconOnSuccess:F=!1}=o,{tooltipNested:G}=o,y="neutral",H=N,R=g==null?void 0:g.neutral;return t.$$set=r=>{o=D(D({},o),ao(r)),e(40,a=J(o,s)),"defaultColor"in r&&e(12,N=r.defaultColor),"tooltip"in r&&e(13,g=r.tooltip),"stateVariant"in r&&e(14,$=r.stateVariant),"onClick"in r&&e(15,L=r.onClick),"tooltipDuration"in r&&e(16,V=r.tooltipDuration),"icon"in r&&e(0,E=r.icon),"stickyColor"in r&&e(17,z=r.stickyColor),"persistOnTooltipClose"in r&&e(18,q=r.persistOnTooltipClose),"replaceIconOnSuccess"in r&&e(1,F=r.replaceIconOnSuccess),"tooltipNested"in r&&e(2,G=r.tooltipNested),"$$scope"in r&&e(38,p=r.$$scope)},t.$$.update=()=>{e(19,{variant:n,...i}=a,n,(e(8,i),e(40,a))),540680&t.$$.dirty[0]&&e(7,c=($==null?void 0:$[y])??n),4104&t.$$.dirty[0]&&e(4,H=y==="success"?"success":y==="failure"?"error":N)},[E,F,G,y,H,f,R,c,i,function(r){q||r||(clearTimeout(w),w=void 0,e(6,R=g==null?void 0:g.neutral),z||e(3,y="neutral"))},async function(r){try{e(3,y=await L(r)??"neutral")}catch{e(3,y="failure")}e(6,R=g==null?void 0:g[y]),clearTimeout(w),w=setTimeout(()=>{f==null||f(),z||e(3,y="neutral")},V)},u,N,g,$,L,V,z,q,n,l,function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){C.call(this,t,r)},function(r){f=r,e(5,f)},p]}class Lo extends X{constructor(o){super(),Y(this,o,Do,zo,oo,{defaultColor:12,tooltip:13,stateVariant:14,onClick:15,tooltipDuration:16,icon:0,stickyColor:17,persistOnTooltipClose:18,replaceIconOnSuccess:1,tooltipNested:2},null,[-1,-1])}}const Vo=t=>({}),Q=t=>({}),qo=t=>({}),U=t=>({});function Ro(t){let o;const e=t[10].text,n=h(e,t,t[11],Q);return{c(){n&&n.c()},m(i,c){n&&n.m(i,c),o=!0},p(i,c){n&&n.p&&(!o||2048&c)&&k(n,e,i,i[11],o?x(e,i[11],c,Vo):v(i[11]),Q)},i(i){o||(d(n,i),o=!0)},o(i){m(n,i),o=!1},d(i){n&&n.d(i)}}}function jo(t){let o,e;return o=new uo({}),{c(){b(o.$$.fragment)},m(n,i){I(o,n,i),e=!0},p:co,i(n){e||(d(o.$$.fragment,n),e=!0)},o(n){m(o.$$.fragment,n),e=!1},d(n){S(o,n)}}}function Po(t){let o;const e=t[10].icon,n=h(e,t,t[11],U);return{c(){n&&n.c()},m(i,c){n&&n.m(i,c),o=!0},p(i,c){n&&n.p&&(!o||2048&c)&&k(n,e,i,i[11],o?x(e,i[11],c,qo):v(i[11]),U)},i(i){o||(d(n,i),o=!0)},o(i){m(n,i),o=!1},d(i){n&&n.d(i)}}}function Ao(t){let o,e,n,i;const c=[Po,jo],s=[];function a(l,p){return l[8].icon?0:1}return o=a(t),e=s[o]=c[o](t),{c(){e.c(),n=no()},m(l,p){s[o].m(l,p),O(l,n,p),i=!0},p(l,p){let u=o;o=a(l),o===u?s[o].p(l,p):(A(),m(s[u],1,1,()=>{s[u]=null}),B(),e=s[o],e?e.p(l,p):(e=s[o]=c[o](l),e.c()),d(e,1),e.m(n.parentNode,n))},i(l){i||(d(e),i=!0)},o(l){m(e),i=!1},d(l){l&&T(n),s[o].d(l)}}}function Bo(t){let o,e,n;return e=new Lo({props:{defaultColor:t[2],size:t[0],variant:t[1],loading:t[7],stickyColor:t[4],tooltip:{neutral:t[3],success:"Copied!"},stateVariant:{success:"soft"},onClick:t[5],icon:!t[8].text,tooltipNested:t[6],$$slots:{iconLeft:[Ao],default:[Ro]},$$scope:{ctx:t}}}),{c(){o=j("span"),b(e.$$.fragment),P(o,"class","c-copy-button svelte-tq93gm")},m(i,c){O(i,o,c),I(e,o,null),n=!0},p(i,[c]){const s={};4&c&&(s.defaultColor=i[2]),1&c&&(s.size=i[0]),2&c&&(s.variant=i[1]),128&c&&(s.loading=i[7]),16&c&&(s.stickyColor=i[4]),8&c&&(s.tooltip={neutral:i[3],success:"Copied!"}),32&c&&(s.onClick=i[5]),256&c&&(s.icon=!i[8].text),64&c&&(s.tooltipNested=i[6]),2304&c&&(s.$$scope={dirty:c,ctx:i}),e.$set(s)},i(i){n||(d(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&T(o),S(e)}}}function Eo(t,o){return new Promise(e=>setTimeout(e,t,o))}function Fo(t,o,e){let{$$slots:n={},$$scope:i}=o;const c=eo(n);let{size:s=1}=o,{variant:a="ghost-block"}=o,{color:l="neutral"}=o,{text:p}=o,{tooltip:u="Copy"}=o,{stickyColor:f=!1}=o,{onCopy:w=async()=>{if(p!==void 0){e(7,g=!0);try{await Promise.all([navigator.clipboard.writeText(typeof p=="string"?p:await p()),Eo(500)])}finally{e(7,g=!1)}return"success"}}}=o,{tooltipNested:N}=o,g=!1;return t.$$set=$=>{"size"in $&&e(0,s=$.size),"variant"in $&&e(1,a=$.variant),"color"in $&&e(2,l=$.color),"text"in $&&e(9,p=$.text),"tooltip"in $&&e(3,u=$.tooltip),"stickyColor"in $&&e(4,f=$.stickyColor),"onCopy"in $&&e(5,w=$.onCopy),"tooltipNested"in $&&e(6,N=$.tooltipNested),"$$scope"in $&&e(11,i=$.$$scope)},[s,a,l,u,f,w,N,g,c,p,n,i]}class Uo extends X{constructor(o){super(),Y(this,o,Fo,Bo,oo,{size:0,variant:1,color:2,text:9,tooltip:3,stickyColor:4,onCopy:5,tooltipNested:6})}}export{Uo as C,Lo as S};
