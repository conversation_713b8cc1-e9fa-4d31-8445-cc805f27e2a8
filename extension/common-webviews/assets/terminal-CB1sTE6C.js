import{S as c,i as f,s as h,b as n,c as l,e as p,f as s,n as C,h as v}from"./SpinnerAugment-CQKp6jSN.js";function w(u){let e,r,o,t,i;return{c(){e=n("svg"),r=n("rect"),o=n("path"),t=n("path"),i=n("path"),l(r,"width","16"),l(r,"height","16"),l(r,"fill","currentColor"),l(r,"fill-opacity","0.01"),l(o,"fill-rule","evenodd"),l(o,"clip-rule","evenodd"),l(o,"d","M3.4718 3.46066C3.65925 3.2732 3.96317 3.2732 4.15062 3.46066L7.35062 6.66066C7.44064 6.75068 7.49121 6.87277 7.49121 7.00007C7.49121 7.12737 7.44064 7.24946 7.35062 7.33949L4.15062 10.5395C3.96317 10.7269 3.65925 10.7269 3.4718 10.5395C3.28435 10.352 3.28435 10.0481 3.4718 9.86067L6.33239 7.00007L3.4718 4.13949C3.28435 3.95203 3.28435 3.64812 3.4718 3.46066Z"),l(o,"fill","currentColor"),l(t,"fill-rule","evenodd"),l(t,"clip-rule","evenodd"),l(t,"d","M7.86854 10.6132C7.57399 10.6132 7.33521 10.8519 7.33521 11.1465C7.33521 11.441 7.57399 11.6798 7.86854 11.6798H12.1352C12.4298 11.6798 12.6685 11.441 12.6685 11.1465C12.6685 10.8519 12.4298 10.6132 12.1352 10.6132H7.86854Z"),l(t,"fill","currentColor"),l(i,"fill-rule","evenodd"),l(i,"clip-rule","evenodd"),l(i,"d","M2.13331 1.06665C1.5442 1.06665 1.06664 1.54421 1.06664 2.13332V13.8667C1.06664 14.4558 1.5442 14.9333 2.13331 14.9333H13.8667C14.4558 14.9333 14.9333 14.4558 14.9333 13.8667V2.13332C14.9333 1.54421 14.4558 1.06665 13.8667 1.06665H2.13331ZM2.13331 2.13332H13.8667V13.8667H2.13331V2.13332Z"),l(i,"fill","currentColor"),l(e,"width","16"),l(e,"height","16"),l(e,"viewBox","0 0 16 16"),l(e,"fill","none"),l(e,"xmlns","http://www.w3.org/2000/svg")},m(d,a){p(d,e,a),s(e,r),s(e,o),s(e,t),s(e,i)},p:C,i:C,o:C,d(d){d&&v(e)}}}class g extends c{constructor(e){super(),f(this,e,null,w,h,{})}}export{g as T};
