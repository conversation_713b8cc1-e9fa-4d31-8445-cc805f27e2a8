<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Autofix</title>
    <script nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <script type="module" crossorigin src="./assets/autofix-DSZEqdI8.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CQKp6jSN.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-ESlFPUk1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CAn8LxGl.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-CBQUAmpN.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-D-fvrWAT.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-QXTl3raU.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CONIBlZ6.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/resize-observer-DdAtcrRr.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4SxYn1J.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-csTQmXPq.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BYlJR0wA.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/file-reader-CGvBXfr1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/file-reader-BZAZY_XQ.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/autofix-Dr7nxBLG.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
