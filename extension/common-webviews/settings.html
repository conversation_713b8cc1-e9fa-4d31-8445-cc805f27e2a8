<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <script type="module" crossorigin src="./assets/settings-VnjkptXG.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CQKp6jSN.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-ESlFPUk1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-D-fvrWAT.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/trash-can-YBGqp3xH.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-D-hN7xfd.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/Content-D7Q35t53.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment--NM_J2iY.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/github-CwfQWdpa.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/mcp-logo-DrfFqzDb.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/magnifying-glass-CXSTSDWT.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CONIBlZ6.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-DOHETl9Q.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/types-DwxhLPcD.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-CMtlLYew.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/resize-observer-DdAtcrRr.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-DA5Ek1es.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-DUeUh8Wh.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CAn8LxGl.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-KjDsYzQv.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-DvKVcjj3.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-ZPisEIAt.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CIi2Dx1U.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-b9B2aQlO.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment--cD032dy.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4SxYn1J.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/index-csTQmXPq.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-8faY21Ia.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-B_cAiW03.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/arrow-up-right-from-square-ChzPb9WB.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-BhsWla-l.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="modulepreload" crossorigin href="./assets/RulesDropdown-Yzl2DW0K.js" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/trash-can-Df_FYENN.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-Be7obQsv.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-DwFbLE28.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/index-CLSyEK0S.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-Dvw-pMXw.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-CLLTFP8m.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-zNvUkrOp.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BuBr3xQZ.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
    <link rel="stylesheet" crossorigin href="./assets/settings-C9rpU2Sn.css" nonce="nonce-dbAIn9Xj7LVD6xYC99331A==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
